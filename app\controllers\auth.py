from datetime import datetime
import os
from flask import Blueprint, render_template, request, redirect, url_for, flash, session, jsonify, current_app, send_file
from flask_login import login_user, logout_user, login_required, current_user
from werkzeug.urls import url_parse
from app import db
from app.models.user import User
from app.utils.excel_processor import ExcelUserProcessor

auth_bp = Blueprint('auth', __name__)

@auth_bp.route('/login', methods=['GET', 'POST'])
def login():
    """Kullanıcı girişi"""
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))
    
    if request.method == 'POST':
        email = request.form.get('email', '').strip().lower()
        password = request.form.get('password', '')
        remember_me = bool(request.form.get('remember_me'))
        
        if not email or not password:
            flash('Email ve şifre alanları zorunludur.', 'error')
            return render_template('login.html')
        
        user = User.query.filter_by(email=email).first()
        
        if user and user.check_password(password):
            if not user.aktif_mi:
                flash('Hesabınız deaktif durumda. Lütfen yöneticinizle iletişime geçin.', 'error')
                return render_template('login.html')
            
            # Son giriş tarihini güncelle
            user.son_giris_tarihi = datetime.utcnow()
            db.session.commit()
            
            login_user(user, remember=remember_me)
            
            # Kullanıcıyı istediği sayfaya yönlendir
            next_page = request.args.get('next')
            if not next_page or url_parse(next_page).netloc != '':
                next_page = url_for('dashboard')
            
            flash(f'Hoş geldiniz, {user.tam_ad}!', 'success')
            return redirect(next_page)
        else:
            flash('Geçersiz email veya şifre.', 'error')
    
    return render_template('login.html')

@auth_bp.route('/logout')
@login_required
def logout():
    """Kullanıcı çıkışı"""
    logout_user()
    flash('Başarıyla çıkış yaptınız.', 'info')
    return redirect(url_for('auth.login'))

@auth_bp.route('/register', methods=['GET', 'POST'])
def register():
    """Kullanıcı kaydı (sadece admin tarafından)"""
    if not current_user.is_authenticated or not current_user.is_admin:
        flash('Bu sayfaya erişim yetkiniz yok.', 'error')
        return redirect(url_for('auth.login'))

    # Yöneticileri getir (manager ve admin rolündeki kullanıcılar)
    managers = User.query.filter(
        User.rol.in_(['manager', 'admin']),
        User.aktif_mi == True
    ).order_by(User.ad, User.soyad).all()

    # Şirketleri getir
    from app.models.company import Company
    companies = Company.get_active_companies()

    if request.method == 'POST':
        ad = request.form.get('ad', '').strip()
        soyad = request.form.get('soyad', '').strip()
        email = request.form.get('email', '').strip().lower()
        telefon_numarasi = request.form.get('telefon_numarasi', '').strip()
        password = request.form.get('password', '')
        rol = request.form.get('rol', 'participant')
        manager_id = request.form.get('manager_id', '') or None
        
        # Company ID validasyonu
        if company_id:
            try:
                company_id = int(company_id)
                company = Company.query.get(company_id)
                if not company or not company.aktif_mi:
                    flash('Geçersiz şirket seçimi.', 'error')
                    return render_template('register.html', managers=managers, companies=companies)
            except ValueError:
                company_id = None

        # Manager ID validasyonu
        if manager_id:
            try:
                manager_id = int(manager_id)
                manager = User.query.get(manager_id)
                if not manager or manager.rol not in ['manager', 'admin']:
                    flash('Geçersiz yönetici seçimi.', 'error')
                    return render_template('register.html', managers=managers, companies=companies)
                # Manager aynı şirkette olmalı
                if company_id and manager.company_id != company_id:
                    flash('Yönetici seçilen şirkette çalışmalıdır.', 'error')
                    return render_template('register.html', managers=managers, companies=companies)
            except ValueError:
                manager_id = None

        # Validasyon
        if not all([ad, soyad, email, telefon_numarasi, password]):
            flash('Tüm alanlar zorunludur.', 'error')
            return render_template('register.html', managers=managers)
        
        if len(password) < 6:
            flash('Şifre en az 6 karakter olmalıdır.', 'error')
            return render_template('register.html', managers=managers)

        if rol not in ['participant', 'manager', 'admin']:
            flash('Geçersiz rol seçimi.', 'error')
            return render_template('register.html', managers=managers)

        # Email kontrolü
        if User.query.filter_by(email=email).first():
            flash('Bu email adresi zaten kullanılıyor.', 'error')
            return render_template('register.html', managers=managers)
        
        # Yeni kullanıcı oluştur
        user = User(
            ad=ad,
            soyad=soyad,
            email=email,
            telefon_numarasi=telefon_numarasi,
            rol=rol,
            manager_id=manager_id,
            company_id=company_id,
            aktif_mi=aktif_mi
        )
        user.set_password(password)
        
        try:
            db.session.add(user)
            db.session.commit()
            flash(f'{user.tam_ad} başarıyla kaydedildi.', 'success')
            return redirect(url_for('auth.users'))
        except Exception as e:
            db.session.rollback()
            flash('Kullanıcı kaydedilirken bir hata oluştu.', 'error')

    return render_template('register.html', managers=managers, companies=companies)

@auth_bp.route('/users')
@login_required
def users():
    """Kullanıcı listesi (sadece admin)"""
    if not current_user.is_admin:
        flash('Bu sayfaya erişim yetkiniz yok.', 'error')
        return redirect(url_for('dashboard'))

    # Filtreleme parametreleri
    rol = request.args.get('rol', '')
    durum = request.args.get('durum', '')
    arama = request.args.get('arama', '')
    sirket = request.args.get('sirket', '')

    page = request.args.get('page', 1, type=int)
    per_page = 20

    # Query oluştur
    query = User.query

    # Filtreleri uygula
    if rol:
        query = query.filter(User.rol == rol)

    if durum == 'aktif':
        query = query.filter(User.aktif_mi == True)
    elif durum == 'pasif':
        query = query.filter(User.aktif_mi == False)

    if sirket:
        query = query.filter(User.company_id == int(sirket))

    if arama:
        search_filter = db.or_(
            User.ad.contains(arama),
            User.soyad.contains(arama),
            User.email.contains(arama)
        )
        query = query.filter(search_filter)

    # Sayfalama
    users = query.order_by(User.olusturulma_tarihi.desc()).paginate(
        page=page, per_page=per_page, error_out=False
    )

    # Şirketleri getir (filtre için)
    from app.models.company import Company
    companies = Company.get_active_companies()

    return render_template('users.html', users=users, companies=companies)

@auth_bp.route('/users/<int:user_id>/toggle-status', methods=['POST'])
@login_required
def toggle_user_status(user_id):
    """Kullanıcı durumunu aktif/pasif yap (sadece admin)"""
    if not current_user.is_admin:
        flash('Bu işlem için yetkiniz yok.', 'error')
        return redirect(url_for('dashboard'))
    
    user = User.query.get_or_404(user_id)
    
    if user.id == current_user.id:
        flash('Kendi hesabınızın durumunu değiştiremezsiniz.', 'error')
        return redirect(url_for('auth.users'))
    
    user.aktif_mi = not user.aktif_mi
    db.session.commit()
    
    status = 'aktif' if user.aktif_mi else 'pasif'
    flash(f'{user.tam_ad} kullanıcısı {status} duruma getirildi.', 'success')
    
    return redirect(url_for('auth.users'))

@auth_bp.route('/profile')
@login_required
def profile():
    """Kullanıcı profili"""
    return render_template('profile.html', user=current_user)

@auth_bp.route('/profile/edit', methods=['GET', 'POST'])
@login_required
def edit_profile():
    """Profil düzenleme"""
    if request.method == 'POST':
        ad = request.form.get('ad', '').strip()
        soyad = request.form.get('soyad', '').strip()
        telefon_numarasi = request.form.get('telefon_numarasi', '').strip()
        
        if not all([ad, soyad, telefon_numarasi]):
            flash('Tüm alanlar zorunludur.', 'error')
            return render_template('edit_profile.html', user=current_user)
        
        current_user.ad = ad
        current_user.soyad = soyad
        current_user.telefon_numarasi = telefon_numarasi
        
        try:
            db.session.commit()
            flash('Profiliniz başarıyla güncellendi.', 'success')
            return redirect(url_for('auth.profile'))
        except Exception as e:
            db.session.rollback()
            flash('Profil güncellenirken bir hata oluştu.', 'error')
    
    return render_template('edit_profile.html', user=current_user)

@auth_bp.route('/change-password', methods=['GET', 'POST'])
@login_required
def change_password():
    """Şifre değiştirme"""
    if request.method == 'POST':
        current_password = request.form.get('current_password', '')
        new_password = request.form.get('new_password', '')
        confirm_password = request.form.get('confirm_password', '')

        if not all([current_password, new_password, confirm_password]):
            flash('Tüm alanlar zorunludur.', 'error')
            return render_template('change_password.html')

        if not current_user.check_password(current_password):
            flash('Mevcut şifreniz yanlış.', 'error')
            return render_template('change_password.html')

        if len(new_password) < 6:
            flash('Yeni şifre en az 6 karakter olmalıdır.', 'error')
            return render_template('change_password.html')

        if new_password != confirm_password:
            flash('Yeni şifreler eşleşmiyor.', 'error')
            return render_template('change_password.html')

        current_user.set_password(new_password)

        try:
            db.session.commit()
            flash('Şifreniz başarıyla değiştirildi.', 'success')
            return redirect(url_for('auth.profile'))
        except Exception as e:
            db.session.rollback()
            flash('Şifre değiştirilirken bir hata oluştu.', 'error')

    return render_template('change_password.html')

@auth_bp.route('/users/<int:user_id>/details')
@login_required
def user_details(user_id):
    """Kullanıcı detayları (AJAX için)"""
    if not current_user.is_admin:
        return jsonify({'success': False, 'message': 'Yetkiniz yok'}), 403

    user = User.query.get_or_404(user_id)

    # İstatistikleri hesapla
    stats = {
        'egitim_sayisi': user.enrollments.filter_by(aktif_mi=True).count(),
        'quiz_sayisi': user.quiz_attempts.filter_by(tamamlandi_mi=True).count(),
        'ortalama_skor': 0
    }

    # Ortalama skoru hesapla
    attempts = user.quiz_attempts.filter_by(tamamlandi_mi=True).all()
    if attempts:
        stats['ortalama_skor'] = round(sum(a.skor for a in attempts) / len(attempts), 1)

    # Son aktiviteler (örnek)
    recent_activities = []

    return jsonify({
        'success': True,
        'user': user.to_dict(),
        'stats': stats,
        'recent_activities': recent_activities
    })

@auth_bp.route('/users/bulk-upload', methods=['GET', 'POST'])
@login_required
def bulk_upload_users():
    """Excel'den toplu kullanıcı yükleme"""
    if not current_user.is_admin:
        flash('Bu sayfaya erişim yetkiniz yok.', 'error')
        return redirect(url_for('dashboard'))

    if request.method == 'POST':
        # Dosya kontrolü
        if 'excel_file' not in request.files:
            flash('Dosya seçilmedi.', 'error')
            return render_template('bulk_upload_users.html')

        file = request.files['excel_file']
        if file.filename == '':
            flash('Dosya seçilmedi.', 'error')
            return render_template('bulk_upload_users.html')

        # Upload klasörünü kontrol et ve oluştur
        upload_folder = current_app.config['UPLOAD_FOLDER']
        if not os.path.exists(upload_folder):
            os.makedirs(upload_folder, exist_ok=True)

        # Excel işleyiciyi başlat
        processor = ExcelUserProcessor(upload_folder)

        # Dosyayı işle
        results, errors = processor.process_excel_file(file)

        if errors:
            for error in errors:
                flash(error, 'error')
            return render_template('bulk_upload_users.html')

        if results:
            if results['success_count'] > 0:
                flash(f"{results['success_count']} kullanıcı başarıyla oluşturuldu.", 'success')

            if results['error_count'] > 0:
                flash(f"{results['error_count']} kullanıcı oluşturulamadı.", 'warning')
                for error in results['errors']:
                    flash(error, 'warning')

            return render_template('bulk_upload_results.html', results=results)

    return render_template('bulk_upload_users.html')

@auth_bp.route('/users/download-sample')
@login_required
def download_sample_excel():
    """Örnek Excel dosyasını indir"""
    if not current_user.is_admin:
        flash('Bu sayfaya erişim yetkiniz yok.', 'error')
        return redirect(url_for('dashboard'))

    # Upload klasörünü kontrol et ve oluştur
    upload_folder = current_app.config['UPLOAD_FOLDER']
    if not os.path.exists(upload_folder):
        os.makedirs(upload_folder, exist_ok=True)

    # Geçici dosya oluştur
    sample_path = os.path.join(upload_folder, 'kullanici_ornegi.xlsx')

    processor = ExcelUserProcessor(upload_folder)
    processor.create_sample_excel(sample_path)

    return send_file(
        sample_path,
        as_attachment=True,
        download_name='kullanici_ornegi.xlsx',
        mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )

@auth_bp.route('/users/<int:user_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_user(user_id):
    """Kullanıcı düzenleme (sadece admin)"""
    if not current_user.is_admin:
        flash('Bu sayfaya erişim yetkiniz yok.', 'error')
        return redirect(url_for('auth.users'))

    user = User.query.get_or_404(user_id)

    # Yöneticileri getir (manager ve admin rolündeki kullanıcılar)
    managers = User.query.filter(
        User.rol.in_(['manager', 'admin']),
        User.aktif_mi == True,
        User.id != user_id  # Kendisini hariç tut
    ).order_by(User.ad, User.soyad).all()

    # Şirketleri getir
    from app.models.company import Company
    companies = Company.get_active_companies()

    if request.method == 'POST':
        # Form verilerini al
        ad = request.form.get('ad', '').strip()
        soyad = request.form.get('soyad', '').strip()
        email = request.form.get('email', '').strip().lower()
        telefon_numarasi = request.form.get('telefon_numarasi', '').strip()
        rol = request.form.get('rol', 'participant')
        manager_id = request.form.get('manager_id', '') or None
        company_id = request.form.get('company_id', '') or None
        aktif_mi = 'aktif_mi' in request.form

        # Manager ID validasyonu
        if manager_id:
            try:
                manager_id = int(manager_id)
                manager = User.query.get(manager_id)
                if not manager or manager.rol not in ['manager', 'admin']:
                    flash('Geçersiz yönetici seçimi.', 'error')
                    return render_template('edit_user.html', user=user, managers=managers)
            except ValueError:
                manager_id = None

        # Validasyon
        if not all([ad, soyad, email, telefon_numarasi]):
            flash('Tüm alanlar zorunludur.', 'error')
            return render_template('edit_user.html', user=user, managers=managers)

        if rol not in ['participant', 'manager', 'admin']:
            flash('Geçersiz rol seçimi.', 'error')
            return render_template('edit_user.html', user=user, managers=managers)

        # Email kontrolü (başka kullanıcıda kullanılıyor mu?)
        existing_user = User.query.filter(User.email == email, User.id != user_id).first()
        if existing_user:
            flash('Bu email adresi başka bir kullanıcı tarafından kullanılıyor.', 'error')
            return render_template('edit_user.html', user=user, managers=managers)

        try:
            # Kullanıcı bilgilerini güncelle
            user.ad = ad
            user.soyad = soyad
            user.email = email
            user.telefon_numarasi = telefon_numarasi
            user.rol = rol
            user.manager_id = manager_id
            user.company_id = company_id
            user.aktif_mi = aktif_mi

            db.session.commit()
            flash(f'{user.tam_ad} kullanıcısı başarıyla güncellendi.', 'success')
            return redirect(url_for('auth.users'))

        except Exception as e:
            db.session.rollback()
            flash('Kullanıcı güncellenirken bir hata oluştu.', 'error')

    return render_template('edit_user.html', user=user, managers=managers, companies=companies)

@auth_bp.route('/users/<int:user_id>/reset-password', methods=['POST'])
@login_required
def reset_user_password(user_id):
    """Kullanıcı şifresini sıfırla (sadece admin)"""
    if not current_user.is_admin:
        return jsonify({'success': False, 'message': 'Yetkiniz yok'}), 403

    user = User.query.get_or_404(user_id)

    try:
        # Varsayılan şifre: 123456
        new_password = '123456'
        user.set_password(new_password)

        db.session.commit()

        return jsonify({
            'success': True,
            'message': f'{user.tam_ad} kullanıcısının şifresi başarıyla sıfırlandı.',
            'new_password': new_password
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': 'Şifre sıfırlanırken bir hata oluştu.'
        }), 500
