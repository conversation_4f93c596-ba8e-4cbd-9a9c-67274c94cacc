{% extends "base.html" %}

{% block title %}<PERSON><PERSON>ı - Eğitim Yönetim Sistemi{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-user-plus"></i> <PERSON>ni <PERSON>llanıcı Oluştur
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{{ url_for('auth.users') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left"></i> Kullanıcı Listesi
        </a>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card shadow">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle"></i> Kullanıcı Bilgileri
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" id="registerForm">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="ad" class="form-label">Ad *</label>
                            <input type="text" class="form-control" id="ad" name="ad" required>
                        </div>
                        <div class="col-md-6">
                            <label for="soyad" class="form-label">Soyad *</label>
                            <input type="text" class="form-control" id="soyad" name="soyad" required>
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="email" class="form-label">E-posta Adresi *</label>
                            <input type="email" class="form-control" id="email" name="email" required>
                            <div class="form-text">Kullanıcı bu e-posta ile giriş yapacak</div>
                        </div>
                        <div class="col-md-6">
                            <label for="telefon_numarasi" class="form-label">Telefon Numarası *</label>
                            <input type="tel" class="form-control" id="telefon_numarasi" name="telefon_numarasi" 
                                   placeholder="05551234567" required>
                            <div class="form-text">SMS bildirimleri için kullanılacak</div>
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="password" class="form-label">Şifre *</label>
                            <input type="password" class="form-control" id="password" name="password" 
                                   minlength="6" required>
                            <div class="form-text">En az 6 karakter olmalıdır</div>
                        </div>
                        <div class="col-md-6">
                            <label for="confirm_password" class="form-label">Şifre Tekrar *</label>
                            <input type="password" class="form-control" id="confirm_password" name="confirm_password" 
                                   minlength="6" required>
                            <div class="form-text">Şifreyi tekrar girin</div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="rol" class="form-label">Kullanıcı Rolü *</label>
                        <select class="form-select" id="rol" name="rol" required>
                            <option value="">Rol seçin...</option>
                            <option value="participant">Participant (Katılımcı)</option>
                            <option value="manager">Manager (Yönetici)</option>
                            <option value="admin">Admin (Sistem Yöneticisi)</option>
                        </select>
                        <div class="form-text">
                            <small>
                                <strong>Participant:</strong> Eğitimlere katılabilir, quiz çözebilir<br>
                                <strong>Manager:</strong> Eğitim ve quiz oluşturabilir, katılımcı yönetebilir<br>
                                <strong>Admin:</strong> Tüm sistem yönetimi yetkilerine sahip
                            </small>
                        </div>
                    </div>

                    <div class="mb-3" id="manager_selection" style="display: none;">
                        <label for="manager_id" class="form-label">Yönetici Seçin</label>
                        <select class="form-select" id="manager_id" name="manager_id">
                            <option value="">Yönetici seçin...</option>
                            {% for manager in managers %}
                            <option value="{{ manager.id }}">{{ manager.tam_ad }} ({{ manager.email }})</option>
                            {% endfor %}
                        </select>
                        <div class="form-text">Katılımcılar için yönetici ataması yapılabilir</div>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="aktif_mi" name="aktif_mi" checked>
                            <label class="form-check-label" for="aktif_mi">
                                Kullanıcı aktif olsun
                            </label>
                            <div class="form-text">Pasif kullanıcılar sisteme giriş yapamaz</div>
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('auth.users') }}" class="btn btn-secondary">
                            <i class="fas fa-times"></i> İptal
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Kullanıcı Oluştur
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card shadow">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-lightbulb"></i> Yardım</h6>
            </div>
            <div class="card-body">
                <h6>Kullanıcı Oluşturma İpuçları:</h6>
                <ul class="small">
                    <li>E-posta adresi benzersiz olmalıdır</li>
                    <li>Telefon numarası 05xxxxxxxxx formatında olmalıdır</li>
                    <li>Güçlü şifre kullanın (en az 6 karakter)</li>
                    <li>Rolü kullanıcının görevine uygun seçin</li>
                </ul>
                
                <hr>
                
                <h6>Rol Açıklamaları:</h6>
                <div class="small">
                    <div class="mb-2">
                        <span class="badge bg-info">Participant</span>
                        <p class="mb-0">Eğitimlere katılır, quiz çözer</p>
                    </div>
                    <div class="mb-2">
                        <span class="badge bg-warning">Manager</span>
                        <p class="mb-0">Eğitim ve quiz oluşturur, yönetir</p>
                    </div>
                    <div class="mb-2">
                        <span class="badge bg-danger">Admin</span>
                        <p class="mb-0">Tüm sistem yönetimi</p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card shadow mt-4">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-shield-alt"></i> Güvenlik</h6>
            </div>
            <div class="card-body">
                <div class="small">
                    <p><strong>Şifre Gereksinimleri:</strong></p>
                    <ul>
                        <li>En az 6 karakter</li>
                        <li>Büyük ve küçük harf karışımı önerilir</li>
                        <li>Sayı ve özel karakter önerilir</li>
                    </ul>
                    
                    <p class="mt-3"><strong>Veri Güvenliği:</strong></p>
                    <ul>
                        <li>Şifreler hash'lenerek saklanır</li>
                        <li>Kişisel veriler şifrelenir</li>
                        <li>Sistem logları tutulur</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Form validasyonu
    $('#registerForm').on('submit', function(e) {
        if (!validateForm()) {
            e.preventDefault();
        }
    });
    
    // Şifre eşleşme kontrolü
    $('#confirm_password').on('input', function() {
        checkPasswordMatch();
    });
    
    // Telefon numarası formatı
    $('#telefon_numarasi').on('input', function() {
        formatPhoneNumber(this);
    });
    
    // E-posta kontrolü
    $('#email').on('blur', function() {
        checkEmailAvailability();
    });
});

function validateForm() {
    let isValid = true;
    
    // Şifre eşleşme kontrolü
    if ($('#password').val() !== $('#confirm_password').val()) {
        alert('Şifreler eşleşmiyor!');
        isValid = false;
    }
    
    // Şifre uzunluk kontrolü
    if ($('#password').val().length < 6) {
        alert('Şifre en az 6 karakter olmalıdır!');
        isValid = false;
    }
    
    // Telefon numarası kontrolü
    let phone = $('#telefon_numarasi').val().replace(/\D/g, '');
    if (phone.length !== 11 || !phone.startsWith('05')) {
        alert('Telefon numarası 05xxxxxxxxx formatında olmalıdır!');
        isValid = false;
    }
    
    // E-posta kontrolü
    let email = $('#email').val();
    let emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
        alert('Geçerli bir e-posta adresi girin!');
        isValid = false;
    }
    
    return isValid;
}

function checkPasswordMatch() {
    let password = $('#password').val();
    let confirmPassword = $('#confirm_password').val();
    
    if (confirmPassword && password !== confirmPassword) {
        $('#confirm_password').addClass('is-invalid');
        $('#confirm_password').removeClass('is-valid');
    } else if (confirmPassword) {
        $('#confirm_password').removeClass('is-invalid');
        $('#confirm_password').addClass('is-valid');
    }
}

function formatPhoneNumber(input) {
    // Sadece rakamları al
    let value = input.value.replace(/\D/g, '');
    
    // 05 ile başlamazsa ekle
    if (value.length > 0 && !value.startsWith('05')) {
        if (value.startsWith('5')) {
            value = '0' + value;
        } else if (value.startsWith('0') && !value.startsWith('05')) {
            value = '05' + value.substring(1);
        }
    }
    
    // Maksimum 11 karakter
    if (value.length > 11) {
        value = value.substring(0, 11);
    }
    
    input.value = value;
    
    // Validasyon göstergesi
    if (value.length === 11 && value.startsWith('05')) {
        $(input).removeClass('is-invalid').addClass('is-valid');
    } else if (value.length > 0) {
        $(input).removeClass('is-valid').addClass('is-invalid');
    } else {
        $(input).removeClass('is-valid is-invalid');
    }
}

function checkEmailAvailability() {
    let email = $('#email').val();
    
    if (email) {
        // Basit e-posta formatı kontrolü
        let emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (emailRegex.test(email)) {
            $('#email').removeClass('is-invalid').addClass('is-valid');
        } else {
            $('#email').removeClass('is-valid').addClass('is-invalid');
        }
    }
}

// Rol seçimi değiştiğinde açıklama göster ve manager seçimini kontrol et
$('#rol').on('change', function() {
    let rol = $(this).val();
    let descriptions = {
        'participant': 'Katılımcılar eğitimlere katılabilir ve quiz çözebilir.',
        'manager': 'Yöneticiler eğitim oluşturabilir, katılımcı yönetebilir ve quiz hazırlayabilir.',
        'admin': 'Adminler tüm sistem yönetimi yetkilerine sahiptir.'
    };

    // Manager seçimini göster/gizle
    if (rol === 'participant') {
        $('#manager_selection').show();
    } else {
        $('#manager_selection').hide();
        $('#manager_id').val(''); // Seçimi temizle
    }

    if (rol && descriptions[rol]) {
        // Geçici olarak açıklama göster
        let $description = $('<div class="alert alert-info mt-2">' + descriptions[rol] + '</div>');
        $('#rol').parent().find('.alert').remove();
        $('#rol').parent().append($description);

        setTimeout(function() {
            $description.fadeOut(function() {
                $(this).remove();
            });
        }, 3000);
    }
});
</script>
{% endblock %}
