r"""
    This code was generated by
   ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
    |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
    |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

    Twilio - Taskrouter
    This is the public Twilio REST API.

    NOTE: This class is auto generated by OpenAPI Generator.
    https://openapi-generator.tech
    Do not edit the class manually.
"""


from datetime import datetime
from typing import Any, Dict, List, Optional, Union, Iterator, AsyncIterator
from twilio.base import deserialize, serialize, values
from twilio.base.instance_context import InstanceContext
from twilio.base.instance_resource import InstanceResource
from twilio.base.list_resource import ListResource
from twilio.base.version import Version
from twilio.base.page import Page
from twilio.rest.taskrouter.v1.workspace.task.reservation import ReservationList


class TaskInstance(InstanceResource):
    class Status(object):
        PENDING = "pending"
        RESERVED = "reserved"
        ASSIGNED = "assigned"
        CANCELED = "canceled"
        COMPLETED = "completed"
        WRAPPING = "wrapping"

    """
    :ivar account_sid: The SID of the [Account](https://www.twilio.com/docs/iam/api/account) that created the Task resource.
    :ivar age: The number of seconds since the Task was created.
    :ivar assignment_status: 
    :ivar attributes: The JSON string with custom attributes of the work. **Note** If this property has been assigned a value, it will only be displayed in FETCH action that returns a single resource. Otherwise, it will be null.
    :ivar addons: An object that contains the [addon](https://www.twilio.com/docs/taskrouter/marketplace) data for all installed addons.
    :ivar date_created: The date and time in GMT when the resource was created specified in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format.
    :ivar date_updated: The date and time in GMT when the resource was last updated specified in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format.
    :ivar task_queue_entered_date: The date and time in GMT when the Task entered the TaskQueue, specified in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format.
    :ivar priority: The current priority score of the Task as assigned to a Worker by the workflow. Tasks with higher priority values will be assigned before Tasks with lower values.
    :ivar reason: The reason the Task was canceled or completed, if applicable.
    :ivar sid: The unique string that we created to identify the Task resource.
    :ivar task_queue_sid: The SID of the TaskQueue.
    :ivar task_queue_friendly_name: The friendly name of the TaskQueue.
    :ivar task_channel_sid: The SID of the TaskChannel.
    :ivar task_channel_unique_name: The unique name of the TaskChannel.
    :ivar timeout: The amount of time in seconds that the Task can live before being assigned.
    :ivar workflow_sid: The SID of the Workflow that is controlling the Task.
    :ivar workflow_friendly_name: The friendly name of the Workflow that is controlling the Task.
    :ivar workspace_sid: The SID of the Workspace that contains the Task.
    :ivar url: The absolute URL of the Task resource.
    :ivar links: The URLs of related resources.
    """

    def __init__(
        self,
        version: Version,
        payload: Dict[str, Any],
        workspace_sid: str,
        sid: Optional[str] = None,
    ):
        super().__init__(version)

        self.account_sid: Optional[str] = payload.get("account_sid")
        self.age: Optional[int] = deserialize.integer(payload.get("age"))
        self.assignment_status: Optional["TaskInstance.Status"] = payload.get(
            "assignment_status"
        )
        self.attributes: Optional[str] = payload.get("attributes")
        self.addons: Optional[str] = payload.get("addons")
        self.date_created: Optional[datetime] = deserialize.iso8601_datetime(
            payload.get("date_created")
        )
        self.date_updated: Optional[datetime] = deserialize.iso8601_datetime(
            payload.get("date_updated")
        )
        self.task_queue_entered_date: Optional[datetime] = deserialize.iso8601_datetime(
            payload.get("task_queue_entered_date")
        )
        self.priority: Optional[int] = deserialize.integer(payload.get("priority"))
        self.reason: Optional[str] = payload.get("reason")
        self.sid: Optional[str] = payload.get("sid")
        self.task_queue_sid: Optional[str] = payload.get("task_queue_sid")
        self.task_queue_friendly_name: Optional[str] = payload.get(
            "task_queue_friendly_name"
        )
        self.task_channel_sid: Optional[str] = payload.get("task_channel_sid")
        self.task_channel_unique_name: Optional[str] = payload.get(
            "task_channel_unique_name"
        )
        self.timeout: Optional[int] = deserialize.integer(payload.get("timeout"))
        self.workflow_sid: Optional[str] = payload.get("workflow_sid")
        self.workflow_friendly_name: Optional[str] = payload.get(
            "workflow_friendly_name"
        )
        self.workspace_sid: Optional[str] = payload.get("workspace_sid")
        self.url: Optional[str] = payload.get("url")
        self.links: Optional[Dict[str, object]] = payload.get("links")

        self._solution = {
            "workspace_sid": workspace_sid,
            "sid": sid or self.sid,
        }
        self._context: Optional[TaskContext] = None

    @property
    def _proxy(self) -> "TaskContext":
        """
        Generate an instance context for the instance, the context is capable of
        performing various actions. All instance actions are proxied to the context

        :returns: TaskContext for this TaskInstance
        """
        if self._context is None:
            self._context = TaskContext(
                self._version,
                workspace_sid=self._solution["workspace_sid"],
                sid=self._solution["sid"],
            )
        return self._context

    def delete(self, if_match: Union[str, object] = values.unset) -> bool:
        """
        Deletes the TaskInstance

        :param if_match: If provided, deletes this Task if (and only if) the [ETag](https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/ETag) header of the Task matches the provided value. This matches the semantics of (and is implemented with) the HTTP [If-Match header](https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/If-Match).

        :returns: True if delete succeeds, False otherwise
        """
        return self._proxy.delete(
            if_match=if_match,
        )

    async def delete_async(self, if_match: Union[str, object] = values.unset) -> bool:
        """
        Asynchronous coroutine that deletes the TaskInstance

        :param if_match: If provided, deletes this Task if (and only if) the [ETag](https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/ETag) header of the Task matches the provided value. This matches the semantics of (and is implemented with) the HTTP [If-Match header](https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/If-Match).

        :returns: True if delete succeeds, False otherwise
        """
        return await self._proxy.delete_async(
            if_match=if_match,
        )

    def fetch(self) -> "TaskInstance":
        """
        Fetch the TaskInstance


        :returns: The fetched TaskInstance
        """
        return self._proxy.fetch()

    async def fetch_async(self) -> "TaskInstance":
        """
        Asynchronous coroutine to fetch the TaskInstance


        :returns: The fetched TaskInstance
        """
        return await self._proxy.fetch_async()

    def update(
        self,
        if_match: Union[str, object] = values.unset,
        attributes: Union[str, object] = values.unset,
        assignment_status: Union["TaskInstance.Status", object] = values.unset,
        reason: Union[str, object] = values.unset,
        priority: Union[int, object] = values.unset,
        task_channel: Union[str, object] = values.unset,
    ) -> "TaskInstance":
        """
        Update the TaskInstance

        :param if_match: If provided, applies this mutation if (and only if) the [ETag](https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/ETag) header of the Task matches the provided value. This matches the semantics of (and is implemented with) the HTTP [If-Match header](https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/If-Match).
        :param attributes: The JSON string that describes the custom attributes of the task.
        :param assignment_status:
        :param reason: The reason that the Task was canceled or completed. This parameter is required only if the Task is canceled or completed. Setting this value queues the task for deletion and logs the reason.
        :param priority: The Task's new priority value. When supplied, the Task takes on the specified priority unless it matches a Workflow Target with a Priority set. Value can be 0 to 2^31^ (2,147,483,647).
        :param task_channel: When MultiTasking is enabled, specify the TaskChannel with the task to update. Can be the TaskChannel's SID or its `unique_name`, such as `voice`, `sms`, or `default`.

        :returns: The updated TaskInstance
        """
        return self._proxy.update(
            if_match=if_match,
            attributes=attributes,
            assignment_status=assignment_status,
            reason=reason,
            priority=priority,
            task_channel=task_channel,
        )

    async def update_async(
        self,
        if_match: Union[str, object] = values.unset,
        attributes: Union[str, object] = values.unset,
        assignment_status: Union["TaskInstance.Status", object] = values.unset,
        reason: Union[str, object] = values.unset,
        priority: Union[int, object] = values.unset,
        task_channel: Union[str, object] = values.unset,
    ) -> "TaskInstance":
        """
        Asynchronous coroutine to update the TaskInstance

        :param if_match: If provided, applies this mutation if (and only if) the [ETag](https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/ETag) header of the Task matches the provided value. This matches the semantics of (and is implemented with) the HTTP [If-Match header](https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/If-Match).
        :param attributes: The JSON string that describes the custom attributes of the task.
        :param assignment_status:
        :param reason: The reason that the Task was canceled or completed. This parameter is required only if the Task is canceled or completed. Setting this value queues the task for deletion and logs the reason.
        :param priority: The Task's new priority value. When supplied, the Task takes on the specified priority unless it matches a Workflow Target with a Priority set. Value can be 0 to 2^31^ (2,147,483,647).
        :param task_channel: When MultiTasking is enabled, specify the TaskChannel with the task to update. Can be the TaskChannel's SID or its `unique_name`, such as `voice`, `sms`, or `default`.

        :returns: The updated TaskInstance
        """
        return await self._proxy.update_async(
            if_match=if_match,
            attributes=attributes,
            assignment_status=assignment_status,
            reason=reason,
            priority=priority,
            task_channel=task_channel,
        )

    @property
    def reservations(self) -> ReservationList:
        """
        Access the reservations
        """
        return self._proxy.reservations

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Taskrouter.V1.TaskInstance {}>".format(context)


class TaskContext(InstanceContext):
    def __init__(self, version: Version, workspace_sid: str, sid: str):
        """
        Initialize the TaskContext

        :param version: Version that contains the resource
        :param workspace_sid: The SID of the Workspace with the Task to update.
        :param sid: The SID of the Task resource to update.
        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "workspace_sid": workspace_sid,
            "sid": sid,
        }
        self._uri = "/Workspaces/{workspace_sid}/Tasks/{sid}".format(**self._solution)

        self._reservations: Optional[ReservationList] = None

    def delete(self, if_match: Union[str, object] = values.unset) -> bool:
        """
        Deletes the TaskInstance

        :param if_match: If provided, deletes this Task if (and only if) the [ETag](https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/ETag) header of the Task matches the provided value. This matches the semantics of (and is implemented with) the HTTP [If-Match header](https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/If-Match).

        :returns: True if delete succeeds, False otherwise
        """
        headers = values.of(
            {
                "If-Match": if_match,
            }
        )

        return self._version.delete(method="DELETE", uri=self._uri, headers=headers)

    async def delete_async(self, if_match: Union[str, object] = values.unset) -> bool:
        """
        Asynchronous coroutine that deletes the TaskInstance

        :param if_match: If provided, deletes this Task if (and only if) the [ETag](https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/ETag) header of the Task matches the provided value. This matches the semantics of (and is implemented with) the HTTP [If-Match header](https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/If-Match).

        :returns: True if delete succeeds, False otherwise
        """
        headers = values.of(
            {
                "If-Match": if_match,
            }
        )

        return await self._version.delete_async(
            method="DELETE", uri=self._uri, headers=headers
        )

    def fetch(self) -> TaskInstance:
        """
        Fetch the TaskInstance


        :returns: The fetched TaskInstance
        """

        payload = self._version.fetch(
            method="GET",
            uri=self._uri,
        )

        return TaskInstance(
            self._version,
            payload,
            workspace_sid=self._solution["workspace_sid"],
            sid=self._solution["sid"],
        )

    async def fetch_async(self) -> TaskInstance:
        """
        Asynchronous coroutine to fetch the TaskInstance


        :returns: The fetched TaskInstance
        """

        payload = await self._version.fetch_async(
            method="GET",
            uri=self._uri,
        )

        return TaskInstance(
            self._version,
            payload,
            workspace_sid=self._solution["workspace_sid"],
            sid=self._solution["sid"],
        )

    def update(
        self,
        if_match: Union[str, object] = values.unset,
        attributes: Union[str, object] = values.unset,
        assignment_status: Union["TaskInstance.Status", object] = values.unset,
        reason: Union[str, object] = values.unset,
        priority: Union[int, object] = values.unset,
        task_channel: Union[str, object] = values.unset,
    ) -> TaskInstance:
        """
        Update the TaskInstance

        :param if_match: If provided, applies this mutation if (and only if) the [ETag](https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/ETag) header of the Task matches the provided value. This matches the semantics of (and is implemented with) the HTTP [If-Match header](https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/If-Match).
        :param attributes: The JSON string that describes the custom attributes of the task.
        :param assignment_status:
        :param reason: The reason that the Task was canceled or completed. This parameter is required only if the Task is canceled or completed. Setting this value queues the task for deletion and logs the reason.
        :param priority: The Task's new priority value. When supplied, the Task takes on the specified priority unless it matches a Workflow Target with a Priority set. Value can be 0 to 2^31^ (2,147,483,647).
        :param task_channel: When MultiTasking is enabled, specify the TaskChannel with the task to update. Can be the TaskChannel's SID or its `unique_name`, such as `voice`, `sms`, or `default`.

        :returns: The updated TaskInstance
        """
        data = values.of(
            {
                "Attributes": attributes,
                "AssignmentStatus": assignment_status,
                "Reason": reason,
                "Priority": priority,
                "TaskChannel": task_channel,
            }
        )
        headers = values.of(
            {
                "If-Match": if_match,
            }
        )

        payload = self._version.update(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return TaskInstance(
            self._version,
            payload,
            workspace_sid=self._solution["workspace_sid"],
            sid=self._solution["sid"],
        )

    async def update_async(
        self,
        if_match: Union[str, object] = values.unset,
        attributes: Union[str, object] = values.unset,
        assignment_status: Union["TaskInstance.Status", object] = values.unset,
        reason: Union[str, object] = values.unset,
        priority: Union[int, object] = values.unset,
        task_channel: Union[str, object] = values.unset,
    ) -> TaskInstance:
        """
        Asynchronous coroutine to update the TaskInstance

        :param if_match: If provided, applies this mutation if (and only if) the [ETag](https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/ETag) header of the Task matches the provided value. This matches the semantics of (and is implemented with) the HTTP [If-Match header](https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/If-Match).
        :param attributes: The JSON string that describes the custom attributes of the task.
        :param assignment_status:
        :param reason: The reason that the Task was canceled or completed. This parameter is required only if the Task is canceled or completed. Setting this value queues the task for deletion and logs the reason.
        :param priority: The Task's new priority value. When supplied, the Task takes on the specified priority unless it matches a Workflow Target with a Priority set. Value can be 0 to 2^31^ (2,147,483,647).
        :param task_channel: When MultiTasking is enabled, specify the TaskChannel with the task to update. Can be the TaskChannel's SID or its `unique_name`, such as `voice`, `sms`, or `default`.

        :returns: The updated TaskInstance
        """
        data = values.of(
            {
                "Attributes": attributes,
                "AssignmentStatus": assignment_status,
                "Reason": reason,
                "Priority": priority,
                "TaskChannel": task_channel,
            }
        )
        headers = values.of(
            {
                "If-Match": if_match,
            }
        )

        payload = await self._version.update_async(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return TaskInstance(
            self._version,
            payload,
            workspace_sid=self._solution["workspace_sid"],
            sid=self._solution["sid"],
        )

    @property
    def reservations(self) -> ReservationList:
        """
        Access the reservations
        """
        if self._reservations is None:
            self._reservations = ReservationList(
                self._version,
                self._solution["workspace_sid"],
                self._solution["sid"],
            )
        return self._reservations

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Taskrouter.V1.TaskContext {}>".format(context)


class TaskPage(Page):
    def get_instance(self, payload: Dict[str, Any]) -> TaskInstance:
        """
        Build an instance of TaskInstance

        :param payload: Payload response from the API
        """
        return TaskInstance(
            self._version, payload, workspace_sid=self._solution["workspace_sid"]
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Taskrouter.V1.TaskPage>"


class TaskList(ListResource):
    def __init__(self, version: Version, workspace_sid: str):
        """
        Initialize the TaskList

        :param version: Version that contains the resource
        :param workspace_sid: The SID of the Workspace with the Tasks to read.

        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "workspace_sid": workspace_sid,
        }
        self._uri = "/Workspaces/{workspace_sid}/Tasks".format(**self._solution)

    def create(
        self,
        timeout: Union[int, object] = values.unset,
        priority: Union[int, object] = values.unset,
        task_channel: Union[str, object] = values.unset,
        workflow_sid: Union[str, object] = values.unset,
        attributes: Union[str, object] = values.unset,
    ) -> TaskInstance:
        """
        Create the TaskInstance

        :param timeout: The amount of time in seconds the new task can live before being assigned. Can be up to a maximum of 2 weeks (1,209,600 seconds). The default value is 24 hours (86,400 seconds). On timeout, the `task.canceled` event will fire with description `Task TTL Exceeded`.
        :param priority: The priority to assign the new task and override the default. When supplied, the new Task will have this priority unless it matches a Workflow Target with a Priority set. When not supplied, the new Task will have the priority of the matching Workflow Target. Value can be 0 to 2^31^ (2,147,483,647).
        :param task_channel: When MultiTasking is enabled, specify the TaskChannel by passing either its `unique_name` or `sid`. Default value is `default`.
        :param workflow_sid: The SID of the Workflow that you would like to handle routing for the new Task. If there is only one Workflow defined for the Workspace that you are posting the new task to, this parameter is optional.
        :param attributes: A URL-encoded JSON string with the attributes of the new task. This value is passed to the Workflow's `assignment_callback_url` when the Task is assigned to a Worker. For example: `{ \\\"task_type\\\": \\\"call\\\", \\\"twilio_call_sid\\\": \\\"CAxxx\\\", \\\"customer_ticket_number\\\": \\\"12345\\\" }`.

        :returns: The created TaskInstance
        """
        data = values.of(
            {
                "Timeout": timeout,
                "Priority": priority,
                "TaskChannel": task_channel,
                "WorkflowSid": workflow_sid,
                "Attributes": attributes,
            }
        )

        payload = self._version.create(
            method="POST",
            uri=self._uri,
            data=data,
        )

        return TaskInstance(
            self._version, payload, workspace_sid=self._solution["workspace_sid"]
        )

    async def create_async(
        self,
        timeout: Union[int, object] = values.unset,
        priority: Union[int, object] = values.unset,
        task_channel: Union[str, object] = values.unset,
        workflow_sid: Union[str, object] = values.unset,
        attributes: Union[str, object] = values.unset,
    ) -> TaskInstance:
        """
        Asynchronously create the TaskInstance

        :param timeout: The amount of time in seconds the new task can live before being assigned. Can be up to a maximum of 2 weeks (1,209,600 seconds). The default value is 24 hours (86,400 seconds). On timeout, the `task.canceled` event will fire with description `Task TTL Exceeded`.
        :param priority: The priority to assign the new task and override the default. When supplied, the new Task will have this priority unless it matches a Workflow Target with a Priority set. When not supplied, the new Task will have the priority of the matching Workflow Target. Value can be 0 to 2^31^ (2,147,483,647).
        :param task_channel: When MultiTasking is enabled, specify the TaskChannel by passing either its `unique_name` or `sid`. Default value is `default`.
        :param workflow_sid: The SID of the Workflow that you would like to handle routing for the new Task. If there is only one Workflow defined for the Workspace that you are posting the new task to, this parameter is optional.
        :param attributes: A URL-encoded JSON string with the attributes of the new task. This value is passed to the Workflow's `assignment_callback_url` when the Task is assigned to a Worker. For example: `{ \\\"task_type\\\": \\\"call\\\", \\\"twilio_call_sid\\\": \\\"CAxxx\\\", \\\"customer_ticket_number\\\": \\\"12345\\\" }`.

        :returns: The created TaskInstance
        """
        data = values.of(
            {
                "Timeout": timeout,
                "Priority": priority,
                "TaskChannel": task_channel,
                "WorkflowSid": workflow_sid,
                "Attributes": attributes,
            }
        )

        payload = await self._version.create_async(
            method="POST",
            uri=self._uri,
            data=data,
        )

        return TaskInstance(
            self._version, payload, workspace_sid=self._solution["workspace_sid"]
        )

    def stream(
        self,
        priority: Union[int, object] = values.unset,
        assignment_status: Union[List[str], object] = values.unset,
        workflow_sid: Union[str, object] = values.unset,
        workflow_name: Union[str, object] = values.unset,
        task_queue_sid: Union[str, object] = values.unset,
        task_queue_name: Union[str, object] = values.unset,
        evaluate_task_attributes: Union[str, object] = values.unset,
        ordering: Union[str, object] = values.unset,
        has_addons: Union[bool, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> Iterator[TaskInstance]:
        """
        Streams TaskInstance records from the API as a generator stream.
        This operation lazily loads records as efficiently as possible until the limit
        is reached.
        The results are returned as a generator, so this operation is memory efficient.

        :param int priority: The priority value of the Tasks to read. Returns the list of all Tasks in the Workspace with the specified priority.
        :param List[str] assignment_status: The `assignment_status` of the Tasks you want to read. Can be: `pending`, `reserved`, `assigned`, `canceled`, `wrapping`, or `completed`. Returns all Tasks in the Workspace with the specified `assignment_status`.
        :param str workflow_sid: The SID of the Workflow with the Tasks to read. Returns the Tasks controlled by the Workflow identified by this SID.
        :param str workflow_name: The friendly name of the Workflow with the Tasks to read. Returns the Tasks controlled by the Workflow identified by this friendly name.
        :param str task_queue_sid: The SID of the TaskQueue with the Tasks to read. Returns the Tasks waiting in the TaskQueue identified by this SID.
        :param str task_queue_name: The `friendly_name` of the TaskQueue with the Tasks to read. Returns the Tasks waiting in the TaskQueue identified by this friendly name.
        :param str evaluate_task_attributes: The attributes of the Tasks to read. Returns the Tasks that match the attributes specified in this parameter.
        :param str ordering: How to order the returned Task resources. y default, Tasks are sorted by ascending DateCreated. This value is specified as: `Attribute:Order`, where `Attribute` can be either `Priority` or `DateCreated` and `Order` can be either `asc` or `desc`. For example, `Priority:desc` returns Tasks ordered in descending order of their Priority. Multiple sort orders can be specified in a comma-separated list such as `Priority:desc,DateCreated:asc`, which returns the Tasks in descending Priority order and ascending DateCreated Order.
        :param bool has_addons: Whether to read Tasks with addons. If `true`, returns only Tasks with addons. If `false`, returns only Tasks without addons.
        :param limit: Upper limit for the number of records to return. stream()
                      guarantees to never return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, stream() will attempt to read the
                          limit with the most efficient page size, i.e. min(limit, 1000)

        :returns: Generator that will yield up to limit results
        """
        limits = self._version.read_limits(limit, page_size)
        page = self.page(
            priority=priority,
            assignment_status=assignment_status,
            workflow_sid=workflow_sid,
            workflow_name=workflow_name,
            task_queue_sid=task_queue_sid,
            task_queue_name=task_queue_name,
            evaluate_task_attributes=evaluate_task_attributes,
            ordering=ordering,
            has_addons=has_addons,
            page_size=limits["page_size"],
        )

        return self._version.stream(page, limits["limit"])

    async def stream_async(
        self,
        priority: Union[int, object] = values.unset,
        assignment_status: Union[List[str], object] = values.unset,
        workflow_sid: Union[str, object] = values.unset,
        workflow_name: Union[str, object] = values.unset,
        task_queue_sid: Union[str, object] = values.unset,
        task_queue_name: Union[str, object] = values.unset,
        evaluate_task_attributes: Union[str, object] = values.unset,
        ordering: Union[str, object] = values.unset,
        has_addons: Union[bool, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> AsyncIterator[TaskInstance]:
        """
        Asynchronously streams TaskInstance records from the API as a generator stream.
        This operation lazily loads records as efficiently as possible until the limit
        is reached.
        The results are returned as a generator, so this operation is memory efficient.

        :param int priority: The priority value of the Tasks to read. Returns the list of all Tasks in the Workspace with the specified priority.
        :param List[str] assignment_status: The `assignment_status` of the Tasks you want to read. Can be: `pending`, `reserved`, `assigned`, `canceled`, `wrapping`, or `completed`. Returns all Tasks in the Workspace with the specified `assignment_status`.
        :param str workflow_sid: The SID of the Workflow with the Tasks to read. Returns the Tasks controlled by the Workflow identified by this SID.
        :param str workflow_name: The friendly name of the Workflow with the Tasks to read. Returns the Tasks controlled by the Workflow identified by this friendly name.
        :param str task_queue_sid: The SID of the TaskQueue with the Tasks to read. Returns the Tasks waiting in the TaskQueue identified by this SID.
        :param str task_queue_name: The `friendly_name` of the TaskQueue with the Tasks to read. Returns the Tasks waiting in the TaskQueue identified by this friendly name.
        :param str evaluate_task_attributes: The attributes of the Tasks to read. Returns the Tasks that match the attributes specified in this parameter.
        :param str ordering: How to order the returned Task resources. y default, Tasks are sorted by ascending DateCreated. This value is specified as: `Attribute:Order`, where `Attribute` can be either `Priority` or `DateCreated` and `Order` can be either `asc` or `desc`. For example, `Priority:desc` returns Tasks ordered in descending order of their Priority. Multiple sort orders can be specified in a comma-separated list such as `Priority:desc,DateCreated:asc`, which returns the Tasks in descending Priority order and ascending DateCreated Order.
        :param bool has_addons: Whether to read Tasks with addons. If `true`, returns only Tasks with addons. If `false`, returns only Tasks without addons.
        :param limit: Upper limit for the number of records to return. stream()
                      guarantees to never return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, stream() will attempt to read the
                          limit with the most efficient page size, i.e. min(limit, 1000)

        :returns: Generator that will yield up to limit results
        """
        limits = self._version.read_limits(limit, page_size)
        page = await self.page_async(
            priority=priority,
            assignment_status=assignment_status,
            workflow_sid=workflow_sid,
            workflow_name=workflow_name,
            task_queue_sid=task_queue_sid,
            task_queue_name=task_queue_name,
            evaluate_task_attributes=evaluate_task_attributes,
            ordering=ordering,
            has_addons=has_addons,
            page_size=limits["page_size"],
        )

        return self._version.stream_async(page, limits["limit"])

    def list(
        self,
        priority: Union[int, object] = values.unset,
        assignment_status: Union[List[str], object] = values.unset,
        workflow_sid: Union[str, object] = values.unset,
        workflow_name: Union[str, object] = values.unset,
        task_queue_sid: Union[str, object] = values.unset,
        task_queue_name: Union[str, object] = values.unset,
        evaluate_task_attributes: Union[str, object] = values.unset,
        ordering: Union[str, object] = values.unset,
        has_addons: Union[bool, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> List[TaskInstance]:
        """
        Lists TaskInstance records from the API as a list.
        Unlike stream(), this operation is eager and will load `limit` records into
        memory before returning.

        :param int priority: The priority value of the Tasks to read. Returns the list of all Tasks in the Workspace with the specified priority.
        :param List[str] assignment_status: The `assignment_status` of the Tasks you want to read. Can be: `pending`, `reserved`, `assigned`, `canceled`, `wrapping`, or `completed`. Returns all Tasks in the Workspace with the specified `assignment_status`.
        :param str workflow_sid: The SID of the Workflow with the Tasks to read. Returns the Tasks controlled by the Workflow identified by this SID.
        :param str workflow_name: The friendly name of the Workflow with the Tasks to read. Returns the Tasks controlled by the Workflow identified by this friendly name.
        :param str task_queue_sid: The SID of the TaskQueue with the Tasks to read. Returns the Tasks waiting in the TaskQueue identified by this SID.
        :param str task_queue_name: The `friendly_name` of the TaskQueue with the Tasks to read. Returns the Tasks waiting in the TaskQueue identified by this friendly name.
        :param str evaluate_task_attributes: The attributes of the Tasks to read. Returns the Tasks that match the attributes specified in this parameter.
        :param str ordering: How to order the returned Task resources. y default, Tasks are sorted by ascending DateCreated. This value is specified as: `Attribute:Order`, where `Attribute` can be either `Priority` or `DateCreated` and `Order` can be either `asc` or `desc`. For example, `Priority:desc` returns Tasks ordered in descending order of their Priority. Multiple sort orders can be specified in a comma-separated list such as `Priority:desc,DateCreated:asc`, which returns the Tasks in descending Priority order and ascending DateCreated Order.
        :param bool has_addons: Whether to read Tasks with addons. If `true`, returns only Tasks with addons. If `false`, returns only Tasks without addons.
        :param limit: Upper limit for the number of records to return. list() guarantees
                      never to return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, list() will attempt to read the limit
                          with the most efficient page size, i.e. min(limit, 1000)

        :returns: list that will contain up to limit results
        """
        return list(
            self.stream(
                priority=priority,
                assignment_status=assignment_status,
                workflow_sid=workflow_sid,
                workflow_name=workflow_name,
                task_queue_sid=task_queue_sid,
                task_queue_name=task_queue_name,
                evaluate_task_attributes=evaluate_task_attributes,
                ordering=ordering,
                has_addons=has_addons,
                limit=limit,
                page_size=page_size,
            )
        )

    async def list_async(
        self,
        priority: Union[int, object] = values.unset,
        assignment_status: Union[List[str], object] = values.unset,
        workflow_sid: Union[str, object] = values.unset,
        workflow_name: Union[str, object] = values.unset,
        task_queue_sid: Union[str, object] = values.unset,
        task_queue_name: Union[str, object] = values.unset,
        evaluate_task_attributes: Union[str, object] = values.unset,
        ordering: Union[str, object] = values.unset,
        has_addons: Union[bool, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> List[TaskInstance]:
        """
        Asynchronously lists TaskInstance records from the API as a list.
        Unlike stream(), this operation is eager and will load `limit` records into
        memory before returning.

        :param int priority: The priority value of the Tasks to read. Returns the list of all Tasks in the Workspace with the specified priority.
        :param List[str] assignment_status: The `assignment_status` of the Tasks you want to read. Can be: `pending`, `reserved`, `assigned`, `canceled`, `wrapping`, or `completed`. Returns all Tasks in the Workspace with the specified `assignment_status`.
        :param str workflow_sid: The SID of the Workflow with the Tasks to read. Returns the Tasks controlled by the Workflow identified by this SID.
        :param str workflow_name: The friendly name of the Workflow with the Tasks to read. Returns the Tasks controlled by the Workflow identified by this friendly name.
        :param str task_queue_sid: The SID of the TaskQueue with the Tasks to read. Returns the Tasks waiting in the TaskQueue identified by this SID.
        :param str task_queue_name: The `friendly_name` of the TaskQueue with the Tasks to read. Returns the Tasks waiting in the TaskQueue identified by this friendly name.
        :param str evaluate_task_attributes: The attributes of the Tasks to read. Returns the Tasks that match the attributes specified in this parameter.
        :param str ordering: How to order the returned Task resources. y default, Tasks are sorted by ascending DateCreated. This value is specified as: `Attribute:Order`, where `Attribute` can be either `Priority` or `DateCreated` and `Order` can be either `asc` or `desc`. For example, `Priority:desc` returns Tasks ordered in descending order of their Priority. Multiple sort orders can be specified in a comma-separated list such as `Priority:desc,DateCreated:asc`, which returns the Tasks in descending Priority order and ascending DateCreated Order.
        :param bool has_addons: Whether to read Tasks with addons. If `true`, returns only Tasks with addons. If `false`, returns only Tasks without addons.
        :param limit: Upper limit for the number of records to return. list() guarantees
                      never to return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, list() will attempt to read the limit
                          with the most efficient page size, i.e. min(limit, 1000)

        :returns: list that will contain up to limit results
        """
        return [
            record
            async for record in await self.stream_async(
                priority=priority,
                assignment_status=assignment_status,
                workflow_sid=workflow_sid,
                workflow_name=workflow_name,
                task_queue_sid=task_queue_sid,
                task_queue_name=task_queue_name,
                evaluate_task_attributes=evaluate_task_attributes,
                ordering=ordering,
                has_addons=has_addons,
                limit=limit,
                page_size=page_size,
            )
        ]

    def page(
        self,
        priority: Union[int, object] = values.unset,
        assignment_status: Union[List[str], object] = values.unset,
        workflow_sid: Union[str, object] = values.unset,
        workflow_name: Union[str, object] = values.unset,
        task_queue_sid: Union[str, object] = values.unset,
        task_queue_name: Union[str, object] = values.unset,
        evaluate_task_attributes: Union[str, object] = values.unset,
        ordering: Union[str, object] = values.unset,
        has_addons: Union[bool, object] = values.unset,
        page_token: Union[str, object] = values.unset,
        page_number: Union[int, object] = values.unset,
        page_size: Union[int, object] = values.unset,
    ) -> TaskPage:
        """
        Retrieve a single page of TaskInstance records from the API.
        Request is executed immediately

        :param priority: The priority value of the Tasks to read. Returns the list of all Tasks in the Workspace with the specified priority.
        :param assignment_status: The `assignment_status` of the Tasks you want to read. Can be: `pending`, `reserved`, `assigned`, `canceled`, `wrapping`, or `completed`. Returns all Tasks in the Workspace with the specified `assignment_status`.
        :param workflow_sid: The SID of the Workflow with the Tasks to read. Returns the Tasks controlled by the Workflow identified by this SID.
        :param workflow_name: The friendly name of the Workflow with the Tasks to read. Returns the Tasks controlled by the Workflow identified by this friendly name.
        :param task_queue_sid: The SID of the TaskQueue with the Tasks to read. Returns the Tasks waiting in the TaskQueue identified by this SID.
        :param task_queue_name: The `friendly_name` of the TaskQueue with the Tasks to read. Returns the Tasks waiting in the TaskQueue identified by this friendly name.
        :param evaluate_task_attributes: The attributes of the Tasks to read. Returns the Tasks that match the attributes specified in this parameter.
        :param ordering: How to order the returned Task resources. y default, Tasks are sorted by ascending DateCreated. This value is specified as: `Attribute:Order`, where `Attribute` can be either `Priority` or `DateCreated` and `Order` can be either `asc` or `desc`. For example, `Priority:desc` returns Tasks ordered in descending order of their Priority. Multiple sort orders can be specified in a comma-separated list such as `Priority:desc,DateCreated:asc`, which returns the Tasks in descending Priority order and ascending DateCreated Order.
        :param has_addons: Whether to read Tasks with addons. If `true`, returns only Tasks with addons. If `false`, returns only Tasks without addons.
        :param page_token: PageToken provided by the API
        :param page_number: Page Number, this value is simply for client state
        :param page_size: Number of records to return, defaults to 50

        :returns: Page of TaskInstance
        """
        data = values.of(
            {
                "Priority": priority,
                "AssignmentStatus": serialize.map(assignment_status, lambda e: e),
                "WorkflowSid": workflow_sid,
                "WorkflowName": workflow_name,
                "TaskQueueSid": task_queue_sid,
                "TaskQueueName": task_queue_name,
                "EvaluateTaskAttributes": evaluate_task_attributes,
                "Ordering": ordering,
                "HasAddons": has_addons,
                "PageToken": page_token,
                "Page": page_number,
                "PageSize": page_size,
            }
        )

        response = self._version.page(method="GET", uri=self._uri, params=data)
        return TaskPage(self._version, response, self._solution)

    async def page_async(
        self,
        priority: Union[int, object] = values.unset,
        assignment_status: Union[List[str], object] = values.unset,
        workflow_sid: Union[str, object] = values.unset,
        workflow_name: Union[str, object] = values.unset,
        task_queue_sid: Union[str, object] = values.unset,
        task_queue_name: Union[str, object] = values.unset,
        evaluate_task_attributes: Union[str, object] = values.unset,
        ordering: Union[str, object] = values.unset,
        has_addons: Union[bool, object] = values.unset,
        page_token: Union[str, object] = values.unset,
        page_number: Union[int, object] = values.unset,
        page_size: Union[int, object] = values.unset,
    ) -> TaskPage:
        """
        Asynchronously retrieve a single page of TaskInstance records from the API.
        Request is executed immediately

        :param priority: The priority value of the Tasks to read. Returns the list of all Tasks in the Workspace with the specified priority.
        :param assignment_status: The `assignment_status` of the Tasks you want to read. Can be: `pending`, `reserved`, `assigned`, `canceled`, `wrapping`, or `completed`. Returns all Tasks in the Workspace with the specified `assignment_status`.
        :param workflow_sid: The SID of the Workflow with the Tasks to read. Returns the Tasks controlled by the Workflow identified by this SID.
        :param workflow_name: The friendly name of the Workflow with the Tasks to read. Returns the Tasks controlled by the Workflow identified by this friendly name.
        :param task_queue_sid: The SID of the TaskQueue with the Tasks to read. Returns the Tasks waiting in the TaskQueue identified by this SID.
        :param task_queue_name: The `friendly_name` of the TaskQueue with the Tasks to read. Returns the Tasks waiting in the TaskQueue identified by this friendly name.
        :param evaluate_task_attributes: The attributes of the Tasks to read. Returns the Tasks that match the attributes specified in this parameter.
        :param ordering: How to order the returned Task resources. y default, Tasks are sorted by ascending DateCreated. This value is specified as: `Attribute:Order`, where `Attribute` can be either `Priority` or `DateCreated` and `Order` can be either `asc` or `desc`. For example, `Priority:desc` returns Tasks ordered in descending order of their Priority. Multiple sort orders can be specified in a comma-separated list such as `Priority:desc,DateCreated:asc`, which returns the Tasks in descending Priority order and ascending DateCreated Order.
        :param has_addons: Whether to read Tasks with addons. If `true`, returns only Tasks with addons. If `false`, returns only Tasks without addons.
        :param page_token: PageToken provided by the API
        :param page_number: Page Number, this value is simply for client state
        :param page_size: Number of records to return, defaults to 50

        :returns: Page of TaskInstance
        """
        data = values.of(
            {
                "Priority": priority,
                "AssignmentStatus": serialize.map(assignment_status, lambda e: e),
                "WorkflowSid": workflow_sid,
                "WorkflowName": workflow_name,
                "TaskQueueSid": task_queue_sid,
                "TaskQueueName": task_queue_name,
                "EvaluateTaskAttributes": evaluate_task_attributes,
                "Ordering": ordering,
                "HasAddons": has_addons,
                "PageToken": page_token,
                "Page": page_number,
                "PageSize": page_size,
            }
        )

        response = await self._version.page_async(
            method="GET", uri=self._uri, params=data
        )
        return TaskPage(self._version, response, self._solution)

    def get_page(self, target_url: str) -> TaskPage:
        """
        Retrieve a specific page of TaskInstance records from the API.
        Request is executed immediately

        :param target_url: API-generated URL for the requested results page

        :returns: Page of TaskInstance
        """
        response = self._version.domain.twilio.request("GET", target_url)
        return TaskPage(self._version, response, self._solution)

    async def get_page_async(self, target_url: str) -> TaskPage:
        """
        Asynchronously retrieve a specific page of TaskInstance records from the API.
        Request is executed immediately

        :param target_url: API-generated URL for the requested results page

        :returns: Page of TaskInstance
        """
        response = await self._version.domain.twilio.request_async("GET", target_url)
        return TaskPage(self._version, response, self._solution)

    def get(self, sid: str) -> TaskContext:
        """
        Constructs a TaskContext

        :param sid: The SID of the Task resource to update.
        """
        return TaskContext(
            self._version, workspace_sid=self._solution["workspace_sid"], sid=sid
        )

    def __call__(self, sid: str) -> TaskContext:
        """
        Constructs a TaskContext

        :param sid: The SID of the Task resource to update.
        """
        return TaskContext(
            self._version, workspace_sid=self._solution["workspace_sid"], sid=sid
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Taskrouter.V1.TaskList>"
