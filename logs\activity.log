[2025-06-02 14:34:27,717] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | REQUEST: GET /settings/ | Extra: user_agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
[2025-06-02 14:34:27,732] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | RESPONSE: GET /settings/ -> 200
[2025-06-02 14:35:40,209] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | REQUEST: GET /settings/ | Extra: user_agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
[2025-06-02 14:35:40,225] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | RESPONSE: GET /settings/ -> 200
[2025-06-02 14:35:41,347] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | REQUEST: GET /settings/ | Extra: user_agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
[2025-06-02 14:35:41,349] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | RESPONSE: GET /settings/ -> 200
[2025-06-02 14:35:42,736] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | REQUEST: GET /settings/logs | Extra: user_agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
[2025-06-02 14:35:42,743] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | RESPONSE: GET /settings/logs -> 200
[2025-06-02 14:35:50,757] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | REQUEST: GET /settings/logs/activity.log/view | Extra: user_agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
[2025-06-02 14:35:50,776] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | RESPONSE: GET /settings/logs/activity.log/view -> 200
[2025-06-02 14:36:06,717] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | REQUEST: GET /settings/logs | Extra: user_agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
[2025-06-02 14:36:06,723] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | RESPONSE: GET /settings/logs -> 200
[2025-06-02 14:36:07,546] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | REQUEST: GET /settings/logs | Extra: user_agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
[2025-06-02 14:36:07,547] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | RESPONSE: GET /settings/logs -> 200
[2025-06-02 14:36:09,077] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | REQUEST: GET /training/ | Extra: user_agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
[2025-06-02 14:36:09,123] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | RESPONSE: GET /training/ -> 200
[2025-06-02 14:36:11,571] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | REQUEST: GET /training/4 | Extra: user_agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
[2025-06-02 14:36:11,612] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | RESPONSE: GET /training/4 -> 200
[2025-06-02 14:36:11,675] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | REQUEST: GET /material/api/training/4/materials | Extra: user_agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
[2025-06-02 14:36:11,677] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | RESPONSE: GET /material/api/training/4/materials -> 200
[2025-06-02 14:36:15,069] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | REQUEST: POST /training/4/remove-participant/18 | Extra: user_agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
[2025-06-02 14:36:15,084] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | RESPONSE: POST /training/4/remove-participant/18 -> 200
[2025-06-02 14:36:16,023] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | REQUEST: GET /training/4 | Extra: user_agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
[2025-06-02 14:36:16,077] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | RESPONSE: GET /training/4 -> 200
[2025-06-02 14:36:16,141] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | REQUEST: GET /material/api/training/4/materials | Extra: user_agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
[2025-06-02 14:36:16,143] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | RESPONSE: GET /material/api/training/4/materials -> 200
[2025-06-02 14:36:19,466] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | REQUEST: GET /settings/ | Extra: user_agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
[2025-06-02 14:36:19,473] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | RESPONSE: GET /settings/ -> 200
[2025-06-02 14:36:20,472] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | REQUEST: GET /settings/logs | Extra: user_agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
[2025-06-02 14:36:20,479] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | RESPONSE: GET /settings/logs -> 200
[2025-06-02 14:36:22,628] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | REQUEST: GET /settings/logs/activity.log/view | Extra: user_agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
[2025-06-02 14:36:22,638] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | RESPONSE: GET /settings/logs/activity.log/view -> 200
[2025-06-02 14:37:04,603] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | REQUEST: GET /settings/ | Extra: user_agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
[2025-06-02 14:37:04,618] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | RESPONSE: GET /settings/ -> 200
[2025-06-02 14:37:06,444] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | REQUEST: GET /training/ | Extra: user_agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
[2025-06-02 14:37:06,493] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | RESPONSE: GET /training/ -> 200
[2025-06-02 14:37:26,074] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | REQUEST: GET /training/9 | Extra: user_agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
[2025-06-02 14:37:26,136] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | RESPONSE: GET /training/9 -> 200
[2025-06-02 14:37:26,227] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | REQUEST: GET /material/api/training/9/materials | Extra: user_agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
[2025-06-02 14:37:26,229] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | RESPONSE: GET /material/api/training/9/materials -> 200
[2025-06-02 14:38:00,134] ACTIVITY: [ACTIVITY] | User: Anonymous | IP: 127.0.0.1 | REQUEST: GET /settings/logs | Extra: user_agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
[2025-06-02 14:38:00,135] ACTIVITY: [ACTIVITY] | User: Anonymous | IP: 127.0.0.1 | RESPONSE: GET /settings/logs -> 302
[2025-06-02 14:38:00,142] ACTIVITY: [ACTIVITY] | User: Anonymous | IP: 127.0.0.1 | REQUEST: GET /auth/login | Extra: user_agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
[2025-06-02 14:38:00,152] ACTIVITY: [ACTIVITY] | User: Anonymous | IP: 127.0.0.1 | RESPONSE: GET /auth/login -> 200
[2025-06-02 14:38:00,227] ACTIVITY: [ACTIVITY] | User: Anonymous | IP: 127.0.0.1 | REQUEST: GET /favicon.ico | Extra: user_agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
[2025-06-02 14:38:00,228] ACTIVITY: [ACTIVITY] | User: Anonymous | IP: 127.0.0.1 | RESPONSE: GET /favicon.ico -> 404
[2025-06-02 14:38:03,132] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | REQUEST: GET /training/9 | Extra: user_agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
[2025-06-02 14:38:03,178] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | RESPONSE: GET /training/9 -> 200
[2025-06-02 14:38:03,302] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | REQUEST: GET /material/api/training/9/materials | Extra: user_agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
[2025-06-02 14:38:03,304] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | RESPONSE: GET /material/api/training/9/materials -> 200
[2025-06-02 14:38:05,993] ACTIVITY: [ACTIVITY] | User: Anonymous | IP: 127.0.0.1 | REQUEST: GET /auth/logout | Extra: user_agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
[2025-06-02 14:38:05,995] ACTIVITY: [ACTIVITY] | User: Anonymous | IP: 127.0.0.1 | RESPONSE: GET /auth/logout -> 302
[2025-06-02 14:38:06,002] ACTIVITY: [ACTIVITY] | User: Anonymous | IP: 127.0.0.1 | REQUEST: GET /auth/login | Extra: user_agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
[2025-06-02 14:38:06,002] ACTIVITY: [ACTIVITY] | User: Anonymous | IP: 127.0.0.1 | RESPONSE: GET /auth/login -> 200
[2025-06-02 14:38:07,456] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | REQUEST: POST /training/9/remove-participant/28 | Extra: user_agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
[2025-06-02 14:38:07,470] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | RESPONSE: POST /training/9/remove-participant/28 -> 200
[2025-06-02 14:38:08,875] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | REQUEST: GET /training/9 | Extra: user_agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
[2025-06-02 14:38:08,884] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | RESPONSE: GET /training/9 -> 200
[2025-06-02 14:38:08,988] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | REQUEST: GET /material/api/training/9/materials | Extra: user_agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
[2025-06-02 14:38:08,990] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | RESPONSE: GET /material/api/training/9/materials -> 200
[2025-06-02 14:38:11,234] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | REQUEST: GET /settings/ | Extra: user_agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
[2025-06-02 14:38:11,240] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | RESPONSE: GET /settings/ -> 200
[2025-06-02 14:38:12,924] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | REQUEST: GET /settings/logs | Extra: user_agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
[2025-06-02 14:38:12,930] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | RESPONSE: GET /settings/logs -> 200
[2025-06-02 14:38:15,250] ACTIVITY: [ACTIVITY] | User: Anonymous | IP: 127.0.0.1 | REQUEST: GET /auth/login | Extra: user_agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
[2025-06-02 14:38:15,251] ACTIVITY: [ACTIVITY] | User: Anonymous | IP: 127.0.0.1 | RESPONSE: GET /auth/login -> 200
[2025-06-02 14:38:20,940] ACTIVITY: [ACTIVITY] | User: Anonymous | IP: 127.0.0.1 | REQUEST: GET /settings/logs | Extra: user_agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
[2025-06-02 14:38:20,943] ACTIVITY: [ACTIVITY] | User: Anonymous | IP: 127.0.0.1 | RESPONSE: GET /settings/logs -> 302
[2025-06-02 14:38:20,948] ACTIVITY: [ACTIVITY] | User: Anonymous | IP: 127.0.0.1 | REQUEST: GET /auth/login | Extra: user_agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
[2025-06-02 14:38:20,949] ACTIVITY: [ACTIVITY] | User: Anonymous | IP: 127.0.0.1 | RESPONSE: GET /auth/login -> 200
[2025-06-02 14:38:24,479] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | REQUEST: GET /settings/logs/activity.log/view | Extra: user_agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
[2025-06-02 14:38:24,489] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | RESPONSE: GET /settings/logs/activity.log/view -> 200
[2025-06-02 14:38:48,620] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | REQUEST: GET /settings/logs | Extra: user_agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
[2025-06-02 14:38:48,634] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | RESPONSE: GET /settings/logs -> 200
[2025-06-02 14:38:53,536] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | REQUEST: GET /auth/logout | Extra: user_agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
[2025-06-02 14:38:53,537] ACTIVITY: [ACTIVITY] | User: Anonymous | IP: ************* | RESPONSE: GET /auth/logout -> 302
[2025-06-02 14:38:53,545] ACTIVITY: [ACTIVITY] | User: Anonymous | IP: ************* | REQUEST: GET /auth/login | Extra: user_agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
[2025-06-02 14:38:53,546] ACTIVITY: [ACTIVITY] | User: Anonymous | IP: ************* | RESPONSE: GET /auth/login -> 200
[2025-06-02 14:38:56,234] ACTIVITY: [ACTIVITY] | User: Anonymous | IP: ************* | REQUEST: POST /auth/login | Extra: user_agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
[2025-06-02 14:38:56,497] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | RESPONSE: POST /auth/login -> 302
[2025-06-02 14:38:56,505] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | REQUEST: GET /dashboard | Extra: user_agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
[2025-06-02 14:38:56,533] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | RESPONSE: GET /dashboard -> 200
[2025-06-02 14:39:00,490] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | REQUEST: GET /settings/ | Extra: user_agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
[2025-06-02 14:39:00,496] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | RESPONSE: GET /settings/ -> 200
[2025-06-02 14:39:01,533] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | REQUEST: GET /settings/logs | Extra: user_agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
[2025-06-02 14:39:01,534] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | RESPONSE: GET /settings/logs -> 200
[2025-06-02 14:39:03,344] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | REQUEST: GET /settings/logs/security.log/view | Extra: user_agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
[2025-06-02 14:39:03,345] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | RESPONSE: GET /settings/logs/security.log/view -> 200
[2025-06-02 14:39:23,274] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | REQUEST: GET /settings/ | Extra: user_agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
[2025-06-02 14:39:23,275] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | RESPONSE: GET /settings/ -> 200
[2025-06-02 14:39:25,108] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | REQUEST: GET /settings/category/email | Extra: user_agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
[2025-06-02 14:39:25,121] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | RESPONSE: GET /settings/category/email -> 200
[2025-06-02 14:39:26,019] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | REQUEST: POST /settings/category/email/save | Extra: user_agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
[2025-06-02 14:39:26,036] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | RESPONSE: POST /settings/category/email/save -> 200
[2025-06-02 14:39:27,007] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | REQUEST: GET /settings/category/email | Extra: user_agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
[2025-06-02 14:39:27,009] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | RESPONSE: GET /settings/category/email -> 200
[2025-06-02 14:39:28,322] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | REQUEST: GET /settings/ | Extra: user_agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
[2025-06-02 14:39:28,323] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | RESPONSE: GET /settings/ -> 200
[2025-06-02 14:39:29,529] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | REQUEST: GET /settings/logs | Extra: user_agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
[2025-06-02 14:39:29,531] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | RESPONSE: GET /settings/logs -> 200
[2025-06-02 14:39:31,704] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | REQUEST: GET /settings/logs/security.log/view | Extra: user_agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
[2025-06-02 14:39:31,705] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | RESPONSE: GET /settings/logs/security.log/view -> 200
[2025-06-02 14:39:36,126] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | REQUEST: GET /settings/logs/activity.log/view | Extra: user_agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
[2025-06-02 14:39:36,138] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | RESPONSE: GET /settings/logs/activity.log/view -> 200
[2025-06-02 14:39:49,591] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | REQUEST: GET /settings/ | Extra: user_agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
[2025-06-02 14:39:49,593] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | RESPONSE: GET /settings/ -> 200
[2025-06-02 14:39:50,901] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | REQUEST: GET /settings/system-info | Extra: user_agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
[2025-06-02 14:39:50,946] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | RESPONSE: GET /settings/system-info -> 200
[2025-06-02 14:40:02,268] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | REQUEST: GET /settings/ | Extra: user_agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
[2025-06-02 14:40:02,270] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | RESPONSE: GET /settings/ -> 200
[2025-06-02 14:44:52,530] ACTIVITY: [ACTIVITY] | User: Anonymous | IP: ************* | REQUEST: GET /auth/users | Extra: user_agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
[2025-06-02 14:44:52,530] ACTIVITY: [ACTIVITY] | User: Anonymous | IP: ************* | RESPONSE: GET /auth/users -> 302
[2025-06-02 14:44:52,550] ACTIVITY: [ACTIVITY] | User: Anonymous | IP: ************* | REQUEST: GET /auth/login | Extra: user_agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
[2025-06-02 14:44:52,550] ACTIVITY: [ACTIVITY] | User: Anonymous | IP: ************* | RESPONSE: GET /auth/login -> 200
[2025-06-02 14:44:55,656] ACTIVITY: [ACTIVITY] | User: Anonymous | IP: ************* | REQUEST: POST /auth/login | Extra: user_agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
[2025-06-02 14:44:55,913] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | RESPONSE: POST /auth/login -> 302
[2025-06-02 14:44:55,923] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | REQUEST: GET /auth/users | Extra: user_agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
[2025-06-02 14:44:55,952] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | RESPONSE: GET /auth/users -> 200
[2025-06-02 14:45:04,903] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | REQUEST: GET /auth/users | Extra: user_agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
[2025-06-02 14:45:04,908] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | RESPONSE: GET /auth/users -> 200
[2025-06-02 15:38:52,902] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | REQUEST: GET /settings/ | Extra: user_agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
[2025-06-02 15:38:52,903] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | RESPONSE: GET /settings/ -> 200
[2025-06-02 15:38:54,467] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | REQUEST: GET /training/ | Extra: user_agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
[2025-06-02 15:38:54,509] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | RESPONSE: GET /training/ -> 200
[2025-06-02 15:39:39,694] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | REQUEST: GET /training/ | Extra: user_agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
[2025-06-02 15:39:39,720] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | RESPONSE: GET /training/ -> 200
[2025-06-02 15:39:57,284] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | REQUEST: GET /training/ | Extra: user_agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
[2025-06-02 15:39:57,288] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | RESPONSE: GET /training/ -> 200
[2025-06-02 15:39:59,751] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | REQUEST: GET /training/ | Extra: user_agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
[2025-06-02 15:39:59,776] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | RESPONSE: GET /training/ -> 200
[2025-06-02 15:42:58,727] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | REQUEST: GET /training/ | Extra: user_agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
[2025-06-02 15:42:58,752] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | RESPONSE: GET /training/ -> 200
[2025-06-02 15:53:41,008] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | REQUEST: GET /company/ | Extra: user_agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
[2025-06-02 15:53:41,040] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | RESPONSE: GET /company/ -> 200
[2025-06-02 15:54:06,556] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | REQUEST: GET /settings/ | Extra: user_agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
[2025-06-02 15:54:06,558] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | RESPONSE: GET /settings/ -> 200
[2025-06-02 15:54:08,442] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | REQUEST: GET /settings/category/sms | Extra: user_agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
[2025-06-02 15:54:08,444] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | RESPONSE: GET /settings/category/sms -> 200
[2025-06-02 15:54:16,368] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | REQUEST: GET /settings/ | Extra: user_agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
[2025-06-02 15:54:16,370] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | RESPONSE: GET /settings/ -> 200
[2025-06-02 15:54:17,093] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | REQUEST: GET /settings/category/email | Extra: user_agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
[2025-06-02 15:54:17,095] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | RESPONSE: GET /settings/category/email -> 200
[2025-06-02 15:54:19,885] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | REQUEST: GET /settings/ | Extra: user_agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
[2025-06-02 15:54:19,886] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | RESPONSE: GET /settings/ -> 200
[2025-06-02 15:55:17,270] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | REQUEST: GET /settings/category/dashboard | Extra: user_agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
[2025-06-02 15:55:17,272] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | RESPONSE: GET /settings/category/dashboard -> 200
[2025-06-02 15:55:20,121] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | REQUEST: GET /settings/ | Extra: user_agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
[2025-06-02 15:55:20,123] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | RESPONSE: GET /settings/ -> 200
[2025-06-02 15:55:35,219] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | REQUEST: GET /training/ | Extra: user_agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
[2025-06-02 15:55:35,244] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | RESPONSE: GET /training/ -> 200
[2025-06-02 15:55:40,852] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | REQUEST: GET /training/ | Extra: user_agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
[2025-06-02 15:55:40,878] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | RESPONSE: GET /training/ -> 200
[2025-06-02 15:55:42,585] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | REQUEST: GET /training/9 | Extra: user_agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
[2025-06-02 15:55:42,622] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | RESPONSE: GET /training/9 -> 200
[2025-06-02 15:55:42,675] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | REQUEST: GET /material/api/training/9/materials | Extra: user_agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
[2025-06-02 15:55:42,677] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | RESPONSE: GET /material/api/training/9/materials -> 200
[2025-06-02 15:55:56,560] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | REQUEST: GET /material/4/view | Extra: user_agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
[2025-06-02 15:55:56,563] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | RESPONSE: GET /material/4/view -> 302
[2025-06-02 15:55:56,571] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | REQUEST: GET /material/training/9 | Extra: user_agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
[2025-06-02 15:55:56,582] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | RESPONSE: GET /material/training/9 -> 200
[2025-06-02 15:55:59,402] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | REQUEST: GET /material/training/9 | Extra: user_agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
[2025-06-02 15:55:59,405] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | RESPONSE: GET /material/training/9 -> 200
[2025-06-02 15:56:08,168] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | REQUEST: GET /material/4/stream | Extra: user_agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
[2025-06-02 15:56:08,169] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | RESPONSE: GET /material/4/stream -> 206
[2025-06-02 15:57:08,322] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | REQUEST: GET /settings/ | Extra: user_agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
[2025-06-02 15:57:08,323] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | RESPONSE: GET /settings/ -> 200
[2025-06-02 15:57:11,017] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | REQUEST: GET /settings/logs | Extra: user_agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
[2025-06-02 15:57:11,018] ACTIVITY: [ACTIVITY] | User: <EMAIL> (1) | IP: ************* | RESPONSE: GET /settings/logs -> 200
