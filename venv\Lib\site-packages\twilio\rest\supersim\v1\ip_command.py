r"""
    This code was generated by
   ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
    |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
    |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

    Twilio - Supersim
    This is the public Twilio REST API.

    NOTE: This class is auto generated by OpenAPI Generator.
    https://openapi-generator.tech
    Do not edit the class manually.
"""


from datetime import datetime
from typing import Any, Dict, List, Optional, Union, Iterator, AsyncIterator
from twilio.base import deserialize, values
from twilio.base.instance_context import InstanceContext
from twilio.base.instance_resource import InstanceResource
from twilio.base.list_resource import ListResource
from twilio.base.version import Version
from twilio.base.page import Page


class IpCommandInstance(InstanceResource):
    class Direction(object):
        TO_SIM = "to_sim"
        FROM_SIM = "from_sim"

    class PayloadType(object):
        TEXT = "text"
        BINARY = "binary"

    class Status(object):
        QUEUED = "queued"
        SENT = "sent"
        RECEIVED = "received"
        FAILED = "failed"

    """
    :ivar sid: The unique string that we created to identify the IP Command resource.
    :ivar account_sid: The SID of the [Account](https://www.twilio.com/docs/iam/api/account) that created the IP Command resource.
    :ivar sim_sid: The SID of the [Super SIM](https://www.twilio.com/docs/iot/supersim/api/sim-resource) that this IP Command was sent to or from.
    :ivar sim_iccid: The [ICCID](https://en.wikipedia.org/wiki/Subscriber_identity_module#ICCID) of the [Super SIM](https://www.twilio.com/docs/iot/supersim/api/sim-resource) that this IP Command was sent to or from.
    :ivar status: 
    :ivar direction: 
    :ivar device_ip: The IP address of the device that the IP Command was sent to or received from. For an IP Command sent to a Super SIM, `device_ip` starts out as `null`, and once the IP Command is “sent”, the `device_ip` will be filled out. An IP Command sent from a Super SIM have its `device_ip` always set.
    :ivar device_port: For an IP Command sent to a Super SIM, it would be the destination port of the IP message. For an IP Command sent from a Super SIM, it would be the source port of the IP message.
    :ivar payload_type: 
    :ivar payload: The payload that is carried in the IP/UDP message. The payload can be encoded in either text or binary format. For text payload, UTF-8 encoding must be used.  For an IP Command sent to a Super SIM, the payload is appended to the IP/UDP message “as is”. The payload should not exceed 1300 bytes.  For an IP Command sent from a Super SIM, the payload from the received IP/UDP message is extracted and sent in binary encoding. For an IP Command sent from a Super SIM, the payload should not exceed 1300 bytes. If it is larger than 1300 bytes, there might be fragmentation on the upstream and the message may appear truncated.
    :ivar date_created: The date and time in GMT when the resource was created specified in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format.
    :ivar date_updated: The date and time in GMT when the resource was last updated specified in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format.
    :ivar url: The absolute URL of the IP Command resource.
    """

    def __init__(
        self, version: Version, payload: Dict[str, Any], sid: Optional[str] = None
    ):
        super().__init__(version)

        self.sid: Optional[str] = payload.get("sid")
        self.account_sid: Optional[str] = payload.get("account_sid")
        self.sim_sid: Optional[str] = payload.get("sim_sid")
        self.sim_iccid: Optional[str] = payload.get("sim_iccid")
        self.status: Optional["IpCommandInstance.Status"] = payload.get("status")
        self.direction: Optional["IpCommandInstance.Direction"] = payload.get(
            "direction"
        )
        self.device_ip: Optional[str] = payload.get("device_ip")
        self.device_port: Optional[int] = deserialize.integer(
            payload.get("device_port")
        )
        self.payload_type: Optional["IpCommandInstance.PayloadType"] = payload.get(
            "payload_type"
        )
        self.payload: Optional[str] = payload.get("payload")
        self.date_created: Optional[datetime] = deserialize.iso8601_datetime(
            payload.get("date_created")
        )
        self.date_updated: Optional[datetime] = deserialize.iso8601_datetime(
            payload.get("date_updated")
        )
        self.url: Optional[str] = payload.get("url")

        self._solution = {
            "sid": sid or self.sid,
        }
        self._context: Optional[IpCommandContext] = None

    @property
    def _proxy(self) -> "IpCommandContext":
        """
        Generate an instance context for the instance, the context is capable of
        performing various actions. All instance actions are proxied to the context

        :returns: IpCommandContext for this IpCommandInstance
        """
        if self._context is None:
            self._context = IpCommandContext(
                self._version,
                sid=self._solution["sid"],
            )
        return self._context

    def fetch(self) -> "IpCommandInstance":
        """
        Fetch the IpCommandInstance


        :returns: The fetched IpCommandInstance
        """
        return self._proxy.fetch()

    async def fetch_async(self) -> "IpCommandInstance":
        """
        Asynchronous coroutine to fetch the IpCommandInstance


        :returns: The fetched IpCommandInstance
        """
        return await self._proxy.fetch_async()

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Supersim.V1.IpCommandInstance {}>".format(context)


class IpCommandContext(InstanceContext):
    def __init__(self, version: Version, sid: str):
        """
        Initialize the IpCommandContext

        :param version: Version that contains the resource
        :param sid: The SID of the IP Command resource to fetch.
        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "sid": sid,
        }
        self._uri = "/IpCommands/{sid}".format(**self._solution)

    def fetch(self) -> IpCommandInstance:
        """
        Fetch the IpCommandInstance


        :returns: The fetched IpCommandInstance
        """

        payload = self._version.fetch(
            method="GET",
            uri=self._uri,
        )

        return IpCommandInstance(
            self._version,
            payload,
            sid=self._solution["sid"],
        )

    async def fetch_async(self) -> IpCommandInstance:
        """
        Asynchronous coroutine to fetch the IpCommandInstance


        :returns: The fetched IpCommandInstance
        """

        payload = await self._version.fetch_async(
            method="GET",
            uri=self._uri,
        )

        return IpCommandInstance(
            self._version,
            payload,
            sid=self._solution["sid"],
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Supersim.V1.IpCommandContext {}>".format(context)


class IpCommandPage(Page):
    def get_instance(self, payload: Dict[str, Any]) -> IpCommandInstance:
        """
        Build an instance of IpCommandInstance

        :param payload: Payload response from the API
        """
        return IpCommandInstance(self._version, payload)

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Supersim.V1.IpCommandPage>"


class IpCommandList(ListResource):
    def __init__(self, version: Version):
        """
        Initialize the IpCommandList

        :param version: Version that contains the resource

        """
        super().__init__(version)

        self._uri = "/IpCommands"

    def create(
        self,
        sim: str,
        payload: str,
        device_port: int,
        payload_type: Union["IpCommandInstance.PayloadType", object] = values.unset,
        callback_url: Union[str, object] = values.unset,
        callback_method: Union[str, object] = values.unset,
    ) -> IpCommandInstance:
        """
        Create the IpCommandInstance

        :param sim: The `sid` or `unique_name` of the [Super SIM](https://www.twilio.com/docs/iot/supersim/api/sim-resource) to send the IP Command to.
        :param payload: The data that will be sent to the device. The payload cannot exceed 1300 bytes. If the PayloadType is set to text, the payload is encoded in UTF-8. If PayloadType is set to binary, the payload is encoded in Base64.
        :param device_port: The device port to which the IP Command will be sent.
        :param payload_type:
        :param callback_url: The URL we should call using the `callback_method` after we have sent the IP Command.
        :param callback_method: The HTTP method we should use to call `callback_url`. Can be `GET` or `POST`, and the default is `POST`.

        :returns: The created IpCommandInstance
        """
        data = values.of(
            {
                "Sim": sim,
                "Payload": payload,
                "DevicePort": device_port,
                "PayloadType": payload_type,
                "CallbackUrl": callback_url,
                "CallbackMethod": callback_method,
            }
        )

        payload = self._version.create(
            method="POST",
            uri=self._uri,
            data=data,
        )

        return IpCommandInstance(self._version, payload)

    async def create_async(
        self,
        sim: str,
        payload: str,
        device_port: int,
        payload_type: Union["IpCommandInstance.PayloadType", object] = values.unset,
        callback_url: Union[str, object] = values.unset,
        callback_method: Union[str, object] = values.unset,
    ) -> IpCommandInstance:
        """
        Asynchronously create the IpCommandInstance

        :param sim: The `sid` or `unique_name` of the [Super SIM](https://www.twilio.com/docs/iot/supersim/api/sim-resource) to send the IP Command to.
        :param payload: The data that will be sent to the device. The payload cannot exceed 1300 bytes. If the PayloadType is set to text, the payload is encoded in UTF-8. If PayloadType is set to binary, the payload is encoded in Base64.
        :param device_port: The device port to which the IP Command will be sent.
        :param payload_type:
        :param callback_url: The URL we should call using the `callback_method` after we have sent the IP Command.
        :param callback_method: The HTTP method we should use to call `callback_url`. Can be `GET` or `POST`, and the default is `POST`.

        :returns: The created IpCommandInstance
        """
        data = values.of(
            {
                "Sim": sim,
                "Payload": payload,
                "DevicePort": device_port,
                "PayloadType": payload_type,
                "CallbackUrl": callback_url,
                "CallbackMethod": callback_method,
            }
        )

        payload = await self._version.create_async(
            method="POST",
            uri=self._uri,
            data=data,
        )

        return IpCommandInstance(self._version, payload)

    def stream(
        self,
        sim: Union[str, object] = values.unset,
        sim_iccid: Union[str, object] = values.unset,
        status: Union["IpCommandInstance.Status", object] = values.unset,
        direction: Union["IpCommandInstance.Direction", object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> Iterator[IpCommandInstance]:
        """
        Streams IpCommandInstance records from the API as a generator stream.
        This operation lazily loads records as efficiently as possible until the limit
        is reached.
        The results are returned as a generator, so this operation is memory efficient.

        :param str sim: The SID or unique name of the Sim resource that IP Command was sent to or from.
        :param str sim_iccid: The ICCID of the Sim resource that IP Command was sent to or from.
        :param &quot;IpCommandInstance.Status&quot; status: The status of the IP Command. Can be: `queued`, `sent`, `received` or `failed`. See the [IP Command Status Values](https://www.twilio.com/docs/iot/supersim/api/ipcommand-resource#status-values) for a description of each.
        :param &quot;IpCommandInstance.Direction&quot; direction: The direction of the IP Command. Can be `to_sim` or `from_sim`. The value of `to_sim` is synonymous with the term `mobile terminated`, and `from_sim` is synonymous with the term `mobile originated`.
        :param limit: Upper limit for the number of records to return. stream()
                      guarantees to never return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, stream() will attempt to read the
                          limit with the most efficient page size, i.e. min(limit, 1000)

        :returns: Generator that will yield up to limit results
        """
        limits = self._version.read_limits(limit, page_size)
        page = self.page(
            sim=sim,
            sim_iccid=sim_iccid,
            status=status,
            direction=direction,
            page_size=limits["page_size"],
        )

        return self._version.stream(page, limits["limit"])

    async def stream_async(
        self,
        sim: Union[str, object] = values.unset,
        sim_iccid: Union[str, object] = values.unset,
        status: Union["IpCommandInstance.Status", object] = values.unset,
        direction: Union["IpCommandInstance.Direction", object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> AsyncIterator[IpCommandInstance]:
        """
        Asynchronously streams IpCommandInstance records from the API as a generator stream.
        This operation lazily loads records as efficiently as possible until the limit
        is reached.
        The results are returned as a generator, so this operation is memory efficient.

        :param str sim: The SID or unique name of the Sim resource that IP Command was sent to or from.
        :param str sim_iccid: The ICCID of the Sim resource that IP Command was sent to or from.
        :param &quot;IpCommandInstance.Status&quot; status: The status of the IP Command. Can be: `queued`, `sent`, `received` or `failed`. See the [IP Command Status Values](https://www.twilio.com/docs/iot/supersim/api/ipcommand-resource#status-values) for a description of each.
        :param &quot;IpCommandInstance.Direction&quot; direction: The direction of the IP Command. Can be `to_sim` or `from_sim`. The value of `to_sim` is synonymous with the term `mobile terminated`, and `from_sim` is synonymous with the term `mobile originated`.
        :param limit: Upper limit for the number of records to return. stream()
                      guarantees to never return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, stream() will attempt to read the
                          limit with the most efficient page size, i.e. min(limit, 1000)

        :returns: Generator that will yield up to limit results
        """
        limits = self._version.read_limits(limit, page_size)
        page = await self.page_async(
            sim=sim,
            sim_iccid=sim_iccid,
            status=status,
            direction=direction,
            page_size=limits["page_size"],
        )

        return self._version.stream_async(page, limits["limit"])

    def list(
        self,
        sim: Union[str, object] = values.unset,
        sim_iccid: Union[str, object] = values.unset,
        status: Union["IpCommandInstance.Status", object] = values.unset,
        direction: Union["IpCommandInstance.Direction", object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> List[IpCommandInstance]:
        """
        Lists IpCommandInstance records from the API as a list.
        Unlike stream(), this operation is eager and will load `limit` records into
        memory before returning.

        :param str sim: The SID or unique name of the Sim resource that IP Command was sent to or from.
        :param str sim_iccid: The ICCID of the Sim resource that IP Command was sent to or from.
        :param &quot;IpCommandInstance.Status&quot; status: The status of the IP Command. Can be: `queued`, `sent`, `received` or `failed`. See the [IP Command Status Values](https://www.twilio.com/docs/iot/supersim/api/ipcommand-resource#status-values) for a description of each.
        :param &quot;IpCommandInstance.Direction&quot; direction: The direction of the IP Command. Can be `to_sim` or `from_sim`. The value of `to_sim` is synonymous with the term `mobile terminated`, and `from_sim` is synonymous with the term `mobile originated`.
        :param limit: Upper limit for the number of records to return. list() guarantees
                      never to return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, list() will attempt to read the limit
                          with the most efficient page size, i.e. min(limit, 1000)

        :returns: list that will contain up to limit results
        """
        return list(
            self.stream(
                sim=sim,
                sim_iccid=sim_iccid,
                status=status,
                direction=direction,
                limit=limit,
                page_size=page_size,
            )
        )

    async def list_async(
        self,
        sim: Union[str, object] = values.unset,
        sim_iccid: Union[str, object] = values.unset,
        status: Union["IpCommandInstance.Status", object] = values.unset,
        direction: Union["IpCommandInstance.Direction", object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> List[IpCommandInstance]:
        """
        Asynchronously lists IpCommandInstance records from the API as a list.
        Unlike stream(), this operation is eager and will load `limit` records into
        memory before returning.

        :param str sim: The SID or unique name of the Sim resource that IP Command was sent to or from.
        :param str sim_iccid: The ICCID of the Sim resource that IP Command was sent to or from.
        :param &quot;IpCommandInstance.Status&quot; status: The status of the IP Command. Can be: `queued`, `sent`, `received` or `failed`. See the [IP Command Status Values](https://www.twilio.com/docs/iot/supersim/api/ipcommand-resource#status-values) for a description of each.
        :param &quot;IpCommandInstance.Direction&quot; direction: The direction of the IP Command. Can be `to_sim` or `from_sim`. The value of `to_sim` is synonymous with the term `mobile terminated`, and `from_sim` is synonymous with the term `mobile originated`.
        :param limit: Upper limit for the number of records to return. list() guarantees
                      never to return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, list() will attempt to read the limit
                          with the most efficient page size, i.e. min(limit, 1000)

        :returns: list that will contain up to limit results
        """
        return [
            record
            async for record in await self.stream_async(
                sim=sim,
                sim_iccid=sim_iccid,
                status=status,
                direction=direction,
                limit=limit,
                page_size=page_size,
            )
        ]

    def page(
        self,
        sim: Union[str, object] = values.unset,
        sim_iccid: Union[str, object] = values.unset,
        status: Union["IpCommandInstance.Status", object] = values.unset,
        direction: Union["IpCommandInstance.Direction", object] = values.unset,
        page_token: Union[str, object] = values.unset,
        page_number: Union[int, object] = values.unset,
        page_size: Union[int, object] = values.unset,
    ) -> IpCommandPage:
        """
        Retrieve a single page of IpCommandInstance records from the API.
        Request is executed immediately

        :param sim: The SID or unique name of the Sim resource that IP Command was sent to or from.
        :param sim_iccid: The ICCID of the Sim resource that IP Command was sent to or from.
        :param status: The status of the IP Command. Can be: `queued`, `sent`, `received` or `failed`. See the [IP Command Status Values](https://www.twilio.com/docs/iot/supersim/api/ipcommand-resource#status-values) for a description of each.
        :param direction: The direction of the IP Command. Can be `to_sim` or `from_sim`. The value of `to_sim` is synonymous with the term `mobile terminated`, and `from_sim` is synonymous with the term `mobile originated`.
        :param page_token: PageToken provided by the API
        :param page_number: Page Number, this value is simply for client state
        :param page_size: Number of records to return, defaults to 50

        :returns: Page of IpCommandInstance
        """
        data = values.of(
            {
                "Sim": sim,
                "SimIccid": sim_iccid,
                "Status": status,
                "Direction": direction,
                "PageToken": page_token,
                "Page": page_number,
                "PageSize": page_size,
            }
        )

        response = self._version.page(method="GET", uri=self._uri, params=data)
        return IpCommandPage(self._version, response)

    async def page_async(
        self,
        sim: Union[str, object] = values.unset,
        sim_iccid: Union[str, object] = values.unset,
        status: Union["IpCommandInstance.Status", object] = values.unset,
        direction: Union["IpCommandInstance.Direction", object] = values.unset,
        page_token: Union[str, object] = values.unset,
        page_number: Union[int, object] = values.unset,
        page_size: Union[int, object] = values.unset,
    ) -> IpCommandPage:
        """
        Asynchronously retrieve a single page of IpCommandInstance records from the API.
        Request is executed immediately

        :param sim: The SID or unique name of the Sim resource that IP Command was sent to or from.
        :param sim_iccid: The ICCID of the Sim resource that IP Command was sent to or from.
        :param status: The status of the IP Command. Can be: `queued`, `sent`, `received` or `failed`. See the [IP Command Status Values](https://www.twilio.com/docs/iot/supersim/api/ipcommand-resource#status-values) for a description of each.
        :param direction: The direction of the IP Command. Can be `to_sim` or `from_sim`. The value of `to_sim` is synonymous with the term `mobile terminated`, and `from_sim` is synonymous with the term `mobile originated`.
        :param page_token: PageToken provided by the API
        :param page_number: Page Number, this value is simply for client state
        :param page_size: Number of records to return, defaults to 50

        :returns: Page of IpCommandInstance
        """
        data = values.of(
            {
                "Sim": sim,
                "SimIccid": sim_iccid,
                "Status": status,
                "Direction": direction,
                "PageToken": page_token,
                "Page": page_number,
                "PageSize": page_size,
            }
        )

        response = await self._version.page_async(
            method="GET", uri=self._uri, params=data
        )
        return IpCommandPage(self._version, response)

    def get_page(self, target_url: str) -> IpCommandPage:
        """
        Retrieve a specific page of IpCommandInstance records from the API.
        Request is executed immediately

        :param target_url: API-generated URL for the requested results page

        :returns: Page of IpCommandInstance
        """
        response = self._version.domain.twilio.request("GET", target_url)
        return IpCommandPage(self._version, response)

    async def get_page_async(self, target_url: str) -> IpCommandPage:
        """
        Asynchronously retrieve a specific page of IpCommandInstance records from the API.
        Request is executed immediately

        :param target_url: API-generated URL for the requested results page

        :returns: Page of IpCommandInstance
        """
        response = await self._version.domain.twilio.request_async("GET", target_url)
        return IpCommandPage(self._version, response)

    def get(self, sid: str) -> IpCommandContext:
        """
        Constructs a IpCommandContext

        :param sid: The SID of the IP Command resource to fetch.
        """
        return IpCommandContext(self._version, sid=sid)

    def __call__(self, sid: str) -> IpCommandContext:
        """
        Constructs a IpCommandContext

        :param sid: The SID of the IP Command resource to fetch.
        """
        return IpCommandContext(self._version, sid=sid)

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Supersim.V1.IpCommandList>"
