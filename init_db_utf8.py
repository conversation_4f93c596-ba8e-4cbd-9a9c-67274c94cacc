#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""UTF-8 encoding ile veritabanı oluşturma scripti"""

import os
import sys

# Encoding ayarları
if sys.version_info[0] >= 3:
    import io
    sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
    sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8')

from app import create_app, db
from app.models.user import User

def init_database():
    """UTF-8 encoding ile veritabanını başlat"""
    app = create_app('development')
    
    with app.app_context():
        # Veritabanı tablolarını oluştur
        db.create_all()
        print("✓ Veritabanı tabloları oluşturuldu.")
        
        # Admin kullanıcısı var mı kontrol et
        existing_admin = User.query.filter_by(email='<EMAIL>').first()
        if existing_admin:
            print("✓ Admin kullanıcısı zaten mevcut.")
            return
        
        # Yeni admin oluştur (UTF-8 karakterlerle)
        admin = User(
            ad='Admin',
            soyad='Kullanıcı',
            email='<EMAIL>',
            telefon_numarasi='05551234567',
            rol='admin'
        )
        admin.set_password('123456')
        
        # Test kullanıcıları (Türkçe karakterlerle)
        test_users = [
            {
                'ad': 'Ahmet',
                'soyad': 'Yılmaz',
                'email': '<EMAIL>',
                'telefon_numarasi': '05551234568',
                'rol': 'manager'
            },
            {
                'ad': 'Ayşe',
                'soyad': 'Öztürk',
                'email': '<EMAIL>',
                'telefon_numarasi': '05551234569',
                'rol': 'participant'
            },
            {
                'ad': 'Mehmet',
                'soyad': 'Çelik',
                'email': '<EMAIL>',
                'telefon_numarasi': '05551234570',
                'rol': 'participant'
            },
            {
                'ad': 'Fatma',
                'soyad': 'Şahin',
                'email': '<EMAIL>',
                'telefon_numarasi': '05551234571',
                'rol': 'participant'
            }
        ]
        
        db.session.add(admin)
        
        for user_data in test_users:
            user = User(**user_data)
            user.set_password('123456')  # Varsayılan şifre
            db.session.add(user)
        
        try:
            db.session.commit()
            print("✓ Admin kullanıcısı oluşturuldu:")
            print(f"  Email: {admin.email}")
            print(f"  Şifre: 123456")
            print(f"  Rol: {admin.rol}")
            print()
            print("✓ Test kullanıcıları oluşturuldu:")
            for user_data in test_users:
                print(f"  - {user_data['ad']} {user_data['soyad']} ({user_data['email']}) - {user_data['rol']}")
            print()
            print("✓ Tüm kullanıcılar için varsayılan şifre: 123456")
            
        except Exception as e:
            db.session.rollback()
            print(f"✗ Hata: {str(e)}")

if __name__ == '__main__':
    init_database()
