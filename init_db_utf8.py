#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""UTF-8 encoding ile veritabanı oluşturma scripti"""

import os
import sys
import json
from datetime import datetime, timedelta

# Encoding ayarları
if sys.version_info[0] >= 3:
    import io
    sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
    sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8')

from app import create_app, db
from app.models.user import User
from app.models.training import Training, Enrollment
from app.models.quiz import Quiz, QuizAttempt

def init_database():
    """UTF-8 encoding ile veritabanını başlat"""
    app = create_app('development')
    
    with app.app_context():
        # Veritabanı tablolarını oluştur
        db.create_all()
        print("✓ Veritabanı tabloları oluşturuldu.")
        
        # Admin kullanıcısı var mı kontrol et
        existing_admin = User.query.filter_by(email='<EMAIL>').first()
        if existing_admin:
            print("✓ Admin kullanıcısı zaten mevcut.")
            return
        
        # Admin kullanıcısı
        admin = User(
            ad='Admin',
            soyad='Kullanıcı',
            email='<EMAIL>',
            telefon_numarasi='05551234567',
            rol='admin'
        )
        admin.set_password('123456')
        db.session.add(admin)

        # 4 Manager (Müdür)
        managers_data = [
            {'ad': 'Ahmet', 'soyad': 'Yılmaz', 'email': '<EMAIL>', 'telefon': '05551234568'},
            {'ad': 'Ayşe', 'soyad': 'Kaya', 'email': '<EMAIL>', 'telefon': '05551234569'},
            {'ad': 'Mehmet', 'soyad': 'Demir', 'email': '<EMAIL>', 'telefon': '05551234570'},
            {'ad': 'Fatma', 'soyad': 'Şahin', 'email': '<EMAIL>', 'telefon': '05551234571'}
        ]

        managers = []
        for i, manager_data in enumerate(managers_data):
            manager = User(
                ad=manager_data['ad'],
                soyad=manager_data['soyad'],
                email=manager_data['email'],
                telefon_numarasi=manager_data['telefon'],
                rol='manager'
            )
            manager.set_password('123456')
            db.session.add(manager)
            managers.append(manager)

        # 20 Katılımcı
        participants_data = [
            {'ad': 'Ali', 'soyad': 'Özkan', 'email': '<EMAIL>', 'telefon': '05551234572'},
            {'ad': 'Zeynep', 'soyad': 'Çelik', 'email': '<EMAIL>', 'telefon': '05551234573'},
            {'ad': 'Mustafa', 'soyad': 'Arslan', 'email': '<EMAIL>', 'telefon': '05551234574'},
            {'ad': 'Elif', 'soyad': 'Güneş', 'email': '<EMAIL>', 'telefon': '05551234575'},
            {'ad': 'Emre', 'soyad': 'Kılıç', 'email': '<EMAIL>', 'telefon': '05551234576'},
            {'ad': 'Seda', 'soyad': 'Aydın', 'email': '<EMAIL>', 'telefon': '05551234577'},
            {'ad': 'Burak', 'soyad': 'Öztürk', 'email': '<EMAIL>', 'telefon': '05551234578'},
            {'ad': 'Merve', 'soyad': 'Koç', 'email': '<EMAIL>', 'telefon': '05551234579'},
            {'ad': 'Oğuz', 'soyad': 'Yıldız', 'email': '<EMAIL>', 'telefon': '05551234580'},
            {'ad': 'Gizem', 'soyad': 'Polat', 'email': '<EMAIL>', 'telefon': '05551234581'},
            {'ad': 'Serkan', 'soyad': 'Erdoğan', 'email': '<EMAIL>', 'telefon': '05551234582'},
            {'ad': 'Deniz', 'soyad': 'Çakır', 'email': '<EMAIL>', 'telefon': '05551234583'},
            {'ad': 'Kemal', 'soyad': 'Başar', 'email': '<EMAIL>', 'telefon': '05551234584'},
            {'ad': 'Pınar', 'soyad': 'Ünlü', 'email': '<EMAIL>', 'telefon': '05551234585'},
            {'ad': 'Tolga', 'soyad': 'Karaca', 'email': '<EMAIL>', 'telefon': '05551234586'},
            {'ad': 'Sibel', 'soyad': 'Doğan', 'email': '<EMAIL>', 'telefon': '05551234587'},
            {'ad': 'Cem', 'soyad': 'Güler', 'email': '<EMAIL>', 'telefon': '05551234588'},
            {'ad': 'Neslihan', 'soyad': 'Taş', 'email': '<EMAIL>', 'telefon': '05551234589'},
            {'ad': 'Rıza', 'soyad': 'Özer', 'email': '<EMAIL>', 'telefon': '05551234590'},
            {'ad': 'Gamze', 'soyad': 'Kurt', 'email': '<EMAIL>', 'telefon': '05551234591'}
        ]

        participants = []
        for i, participant_data in enumerate(participants_data):
            # Her 5 katılımcıyı bir manager'a ata
            manager_index = i // 5
            participant = User(
                ad=participant_data['ad'],
                soyad=participant_data['soyad'],
                email=participant_data['email'],
                telefon_numarasi=participant_data['telefon'],
                rol='participant',
                manager_id=None  # Sonra atanacak
            )
            participant.set_password('123456')
            db.session.add(participant)
            participants.append((participant, manager_index))

        # Commit et ki ID'ler oluşsun
        db.session.commit()

        # Manager ilişkilerini kur
        for participant, manager_index in participants:
            if manager_index < len(managers):
                participant.manager_id = managers[manager_index].id
        
        # 4 İSG Eğitimi oluştur
        trainings_data = [
            {
                'ad': 'Temel İş Sağlığı ve Güvenliği Eğitimi',
                'aciklama': 'İş yerinde karşılaşılabilecek temel risklerin tanınması, değerlendirilmesi ve kontrol edilmesi konularında temel bilgilerin verildiği eğitim.',
                'baslangic': datetime.utcnow() + timedelta(days=7),
                'bitis': datetime.utcnow() + timedelta(days=8)
            },
            {
                'ad': 'Yangın Güvenliği ve Acil Durum Eğitimi',
                'aciklama': 'Yangın risklerinin önlenmesi, yangın söndürme teknikleri ve acil durum prosedürleri hakkında kapsamlı eğitim.',
                'baslangic': datetime.utcnow() + timedelta(days=14),
                'bitis': datetime.utcnow() + timedelta(days=15)
            },
            {
                'ad': 'Kişisel Koruyucu Donanım (KKD) Eğitimi',
                'aciklama': 'Kişisel koruyucu donanımların seçimi, kullanımı, bakımı ve muhafazası konularında detaylı bilgilendirme.',
                'baslangic': datetime.utcnow() + timedelta(days=21),
                'bitis': datetime.utcnow() + timedelta(days=22)
            },
            {
                'ad': 'İş Kazası ve Meslek Hastalıkları Önleme Eğitimi',
                'aciklama': 'İş kazalarının nedenlerinin analizi, meslek hastalıklarının önlenmesi ve güvenli çalışma yöntemleri.',
                'baslangic': datetime.utcnow() + timedelta(days=28),
                'bitis': datetime.utcnow() + timedelta(days=29)
            }
        ]

        trainings = []
        for i, training_data in enumerate(trainings_data):
            # Her eğitimi farklı bir manager oluştursun
            creator = managers[i % len(managers)]
            training = Training(
                ad=training_data['ad'],
                aciklama=training_data['aciklama'],
                baslangic_tarihi=training_data['baslangic'],
                bitis_tarihi=training_data['bitis'],
                olusturan_id=creator.id
            )
            db.session.add(training)
            trainings.append(training)

        db.session.commit()  # Training ID'lerini al

        # Quiz soruları
        quiz_questions = {
            'Temel İş Sağlığı ve Güvenliği Eğitimi': [
                {
                    'question': 'İş Sağlığı ve Güvenliği Kanunu hangi yılda yürürlüğe girmiştir?',
                    'options': ['2010', '2012', '2014', '2016'],
                    'correct_answer': '2012'
                },
                {
                    'question': 'İSG risk değerlendirmesi hangi sıklıkla yapılmalıdır?',
                    'options': ['Yılda bir', 'İki yılda bir', 'Gerektiğinde', 'Hiç yapılmaz'],
                    'correct_answer': 'Gerektiğinde'
                },
                {
                    'question': 'İş kazası bildirimi kaç gün içinde yapılmalıdır?',
                    'options': ['1 gün', '3 gün', '7 gün', '15 gün'],
                    'correct_answer': '3 gün'
                },
                {
                    'question': 'İSG komitesi en az kaç kişiden oluşur?',
                    'options': ['3', '6', '9', '12'],
                    'correct_answer': '6'
                },
                {
                    'question': 'İş güvenliği uzmanı çalıştırma zorunluluğu kaç ve üzeri çalışanı olan işyerleri için geçerlidir?',
                    'options': ['10', '20', '50', '100'],
                    'correct_answer': '50'
                }
            ],
            'Yangın Güvenliği ve Acil Durum Eğitimi': [
                {
                    'question': 'Yangın üçgeni hangi üç unsurdan oluşur?',
                    'options': ['Isı-Oksijen-Yakıt', 'Su-Hava-Ateş', 'Duman-Alev-Kül', 'Sıcaklık-Nem-Basınç'],
                    'correct_answer': 'Isı-Oksijen-Yakıt'
                },
                {
                    'question': 'CO2 yangın söndürücüsü hangi sınıf yangınlarda kullanılır?',
                    'options': ['A sınıfı', 'B sınıfı', 'C sınıfı', 'B ve C sınıfı'],
                    'correct_answer': 'B ve C sınıfı'
                },
                {
                    'question': 'Yangın alarm sistemi test edilme sıklığı nedir?',
                    'options': ['Haftalık', 'Aylık', '3 aylık', '6 aylık'],
                    'correct_answer': 'Aylık'
                },
                {
                    'question': 'Acil durum toplanma alanının özellikleri nelerdir?',
                    'options': ['Kapalı alan', 'Açık ve güvenli alan', 'Bodrum katı', 'Çatı katı'],
                    'correct_answer': 'Açık ve güvenli alan'
                },
                {
                    'question': 'Yangın merdiveni genişliği en az kaç metre olmalıdır?',
                    'options': ['0.9 m', '1.2 m', '1.5 m', '2.0 m'],
                    'correct_answer': '1.2 m'
                }
            ],
            'Kişisel Koruyucu Donanım (KKD) Eğitimi': [
                {
                    'question': 'KKD kullanımında öncelik sırası nedir?',
                    'options': ['Koruma-Önleme-Kontrol', 'Önleme-Kontrol-Koruma', 'Kontrol-Koruma-Önleme', 'Koruma-Kontrol-Önleme'],
                    'correct_answer': 'Önleme-Kontrol-Koruma'
                },
                {
                    'question': 'Baret hangi tip korumayı sağlar?',
                    'options': ['Göz koruması', 'Kafa koruması', 'Kulak koruması', 'Solunum koruması'],
                    'correct_answer': 'Kafa koruması'
                },
                {
                    'question': 'N95 maskesi hangi tip koruma sağlar?',
                    'options': ['%85 filtrasyon', '%90 filtrasyon', '%95 filtrasyon', '%99 filtrasyon'],
                    'correct_answer': '%95 filtrasyon'
                },
                {
                    'question': 'Güvenlik ayakkabısının temel özelliği nedir?',
                    'options': ['Su geçirmezlik', 'Çelik burun', 'Yüksek topuk', 'Renkli olması'],
                    'correct_answer': 'Çelik burun'
                },
                {
                    'question': 'KKD bakım ve kontrolü kim tarafından yapılır?',
                    'options': ['İşveren', 'İSG uzmanı', 'Çalışan', 'Hepsi'],
                    'correct_answer': 'Hepsi'
                }
            ],
            'İş Kazası ve Meslek Hastalıkları Önleme Eğitimi': [
                {
                    'question': 'İş kazası tanımında hangi unsur yoktur?',
                    'options': ['Ani olay', 'İş yerinde', 'Bedensel zarar', 'Kasıtlı eylem'],
                    'correct_answer': 'Kasıtlı eylem'
                },
                {
                    'question': 'Heinrich piramidine göre 1 ölümlü kazaya karşılık kaç yaralanmasız kaza vardır?',
                    'options': ['30', '300', '3000', '30000'],
                    'correct_answer': '300'
                },
                {
                    'question': 'Meslek hastalığının temel nedeni nedir?',
                    'options': ['Ani etki', 'Uzun süreli etki', 'Genetik faktör', 'Yaş faktörü'],
                    'correct_answer': 'Uzun süreli etki'
                },
                {
                    'question': 'İş kazası oranı nasıl hesaplanır?',
                    'options': ['Kaza sayısı/Çalışan sayısı', 'Kaza sayısı/Çalışılan saat', 'Kaza sayısı x 1.000.000/Çalışılan saat', 'Kaza sayısı x 100/Çalışan sayısı'],
                    'correct_answer': 'Kaza sayısı x 1.000.000/Çalışılan saat'
                },
                {
                    'question': 'Near miss (ramak kala olay) raporlamasının amacı nedir?',
                    'options': ['Ceza vermek', 'Önleyici tedbir almak', 'Suçlu bulmak', 'Kayıt tutmak'],
                    'correct_answer': 'Önleyici tedbir almak'
                }
            ]
        }

        # Her eğitim için quiz oluştur
        quizzes = []
        for training in trainings:
            questions = quiz_questions.get(training.ad, [])
            if questions:
                quiz = Quiz(
                    training_id=training.id,
                    ad=f"{training.ad} - Değerlendirme Quizi",
                    aciklama=f"{training.ad} eğitimi sonrası değerlendirme quizi",
                    questions_json=json.dumps(questions, ensure_ascii=False),
                    gecer_notu=70.0,
                    olusturan_id=training.olusturan_id
                )
                db.session.add(quiz)
                quizzes.append(quiz)

        db.session.commit()

        # Katılımcıları eğitimlere kaydet (her eğitime rastgele katılımcılar)
        import random
        all_participants = [p[0] for p in participants]

        for training in trainings:
            # Her eğitime 10-15 rastgele katılımcı ata
            selected_participants = random.sample(all_participants, random.randint(10, 15))
            for participant in selected_participants:
                enrollment = Enrollment(
                    user_id=participant.id,
                    training_id=training.id,
                    katilim_teyidi_durumu=random.choice(['beklemede', 'teyit_edildi', 'teyit_edildi', 'teyit_edildi'])  # Çoğu teyit edilmiş
                )
                db.session.add(enrollment)

        db.session.commit()

        try:
            print("✓ Admin kullanıcısı oluşturuldu:")
            print(f"  Email: {admin.email}")
            print(f"  Şifre: 123456")
            print(f"  Rol: {admin.rol}")
            print()

            print("✓ Yöneticiler oluşturuldu:")
            for manager in managers:
                team_count = len([p for p, _ in participants if managers.index(manager) == _])
                print(f"  - {manager.tam_ad} ({manager.email}) - Ekip: {team_count} kişi")
            print()

            print("✓ Katılımcılar oluşturuldu:")
            for participant, manager_index in participants:
                manager_name = managers[manager_index].tam_ad if manager_index < len(managers) else "Atanmamış"
                print(f"  - {participant.tam_ad} ({participant.email}) - Yöneticisi: {manager_name}")
            print()

            print("✓ İSG Eğitimleri oluşturuldu:")
            for training in trainings:
                participant_count = training.katilimci_sayisi
                print(f"  - {training.ad}")
                print(f"    Tarih: {training.baslangic_tarihi.strftime('%d.%m.%Y')} - {training.bitis_tarihi.strftime('%d.%m.%Y')}")
                print(f"    Oluşturan: {training.olusturan.tam_ad}")
                print(f"    Katılımcı: {participant_count} kişi")
            print()

            print("✓ Quizler oluşturuldu:")
            for quiz in quizzes:
                print(f"  - {quiz.ad} (Geçer not: {quiz.gecer_notu}%)")
            print()

            print("✓ Tüm kullanıcılar için varsayılan şifre: 123456")
            print("✓ Manager-katılımcı ilişkileri kuruldu")
            print("✓ Eğitim kayıtları tamamlandı")

        except Exception as e:
            db.session.rollback()
            print(f"✗ Hata: {str(e)}")

if __name__ == '__main__':
    init_database()
