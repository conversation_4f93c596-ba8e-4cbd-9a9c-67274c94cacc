#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Sistem ayarları tablosunu oluştur ve varsayılan değerleri yükle
"""

from app import create_app, db
from app.models.settings import SystemSettings

def main():
    app = create_app()
    
    with app.app_context():
        try:
            # Tabloları oluştur
            db.create_all()
            print("✅ Veritabanı tabloları oluşturuldu")
            
            # Varsayılan ayarları oluştur
            success = SystemSettings.initialize_default_settings()
            
            if success:
                print("✅ Varsayılan sistem ayarları oluşturuldu")
            else:
                print("❌ Varsayılan ayarlar oluşturulamadı")
            
            # Ayar sayısını kontrol et
            count = SystemSettings.query.count()
            print(f"📊 Toplam ayar sayısı: {count}")
            
            # Kategorilere göre ayar sayıları
            categories = db.session.query(SystemSettings.category, db.func.count(SystemSettings.id)).group_by(SystemSettings.category).all()
            
            print("\n📋 Kategori bazında ayar sayıları:")
            for category, count in categories:
                print(f"  • {category}: {count} ayar")
            
            print("\n🎉 Sistem ayarları başarıyla hazırlandı!")
            
        except Exception as e:
            print(f"❌ Hata oluştu: {str(e)}")
            return False
    
    return True

if __name__ == "__main__":
    main()
