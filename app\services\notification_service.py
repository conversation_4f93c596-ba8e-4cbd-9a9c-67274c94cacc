#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""<PERSON><PERSON><PERSON><PERSON> (SMS ve Email)"""

import os
from datetime import datetime
from flask import current_app, url_for
from flask_mail import Message
from twilio.rest import Client
from app import mail, db
from app.models.communication import CommunicationLog

class NotificationService:
    """SMS ve Email gönderme servisi"""
    
    def __init__(self):
        self.twilio_client = None
        self._init_twilio()
    
    def _init_twilio(self):
        """Twilio client'ını başlat"""
        try:
            account_sid = current_app.config.get('TWILIO_ACCOUNT_SID')
            auth_token = current_app.config.get('TWILIO_AUTH_TOKEN')
            
            if account_sid and auth_token:
                self.twilio_client = Client(account_sid, auth_token)
            else:
                current_app.logger.warning("Twilio credentials not configured")
        except Exception as e:
            current_app.logger.error(f"Twilio initialization error: {str(e)}")
    
    def send_sms(self, to_phone, message, message_type='info', related_training_id=None, related_user_id=None):
        """SMS gönder"""
        try:
            if not self.twilio_client:
                current_app.logger.error("Twilio client not initialized")
                return False, "SMS servisi yapılandırılmamış"
            
            # Telefon numarasını formatla
            if not to_phone.startswith('+'):
                to_phone = '+90' + to_phone.replace('0', '', 1)  # Türkiye kodu ekle
            
            # SMS gönder
            message_obj = self.twilio_client.messages.create(
                body=message,
                from_=current_app.config.get('TWILIO_PHONE_NUMBER', '+**********'),
                to=to_phone
            )
            
            # Log kaydet
            self._log_communication(
                message_type='sms',
                content=message,
                status='sent',
                external_id=message_obj.sid,
                related_training_id=related_training_id,
                related_user_id=related_user_id
            )
            
            return True, f"SMS gönderildi: {message_obj.sid}"
            
        except Exception as e:
            error_msg = f"SMS gönderme hatası: {str(e)}"
            current_app.logger.error(error_msg)
            
            # Hata log'u kaydet
            self._log_communication(
                message_type='sms',
                content=message,
                status='failed',
                error_message=str(e),
                related_training_id=related_training_id,
                related_user_id=related_user_id
            )
            
            return False, error_msg
    
    def send_email(self, to_email, subject, body, message_type='info', related_training_id=None, related_user_id=None):
        """Email gönder"""
        try:
            msg = Message(
                subject=subject,
                recipients=[to_email],
                html=body,
                sender=current_app.config.get('MAIL_DEFAULT_SENDER')
            )
            
            mail.send(msg)
            
            # Log kaydet
            self._log_communication(
                message_type='email',
                content=f"Subject: {subject}\n\n{body}",
                status='sent',
                related_training_id=related_training_id,
                related_user_id=related_user_id
            )
            
            return True, "Email gönderildi"
            
        except Exception as e:
            error_msg = f"Email gönderme hatası: {str(e)}"
            current_app.logger.error(error_msg)
            
            # Hata log'u kaydet
            self._log_communication(
                message_type='email',
                content=f"Subject: {subject}\n\n{body}",
                status='failed',
                error_message=str(e),
                related_training_id=related_training_id,
                related_user_id=related_user_id
            )
            
            return False, error_msg
    
    def _log_communication(self, message_type, content, status, external_id=None,
                          error_message=None, related_training_id=None, related_user_id=None):
        """İletişim log'u kaydet"""
        try:
            log = CommunicationLog(
                type=message_type,
                recipient_id=related_user_id,
                content=content,
                status=status,
                external_id=external_id,
                error_message=error_message,
                training_id=related_training_id
            )

            db.session.add(log)
            db.session.commit()

        except Exception as e:
            current_app.logger.error(f"Communication log error: {str(e)}")
            db.session.rollback()

class TrainingNotificationService:
    """Eğitim özelinde bildirim servisi"""
    
    def __init__(self):
        self.notification_service = NotificationService()
    
    def send_participation_confirmation_sms(self, training, participants):
        """Katılım teyidi SMS'i gönder"""
        results = {'success': 0, 'failed': 0, 'errors': []}
        
        for participant in participants:
            message = self._create_participation_sms_message(training, participant)
            
            success, msg = self.notification_service.send_sms(
                to_phone=participant.telefon_numarasi,
                message=message,
                message_type='participation_confirmation',
                related_training_id=training.id,
                related_user_id=participant.id
            )
            
            if success:
                results['success'] += 1
            else:
                results['failed'] += 1
                results['errors'].append(f"{participant.tam_ad}: {msg}")
        
        return results
    
    def send_quiz_link_sms(self, training, quiz, participants):
        """Quiz linki SMS'i gönder"""
        results = {'success': 0, 'failed': 0, 'errors': []}
        
        for participant in participants:
            message = self._create_quiz_sms_message(training, quiz, participant)
            
            success, msg = self.notification_service.send_sms(
                to_phone=participant.telefon_numarasi,
                message=message,
                message_type='quiz_link',
                related_training_id=training.id,
                related_user_id=participant.id
            )
            
            if success:
                results['success'] += 1
            else:
                results['failed'] += 1
                results['errors'].append(f"{participant.tam_ad}: {msg}")
        
        return results
    
    def send_evaluation_survey_email(self, training, managers):
        """Değerlendirme anketi email'i gönder"""
        results = {'success': 0, 'failed': 0, 'errors': []}
        
        for manager in managers:
            subject, body = self._create_evaluation_email_content(training, manager)
            
            success, msg = self.notification_service.send_email(
                to_email=manager.email,
                subject=subject,
                body=body,
                message_type='evaluation_survey',
                related_training_id=training.id,
                related_user_id=manager.id
            )
            
            if success:
                results['success'] += 1
            else:
                results['failed'] += 1
                results['errors'].append(f"{manager.tam_ad}: {msg}")
        
        return results
    
    def _create_participation_sms_message(self, training, participant):
        """Katılım teyidi SMS mesajı oluştur"""
        return f"""
Sayın {participant.ad} {participant.soyad},

"{training.ad}" eğitimine katılımınız teyit edilmiştir.

📅 Tarih: {training.baslangic_tarihi.strftime('%d.%m.%Y %H:%M')}
📍 Yer: {training.yer or 'Bildirilecek'}

Katılım durumunuzu onaylamak için: {url_for('training.confirm_participation', training_id=training.id, user_id=participant.id, _external=True)}

Bayraktar Eğitim Sistemi
        """.strip()
    
    def _create_quiz_sms_message(self, training, quiz, participant):
        """Quiz linki SMS mesajı oluştur"""
        return f"""
Sayın {participant.ad} {participant.soyad},

"{training.ad}" eğitimi quiz'i hazır!

📝 Quiz: {quiz.ad}
⏰ Süre: {quiz.sure_dakika or 30} dakika
📊 Geçer not: %{quiz.gecer_notu}

Quiz'e başlamak için: {url_for('quiz.take_quiz', quiz_id=quiz.id, _external=True)}

Bayraktar Eğitim Sistemi
        """.strip()
    
    def _create_evaluation_email_content(self, training, manager):
        """Değerlendirme anketi email içeriği oluştur"""
        # Manager'ın ekibindeki katılımcıları bul
        team_participants = [enrollment.user for enrollment in training.enrollments 
                           if enrollment.user.manager_id == manager.id]
        
        subject = f"Eğitim Değerlendirme Anketi - {training.ad}"
        
        participants_list = "\n".join([f"• {p.tam_ad}" for p in team_participants])
        
        body = f"""
        <html>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
            <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                <h2 style="color: #2c3e50; border-bottom: 2px solid #3498db; padding-bottom: 10px;">
                    Eğitim Değerlendirme Anketi
                </h2>
                
                <p>Sayın <strong>{manager.tam_ad}</strong>,</p>
                
                <p>Ekibinizdeki çalışanlar aşağıdaki eğitime katılmıştır:</p>
                
                <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;">
                    <h3 style="color: #2c3e50; margin-top: 0;">📚 {training.ad}</h3>
                    <p><strong>📅 Tarih:</strong> {training.baslangic_tarihi.strftime('%d.%m.%Y')} - {training.bitis_tarihi.strftime('%d.%m.%Y')}</p>
                    <p><strong>👨‍🏫 Eğitmen:</strong> {training.olusturan.tam_ad}</p>
                </div>
                
                <h4 style="color: #2c3e50;">👥 Katılan Ekip Üyeleriniz:</h4>
                <div style="background-color: #e8f5e8; padding: 10px; border-radius: 5px;">
                    {participants_list.replace(chr(10), '<br>')}
                </div>
                
                <p style="margin-top: 20px;">
                    Lütfen ekibinizdeki çalışanların eğitim performansını değerlendirmek için 
                    aşağıdaki anketi doldurunuz:
                </p>
                
                <div style="text-align: center; margin: 30px 0;">
                    <a href="{url_for('training.manager_survey', training_id=training.id, manager_id=manager.id, _external=True)}" 
                       style="background-color: #3498db; color: white; padding: 12px 30px; text-decoration: none; 
                              border-radius: 5px; font-weight: bold; display: inline-block;">
                        📝 Değerlendirme Anketini Doldur
                    </a>
                </div>
                
                <div style="background-color: #fff3cd; padding: 15px; border-radius: 5px; margin-top: 20px;">
                    <p style="margin: 0;"><strong>💡 Not:</strong> 
                    Bu anket, eğitimin etkinliğini ölçmek ve gelecekteki eğitimleri 
                    iyileştirmek amacıyla hazırlanmıştır.</p>
                </div>
                
                <hr style="margin: 30px 0; border: none; border-top: 1px solid #ddd;">
                
                <p style="font-size: 12px; color: #666; text-align: center;">
                    Bu email Bayraktar Eğitim Yönetim Sistemi tarafından otomatik olarak gönderilmiştir.<br>
                    Sorularınız için: <a href="mailto:<EMAIL>"><EMAIL></a>
                </p>
            </div>
        </body>
        </html>
        """
        
        return subject, body
