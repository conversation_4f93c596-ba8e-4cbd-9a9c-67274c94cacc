{% extends "base.html" %}

{% block title %}Sistem Logları - Eğitim Yönetim Sistemi{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-file-alt"></i> Sistem Logları
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('settings.index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> Ayarlara Dön
            </a>
            <button type="button" class="btn btn-outline-primary" onclick="refreshPage()">
                <i class="fas fa-sync-alt"></i> Yenile
            </button>
            <button type="button" class="btn btn-outline-warning" onclick="clearLogs()">
                <i class="fas fa-trash"></i> Logları Temizle
            </button>
        </div>
    </div>
</div>

{% if log_files %}
<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-list"></i> Log Dosyaları
                    <span class="badge bg-secondary">{{ log_files|length }}</span>
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Dosya Adı</th>
                                <th>Boyut</th>
                                <th>Son Değişiklik</th>
                                <th>İşlemler</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for log_file in log_files %}
                            <tr>
                                <td>
                                    <i class="fas fa-file-alt text-info me-2"></i>
                                    <strong>{{ log_file.name }}</strong>
                                </td>
                                <td>
                                    <span class="badge bg-info">{{ log_file.size }} KB</span>
                                </td>
                                <td>
                                    <small class="text-muted">
                                        {{ log_file.modified.strftime('%d.%m.%Y %H:%M:%S') }}
                                    </small>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <button type="button" class="btn btn-outline-primary" 
                                                onclick="viewLog('{{ log_file.name }}')"
                                                title="Log İçeriğini Görüntüle">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button type="button" class="btn btn-outline-success" 
                                                onclick="downloadLog('{{ log_file.name }}')"
                                                title="Log Dosyasını İndir">
                                            <i class="fas fa-download"></i>
                                        </button>
                                        <button type="button" class="btn btn-outline-danger" 
                                                onclick="deleteLog('{{ log_file.name }}')"
                                                title="Log Dosyasını Sil">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Log İçeriği Modal -->
<div class="modal fade" id="logViewModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-file-alt"></i> Log İçeriği: <span id="logFileName"></span>
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <div class="btn-group btn-group-sm" role="group">
                        <button type="button" class="btn btn-outline-secondary" onclick="toggleWrap()">
                            <i class="fas fa-text-width"></i> Satır Kaydırma
                        </button>
                        <button type="button" class="btn btn-outline-info" onclick="searchInLog()">
                            <i class="fas fa-search"></i> Ara
                        </button>
                        <button type="button" class="btn btn-outline-success" onclick="refreshLogContent()">
                            <i class="fas fa-sync-alt"></i> Yenile
                        </button>
                    </div>
                </div>
                <div id="logContent" class="border rounded p-3" style="height: 500px; overflow-y: auto; background-color: #f8f9fa;">
                    <div class="text-center text-muted">
                        <i class="fas fa-spinner fa-spin fa-2x"></i>
                        <p class="mt-2">Log içeriği yükleniyor...</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Kapat</button>
                <button type="button" class="btn btn-success" onclick="downloadCurrentLog()">
                    <i class="fas fa-download"></i> İndir
                </button>
            </div>
        </div>
    </div>
</div>

{% else %}
<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-body text-center py-5">
                <i class="fas fa-file-alt fa-4x text-muted mb-4"></i>
                <h4 class="text-muted">Log Dosyası Bulunamadı</h4>
                <p class="text-muted">
                    Henüz hiç log dosyası oluşturulmamış veya log klasörü mevcut değil.
                </p>
                <div class="mt-4">
                    <button type="button" class="btn btn-outline-primary" onclick="createLogDirectory()">
                        <i class="fas fa-folder-plus"></i> Log Klasörü Oluştur
                    </button>
                    <button type="button" class="btn btn-outline-info" onclick="generateSampleLog()">
                        <i class="fas fa-file-plus"></i> Örnek Log Oluştur
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Log Bilgileri -->
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card shadow">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle"></i> Log Bilgileri
                </h6>
            </div>
            <div class="card-body">
                <table class="table table-sm">
                    <tr>
                        <td><strong>Log Klasörü:</strong></td>
                        <td><code>logs/</code></td>
                    </tr>
                    <tr>
                        <td><strong>Toplam Dosya:</strong></td>
                        <td>{{ log_files|length }}</td>
                    </tr>
                    <tr>
                        <td><strong>Toplam Boyut:</strong></td>
                        <td>
                            {% set total_size = log_files|sum(attribute='size') %}
                            {{ "%.2f"|format(total_size) }} KB
                        </td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card shadow">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-tools"></i> Log Araçları
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button type="button" class="btn btn-outline-info btn-sm" onclick="exportAllLogs()">
                        <i class="fas fa-archive"></i> Tüm Logları Arşivle
                    </button>
                    <button type="button" class="btn btn-outline-warning btn-sm" onclick="rotateLogs()">
                        <i class="fas fa-redo"></i> Log Rotasyonu
                    </button>
                    <button type="button" class="btn btn-outline-secondary btn-sm" onclick="showLogSettings()">
                        <i class="fas fa-cog"></i> Log Ayarları
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
#logContent {
    font-family: 'Courier New', monospace;
    font-size: 0.85rem;
    white-space: pre-wrap;
    word-break: break-all;
}

.log-line {
    margin: 0;
    padding: 2px 0;
}

.log-error {
    background-color: #f8d7da;
    color: #721c24;
}

.log-warning {
    background-color: #fff3cd;
    color: #856404;
}

.log-info {
    background-color: #d1ecf1;
    color: #0c5460;
}

.table th {
    border-top: none;
}

.card {
    border: none;
    border-radius: 10px;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
let currentLogFile = '';
let wrapEnabled = true;

function refreshPage() {
    location.reload();
}

function viewLog(fileName) {
    currentLogFile = fileName;
    $('#logFileName').text(fileName);
    $('#logViewModal').modal('show');
    loadLogContent(fileName);
}

function loadLogContent(fileName) {
    $('#logContent').html(`
        <div class="text-center text-muted">
            <i class="fas fa-spinner fa-spin fa-2x"></i>
            <p class="mt-2">Log içeriği yükleniyor...</p>
        </div>
    `);

    // Gerçek log içeriğini yükle
    fetch(`/settings/logs/${fileName}/view?lines=500`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayLogContent(data.content);
            } else {
                $('#logContent').html(`
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle"></i>
                        ${data.message}
                    </div>
                `);
            }
        })
        .catch(error => {
            $('#logContent').html(`
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i>
                    Log yüklenirken hata oluştu: ${error}
                </div>
            `);
        });
}

function displayLogContent(content) {
    const lines = content.split('\n');
    let html = '';
    
    lines.forEach(line => {
        let className = 'log-line';
        if (line.includes('ERROR')) className += ' log-error';
        else if (line.includes('WARNING')) className += ' log-warning';
        else if (line.includes('INFO')) className += ' log-info';
        
        html += `<div class="${className}">${escapeHtml(line)}</div>`;
    });
    
    $('#logContent').html(html);
}

function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

function downloadLog(fileName) {
    window.open(`/settings/logs/${fileName}/download`, '_blank');
}

function deleteLog(fileName) {
    if (confirm(`"${fileName}" log dosyasını silmek istediğinizden emin misiniz?\n\nBu işlem geri alınamaz!`)) {
        fetch(`/settings/logs/${fileName}/delete`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('✅ ' + data.message);
                location.reload();
            } else {
                alert('❌ ' + data.message);
            }
        })
        .catch(error => {
            alert('❌ Hata: ' + error);
        });
    }
}

function clearLogs() {
    if (confirm('TÜM log dosyalarını temizlemek istediğinizden emin misiniz?\n\nBu işlem dosyaları silmez, sadece içeriklerini temizler.')) {
        fetch('/settings/logs/clear-all', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('✅ ' + data.message);
                location.reload();
            } else {
                alert('❌ ' + data.message);
            }
        })
        .catch(error => {
            alert('❌ Hata: ' + error);
        });
    }
}

function toggleWrap() {
    wrapEnabled = !wrapEnabled;
    const content = document.getElementById('logContent');
    if (wrapEnabled) {
        content.style.whiteSpace = 'pre-wrap';
        content.style.wordBreak = 'break-all';
    } else {
        content.style.whiteSpace = 'pre';
        content.style.wordBreak = 'normal';
    }
}

function searchInLog() {
    const searchTerm = prompt('Log içinde aranacak metin:');
    if (searchTerm) {
        const content = document.getElementById('logContent');
        const text = content.textContent;
        const regex = new RegExp(searchTerm, 'gi');
        const highlightedText = text.replace(regex, `<mark>$&</mark>`);
        content.innerHTML = highlightedText;
    }
}

function refreshLogContent() {
    if (currentLogFile) {
        loadLogContent(currentLogFile);
    }
}

function downloadCurrentLog() {
    if (currentLogFile) {
        downloadLog(currentLogFile);
    }
}

function createLogDirectory() {
    alert('📁 Log klasörü oluşturuluyor...');
    // TODO: Implement log directory creation
}

function generateSampleLog() {
    alert('📝 Örnek log dosyası oluşturuluyor...');
    // TODO: Implement sample log generation
}

function exportAllLogs() {
    alert('📦 Tüm loglar arşivleniyor...');
    // TODO: Implement log export
}

function rotateLogs() {
    if (confirm('Log rotasyonu başlatılsın mı?\n\nEski loglar arşivlenecek ve yeni log dosyaları oluşturulacak.')) {
        alert('🔄 Log rotasyonu başlatılıyor...');
        // TODO: Implement log rotation
    }
}

function showLogSettings() {
    alert('⚙️ Log ayarları özelliği yakında eklenecek...');
    // TODO: Implement log settings
}
</script>
{% endblock %}
