r"""
    This code was generated by
   ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
    |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
    |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

    Twilio - Preview
    This is the public Twilio REST API.

    NOTE: This class is auto generated by OpenAPI Generator.
    https://openapi-generator.tech
    Do not edit the class manually.
"""


from datetime import datetime
from typing import Any, Dict, List, Optional, Union, Iterator, AsyncIterator
from twilio.base import deserialize, values
from twilio.base.instance_context import InstanceContext
from twilio.base.instance_resource import InstanceResource
from twilio.base.list_resource import ListResource
from twilio.base.version import Version
from twilio.base.page import Page


class DeploymentInstance(InstanceResource):

    """
    :ivar sid: Contains a 34 character string that uniquely identifies this Deployment resource.
    :ivar url: Contains an absolute URL for this Deployment resource.
    :ivar friendly_name: Contains a human readable descriptive text for this Deployment, up to 64 characters long
    :ivar fleet_sid: Specifies the unique string identifier of the Fleet that the given Deployment belongs to.
    :ivar account_sid: Specifies the unique string identifier of the Account responsible for this Deployment.
    :ivar sync_service_sid: Specifies the unique string identifier of the Twilio Sync service instance linked to and accessible by this Deployment.
    :ivar date_created: Specifies the date this Deployment was created, given in UTC ISO 8601 format.
    :ivar date_updated: Specifies the date this Deployment was last updated, given in UTC ISO 8601 format.
    """

    def __init__(
        self,
        version: Version,
        payload: Dict[str, Any],
        fleet_sid: str,
        sid: Optional[str] = None,
    ):
        super().__init__(version)

        self.sid: Optional[str] = payload.get("sid")
        self.url: Optional[str] = payload.get("url")
        self.friendly_name: Optional[str] = payload.get("friendly_name")
        self.fleet_sid: Optional[str] = payload.get("fleet_sid")
        self.account_sid: Optional[str] = payload.get("account_sid")
        self.sync_service_sid: Optional[str] = payload.get("sync_service_sid")
        self.date_created: Optional[datetime] = deserialize.iso8601_datetime(
            payload.get("date_created")
        )
        self.date_updated: Optional[datetime] = deserialize.iso8601_datetime(
            payload.get("date_updated")
        )

        self._solution = {
            "fleet_sid": fleet_sid,
            "sid": sid or self.sid,
        }
        self._context: Optional[DeploymentContext] = None

    @property
    def _proxy(self) -> "DeploymentContext":
        """
        Generate an instance context for the instance, the context is capable of
        performing various actions. All instance actions are proxied to the context

        :returns: DeploymentContext for this DeploymentInstance
        """
        if self._context is None:
            self._context = DeploymentContext(
                self._version,
                fleet_sid=self._solution["fleet_sid"],
                sid=self._solution["sid"],
            )
        return self._context

    def delete(self) -> bool:
        """
        Deletes the DeploymentInstance


        :returns: True if delete succeeds, False otherwise
        """
        return self._proxy.delete()

    async def delete_async(self) -> bool:
        """
        Asynchronous coroutine that deletes the DeploymentInstance


        :returns: True if delete succeeds, False otherwise
        """
        return await self._proxy.delete_async()

    def fetch(self) -> "DeploymentInstance":
        """
        Fetch the DeploymentInstance


        :returns: The fetched DeploymentInstance
        """
        return self._proxy.fetch()

    async def fetch_async(self) -> "DeploymentInstance":
        """
        Asynchronous coroutine to fetch the DeploymentInstance


        :returns: The fetched DeploymentInstance
        """
        return await self._proxy.fetch_async()

    def update(
        self,
        friendly_name: Union[str, object] = values.unset,
        sync_service_sid: Union[str, object] = values.unset,
    ) -> "DeploymentInstance":
        """
        Update the DeploymentInstance

        :param friendly_name: Provides a human readable descriptive text for this Deployment, up to 64 characters long
        :param sync_service_sid: Provides the unique string identifier of the Twilio Sync service instance that will be linked to and accessible by this Deployment.

        :returns: The updated DeploymentInstance
        """
        return self._proxy.update(
            friendly_name=friendly_name,
            sync_service_sid=sync_service_sid,
        )

    async def update_async(
        self,
        friendly_name: Union[str, object] = values.unset,
        sync_service_sid: Union[str, object] = values.unset,
    ) -> "DeploymentInstance":
        """
        Asynchronous coroutine to update the DeploymentInstance

        :param friendly_name: Provides a human readable descriptive text for this Deployment, up to 64 characters long
        :param sync_service_sid: Provides the unique string identifier of the Twilio Sync service instance that will be linked to and accessible by this Deployment.

        :returns: The updated DeploymentInstance
        """
        return await self._proxy.update_async(
            friendly_name=friendly_name,
            sync_service_sid=sync_service_sid,
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Preview.DeployedDevices.DeploymentInstance {}>".format(context)


class DeploymentContext(InstanceContext):
    def __init__(self, version: Version, fleet_sid: str, sid: str):
        """
        Initialize the DeploymentContext

        :param version: Version that contains the resource
        :param fleet_sid:
        :param sid: Provides a 34 character string that uniquely identifies the requested Deployment resource.
        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "fleet_sid": fleet_sid,
            "sid": sid,
        }
        self._uri = "/Fleets/{fleet_sid}/Deployments/{sid}".format(**self._solution)

    def delete(self) -> bool:
        """
        Deletes the DeploymentInstance


        :returns: True if delete succeeds, False otherwise
        """
        return self._version.delete(
            method="DELETE",
            uri=self._uri,
        )

    async def delete_async(self) -> bool:
        """
        Asynchronous coroutine that deletes the DeploymentInstance


        :returns: True if delete succeeds, False otherwise
        """
        return await self._version.delete_async(
            method="DELETE",
            uri=self._uri,
        )

    def fetch(self) -> DeploymentInstance:
        """
        Fetch the DeploymentInstance


        :returns: The fetched DeploymentInstance
        """

        payload = self._version.fetch(
            method="GET",
            uri=self._uri,
        )

        return DeploymentInstance(
            self._version,
            payload,
            fleet_sid=self._solution["fleet_sid"],
            sid=self._solution["sid"],
        )

    async def fetch_async(self) -> DeploymentInstance:
        """
        Asynchronous coroutine to fetch the DeploymentInstance


        :returns: The fetched DeploymentInstance
        """

        payload = await self._version.fetch_async(
            method="GET",
            uri=self._uri,
        )

        return DeploymentInstance(
            self._version,
            payload,
            fleet_sid=self._solution["fleet_sid"],
            sid=self._solution["sid"],
        )

    def update(
        self,
        friendly_name: Union[str, object] = values.unset,
        sync_service_sid: Union[str, object] = values.unset,
    ) -> DeploymentInstance:
        """
        Update the DeploymentInstance

        :param friendly_name: Provides a human readable descriptive text for this Deployment, up to 64 characters long
        :param sync_service_sid: Provides the unique string identifier of the Twilio Sync service instance that will be linked to and accessible by this Deployment.

        :returns: The updated DeploymentInstance
        """
        data = values.of(
            {
                "FriendlyName": friendly_name,
                "SyncServiceSid": sync_service_sid,
            }
        )

        payload = self._version.update(
            method="POST",
            uri=self._uri,
            data=data,
        )

        return DeploymentInstance(
            self._version,
            payload,
            fleet_sid=self._solution["fleet_sid"],
            sid=self._solution["sid"],
        )

    async def update_async(
        self,
        friendly_name: Union[str, object] = values.unset,
        sync_service_sid: Union[str, object] = values.unset,
    ) -> DeploymentInstance:
        """
        Asynchronous coroutine to update the DeploymentInstance

        :param friendly_name: Provides a human readable descriptive text for this Deployment, up to 64 characters long
        :param sync_service_sid: Provides the unique string identifier of the Twilio Sync service instance that will be linked to and accessible by this Deployment.

        :returns: The updated DeploymentInstance
        """
        data = values.of(
            {
                "FriendlyName": friendly_name,
                "SyncServiceSid": sync_service_sid,
            }
        )

        payload = await self._version.update_async(
            method="POST",
            uri=self._uri,
            data=data,
        )

        return DeploymentInstance(
            self._version,
            payload,
            fleet_sid=self._solution["fleet_sid"],
            sid=self._solution["sid"],
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Preview.DeployedDevices.DeploymentContext {}>".format(context)


class DeploymentPage(Page):
    def get_instance(self, payload: Dict[str, Any]) -> DeploymentInstance:
        """
        Build an instance of DeploymentInstance

        :param payload: Payload response from the API
        """
        return DeploymentInstance(
            self._version, payload, fleet_sid=self._solution["fleet_sid"]
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Preview.DeployedDevices.DeploymentPage>"


class DeploymentList(ListResource):
    def __init__(self, version: Version, fleet_sid: str):
        """
        Initialize the DeploymentList

        :param version: Version that contains the resource
        :param fleet_sid:

        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "fleet_sid": fleet_sid,
        }
        self._uri = "/Fleets/{fleet_sid}/Deployments".format(**self._solution)

    def create(
        self,
        friendly_name: Union[str, object] = values.unset,
        sync_service_sid: Union[str, object] = values.unset,
    ) -> DeploymentInstance:
        """
        Create the DeploymentInstance

        :param friendly_name: Provides a human readable descriptive text for this Deployment, up to 256 characters long.
        :param sync_service_sid: Provides the unique string identifier of the Twilio Sync service instance that will be linked to and accessible by this Deployment.

        :returns: The created DeploymentInstance
        """
        data = values.of(
            {
                "FriendlyName": friendly_name,
                "SyncServiceSid": sync_service_sid,
            }
        )

        payload = self._version.create(
            method="POST",
            uri=self._uri,
            data=data,
        )

        return DeploymentInstance(
            self._version, payload, fleet_sid=self._solution["fleet_sid"]
        )

    async def create_async(
        self,
        friendly_name: Union[str, object] = values.unset,
        sync_service_sid: Union[str, object] = values.unset,
    ) -> DeploymentInstance:
        """
        Asynchronously create the DeploymentInstance

        :param friendly_name: Provides a human readable descriptive text for this Deployment, up to 256 characters long.
        :param sync_service_sid: Provides the unique string identifier of the Twilio Sync service instance that will be linked to and accessible by this Deployment.

        :returns: The created DeploymentInstance
        """
        data = values.of(
            {
                "FriendlyName": friendly_name,
                "SyncServiceSid": sync_service_sid,
            }
        )

        payload = await self._version.create_async(
            method="POST",
            uri=self._uri,
            data=data,
        )

        return DeploymentInstance(
            self._version, payload, fleet_sid=self._solution["fleet_sid"]
        )

    def stream(
        self,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> Iterator[DeploymentInstance]:
        """
        Streams DeploymentInstance records from the API as a generator stream.
        This operation lazily loads records as efficiently as possible until the limit
        is reached.
        The results are returned as a generator, so this operation is memory efficient.

        :param limit: Upper limit for the number of records to return. stream()
                      guarantees to never return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, stream() will attempt to read the
                          limit with the most efficient page size, i.e. min(limit, 1000)

        :returns: Generator that will yield up to limit results
        """
        limits = self._version.read_limits(limit, page_size)
        page = self.page(page_size=limits["page_size"])

        return self._version.stream(page, limits["limit"])

    async def stream_async(
        self,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> AsyncIterator[DeploymentInstance]:
        """
        Asynchronously streams DeploymentInstance records from the API as a generator stream.
        This operation lazily loads records as efficiently as possible until the limit
        is reached.
        The results are returned as a generator, so this operation is memory efficient.

        :param limit: Upper limit for the number of records to return. stream()
                      guarantees to never return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, stream() will attempt to read the
                          limit with the most efficient page size, i.e. min(limit, 1000)

        :returns: Generator that will yield up to limit results
        """
        limits = self._version.read_limits(limit, page_size)
        page = await self.page_async(page_size=limits["page_size"])

        return self._version.stream_async(page, limits["limit"])

    def list(
        self,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> List[DeploymentInstance]:
        """
        Lists DeploymentInstance records from the API as a list.
        Unlike stream(), this operation is eager and will load `limit` records into
        memory before returning.

        :param limit: Upper limit for the number of records to return. list() guarantees
                      never to return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, list() will attempt to read the limit
                          with the most efficient page size, i.e. min(limit, 1000)

        :returns: list that will contain up to limit results
        """
        return list(
            self.stream(
                limit=limit,
                page_size=page_size,
            )
        )

    async def list_async(
        self,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> List[DeploymentInstance]:
        """
        Asynchronously lists DeploymentInstance records from the API as a list.
        Unlike stream(), this operation is eager and will load `limit` records into
        memory before returning.

        :param limit: Upper limit for the number of records to return. list() guarantees
                      never to return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, list() will attempt to read the limit
                          with the most efficient page size, i.e. min(limit, 1000)

        :returns: list that will contain up to limit results
        """
        return [
            record
            async for record in await self.stream_async(
                limit=limit,
                page_size=page_size,
            )
        ]

    def page(
        self,
        page_token: Union[str, object] = values.unset,
        page_number: Union[int, object] = values.unset,
        page_size: Union[int, object] = values.unset,
    ) -> DeploymentPage:
        """
        Retrieve a single page of DeploymentInstance records from the API.
        Request is executed immediately

        :param page_token: PageToken provided by the API
        :param page_number: Page Number, this value is simply for client state
        :param page_size: Number of records to return, defaults to 50

        :returns: Page of DeploymentInstance
        """
        data = values.of(
            {
                "PageToken": page_token,
                "Page": page_number,
                "PageSize": page_size,
            }
        )

        response = self._version.page(method="GET", uri=self._uri, params=data)
        return DeploymentPage(self._version, response, self._solution)

    async def page_async(
        self,
        page_token: Union[str, object] = values.unset,
        page_number: Union[int, object] = values.unset,
        page_size: Union[int, object] = values.unset,
    ) -> DeploymentPage:
        """
        Asynchronously retrieve a single page of DeploymentInstance records from the API.
        Request is executed immediately

        :param page_token: PageToken provided by the API
        :param page_number: Page Number, this value is simply for client state
        :param page_size: Number of records to return, defaults to 50

        :returns: Page of DeploymentInstance
        """
        data = values.of(
            {
                "PageToken": page_token,
                "Page": page_number,
                "PageSize": page_size,
            }
        )

        response = await self._version.page_async(
            method="GET", uri=self._uri, params=data
        )
        return DeploymentPage(self._version, response, self._solution)

    def get_page(self, target_url: str) -> DeploymentPage:
        """
        Retrieve a specific page of DeploymentInstance records from the API.
        Request is executed immediately

        :param target_url: API-generated URL for the requested results page

        :returns: Page of DeploymentInstance
        """
        response = self._version.domain.twilio.request("GET", target_url)
        return DeploymentPage(self._version, response, self._solution)

    async def get_page_async(self, target_url: str) -> DeploymentPage:
        """
        Asynchronously retrieve a specific page of DeploymentInstance records from the API.
        Request is executed immediately

        :param target_url: API-generated URL for the requested results page

        :returns: Page of DeploymentInstance
        """
        response = await self._version.domain.twilio.request_async("GET", target_url)
        return DeploymentPage(self._version, response, self._solution)

    def get(self, sid: str) -> DeploymentContext:
        """
        Constructs a DeploymentContext

        :param sid: Provides a 34 character string that uniquely identifies the requested Deployment resource.
        """
        return DeploymentContext(
            self._version, fleet_sid=self._solution["fleet_sid"], sid=sid
        )

    def __call__(self, sid: str) -> DeploymentContext:
        """
        Constructs a DeploymentContext

        :param sid: Provides a 34 character string that uniquely identifies the requested Deployment resource.
        """
        return DeploymentContext(
            self._version, fleet_sid=self._solution["fleet_sid"], sid=sid
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Preview.DeployedDevices.DeploymentList>"
