{% extends "base.html" %}

{% block title %}
{% if training %}Eğitim Düzenle{% else %}Yeni Eğitim{% endif %} - Eğitim Yönetim Sistemi
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        {% if training %}
        <i class="fas fa-edit"></i> Eğitim Düzenle: {{ training.ad }}
        {% else %}
        <i class="fas fa-plus"></i> Yeni Eğitim Oluştur
        {% endif %}
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{{ url_for('training.list_trainings') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left"></i> <PERSON><PERSON>
        </a>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card shadow">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle"></i> Eğitim Bilgileri
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="mb-3">
                        <label for="ad" class="form-label">Eğitim Adı *</label>
                        <input type="text" class="form-control" id="ad" name="ad" 
                               value="{{ training.ad if training else '' }}" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="aciklama" class="form-label">Açıklama</label>
                        <textarea class="form-control" id="aciklama" name="aciklama" rows="4">{{ training.aciklama if training else '' }}</textarea>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="baslangic_tarihi" class="form-label">Başlangıç Tarihi *</label>
                            <input type="datetime-local" class="form-control" id="baslangic_tarihi" name="baslangic_tarihi" 
                                   value="{{ training.baslangic_tarihi.strftime('%Y-%m-%dT%H:%M') if training and training.baslangic_tarihi else '' }}" required>
                        </div>
                        <div class="col-md-6">
                            <label for="bitis_tarihi" class="form-label">Bitiş Tarihi *</label>
                            <input type="datetime-local" class="form-control" id="bitis_tarihi" name="bitis_tarihi" 
                                   value="{{ training.bitis_tarihi.strftime('%Y-%m-%dT%H:%M') if training and training.bitis_tarihi else '' }}" required>
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('training.list_trainings') }}" class="btn btn-secondary">
                            <i class="fas fa-times"></i> İptal
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> 
                            {% if training %}Güncelle{% else %}Oluştur{% endif %}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card shadow">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-lightbulb"></i> Yardım</h6>
            </div>
            <div class="card-body">
                <h6>Eğitim Oluşturma İpuçları:</h6>
                <ul class="small">
                    <li>Eğitim adını açık ve anlaşılır yazın</li>
                    <li>Başlangıç tarihi gelecekte olmalıdır</li>
                    <li>Bitiş tarihi başlangıç tarihinden sonra olmalıdır</li>
                    <li>Açıklama alanında eğitim içeriği hakkında bilgi verin</li>
                </ul>
                
                <hr>
                
                <h6>Sonraki Adımlar:</h6>
                <ul class="small">
                    <li>Eğitimi oluşturduktan sonra katılımcı ekleyebilirsiniz</li>
                    <li>Quiz ve anketler oluşturabilirsiniz</li>
                    <li>SMS teyitlerini gönderebilirsiniz</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Tarih validasyonu
    $('#baslangic_tarihi, #bitis_tarihi').on('change', function() {
        var baslangic = new Date($('#baslangic_tarihi').val());
        var bitis = new Date($('#bitis_tarihi').val());
        
        if (baslangic && bitis && bitis <= baslangic) {
            alert('Bitiş tarihi başlangıç tarihinden sonra olmalıdır.');
            $('#bitis_tarihi').focus();
        }
    });
});
</script>
{% endblock %}
