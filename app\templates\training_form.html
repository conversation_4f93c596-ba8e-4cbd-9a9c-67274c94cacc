{% extends "base.html" %}

{% block title %}
{% if training %}Eğitim Düzenle{% else %}Yeni Eğitim{% endif %} - Eğitim Yönetim Sistemi
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        {% if training %}
        <i class="fas fa-edit"></i> Eğitim Düzenle: {{ training.ad }}
        {% else %}
        <i class="fas fa-plus"></i> Yeni Eğitim Oluştur
        {% endif %}
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{{ url_for('training.list_trainings') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left"></i> <PERSON><PERSON>
        </a>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card shadow">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle"></i> Eğitim Bilgileri
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="mb-3">
                        <label for="ad" class="form-label">Eğitim Adı *</label>
                        <input type="text" class="form-control" id="ad" name="ad" 
                               value="{{ training.ad if training else '' }}" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="aciklama" class="form-label">Açıklama</label>
                        <textarea class="form-control" id="aciklama" name="aciklama" rows="4">{{ training.aciklama if training else '' }}</textarea>
                    </div>
                    
                    <!-- Şirket Seçimi -->
                    <div class="mb-3">
                        <label for="company_id" class="form-label">Şirket</label>
                        <select class="form-select" id="company_id" name="company_id">
                            <option value="">Şirket seçin...</option>
                            {% for company in companies %}
                            <option value="{{ company.id }}" {{ 'selected' if training and training.company_id == company.id else '' }}>
                                {{ company.tam_ad }}
                            </option>
                            {% endfor %}
                        </select>
                        <div class="form-text">Eğitimin bağlı olduğu şirket</div>
                    </div>

                    <!-- Eğitmen Seçimi -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-chalkboard-teacher"></i> Eğitmen Bilgileri
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label class="form-label">Eğitmen Tipi *</label>
                                <div>
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="radio" name="egitmen_tipi" id="egitmen_sistem" value="sistem"
                                               {{ 'checked' if training and training.egitmen_user_id else '' }}>
                                        <label class="form-check-label" for="egitmen_sistem">
                                            <i class="fas fa-user"></i> Sistem Eğitmeni
                                        </label>
                                    </div>
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="radio" name="egitmen_tipi" id="egitmen_dis" value="dis"
                                               {{ 'checked' if not training or not training.egitmen_user_id else '' }}>
                                        <label class="form-check-label" for="egitmen_dis">
                                            <i class="fas fa-user-plus"></i> Dış Eğitmen
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <!-- Sistem Eğitmeni Seçimi -->
                            <div id="sistem_egitmen_section" style="{{ 'display: block;' if training and training.egitmen_user_id else 'display: none;' }}">
                                <div class="mb-3">
                                    <label for="egitmen_user_id" class="form-label">Sistem Eğitmeni Seçin *</label>
                                    <select class="form-select" id="egitmen_user_id" name="egitmen_user_id">
                                        <option value="">Eğitmen seçin...</option>
                                        {% for instructor in instructors %}
                                        <option value="{{ instructor.id }}" {{ 'selected' if training and training.egitmen_user_id == instructor.id else '' }}>
                                            {{ instructor.tam_ad }} ({{ instructor.email }})
                                            {% if instructor.company %} - {{ instructor.company.tam_ad }}{% endif %}
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>

                            <!-- Dış Eğitmen Bilgileri -->
                            <div id="dis_egitmen_section" style="{{ 'display: none;' if training and training.egitmen_user_id else 'display: block;' }}">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="egitmen_adi" class="form-label">Eğitmen Adı *</label>
                                            <input type="text" class="form-control" id="egitmen_adi" name="egitmen_adi"
                                                   value="{{ training.egitmen_adi if training and not training.egitmen_user_id else '' }}">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="egitmen_telefon" class="form-label">Telefon Numarası *</label>
                                            <input type="tel" class="form-control" id="egitmen_telefon" name="egitmen_telefon"
                                                   value="{{ training.egitmen_telefon if training and not training.egitmen_user_id else '' }}"
                                                   placeholder="05XXXXXXXXX">
                                            <div class="form-text">SMS onayı için gerekli</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label for="egitmen_email" class="form-label">E-posta</label>
                                    <input type="email" class="form-control" id="egitmen_email" name="egitmen_email"
                                           value="{{ training.egitmen_email if training and not training.egitmen_user_id else '' }}">
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="baslangic_tarihi" class="form-label">Başlangıç Tarihi *</label>
                            <input type="datetime-local" class="form-control" id="baslangic_tarihi" name="baslangic_tarihi"
                                   value="{{ training.baslangic_tarihi.strftime('%Y-%m-%dT%H:%M') if training and training.baslangic_tarihi else '' }}" required>
                        </div>
                        <div class="col-md-6">
                            <label for="bitis_tarihi" class="form-label">Bitiş Tarihi *</label>
                            <input type="datetime-local" class="form-control" id="bitis_tarihi" name="bitis_tarihi"
                                   value="{{ training.bitis_tarihi.strftime('%Y-%m-%dT%H:%M') if training and training.bitis_tarihi else '' }}" required>
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('training.list_trainings') }}" class="btn btn-secondary">
                            <i class="fas fa-times"></i> İptal
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> 
                            {% if training %}Güncelle{% else %}Oluştur{% endif %}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card shadow">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-lightbulb"></i> Yardım</h6>
            </div>
            <div class="card-body">
                <h6>Eğitim Oluşturma İpuçları:</h6>
                <ul class="small">
                    <li>Eğitim adını açık ve anlaşılır yazın</li>
                    <li>Başlangıç tarihi gelecekte olmalıdır</li>
                    <li>Bitiş tarihi başlangıç tarihinden sonra olmalıdır</li>
                    <li>Açıklama alanında eğitim içeriği hakkında bilgi verin</li>
                </ul>
                
                <hr>
                
                <h6>Sonraki Adımlar:</h6>
                <ul class="small">
                    <li>Eğitimi oluşturduktan sonra katılımcı ekleyebilirsiniz</li>
                    <li>Quiz ve anketler oluşturabilirsiniz</li>
                    <li>SMS teyitlerini gönderebilirsiniz</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Tarih validasyonu
    $('#baslangic_tarihi, #bitis_tarihi').on('change', function() {
        var baslangic = new Date($('#baslangic_tarihi').val());
        var bitis = new Date($('#bitis_tarihi').val());

        if (baslangic && bitis && bitis <= baslangic) {
            alert('Bitiş tarihi başlangıç tarihinden sonra olmalıdır.');
            $('#bitis_tarihi').focus();
        }
    });

    // Sayfa yüklendiğinde doğru radio button'ı seç
    {% if training and training.egitmen_user_id %}
    $('#egitmen_sistem').prop('checked', true);
    $('#sistem_egitmen_section').show();
    $('#dis_egitmen_section').hide();
    {% else %}
    $('#egitmen_dis').prop('checked', true);
    $('#sistem_egitmen_section').hide();
    $('#dis_egitmen_section').show();
    {% endif %}
});

// Eğitmen tipi değiştiğinde form alanlarını göster/gizle
$('input[name="egitmen_tipi"]').on('change', function() {
    let tip = $(this).val();

    if (tip === 'sistem') {
        $('#sistem_egitmen_section').show();
        $('#dis_egitmen_section').hide();

        // Dış eğitmen alanlarını temizle
        $('#egitmen_adi').val('');
        $('#egitmen_telefon').val('');
        $('#egitmen_email').val('');
    } else {
        $('#sistem_egitmen_section').hide();
        $('#dis_egitmen_section').show();

        // Sistem eğitmen seçimini temizle
        $('#egitmen_user_id').val('');
    }
});

// Form submit öncesi validasyon
$('form').on('submit', function(e) {
    let egitmenTipi = $('input[name="egitmen_tipi"]:checked').val();

    if (!egitmenTipi) {
        alert('Eğitmen tipi seçilmelidir.');
        e.preventDefault();
        return false;
    }

    if (egitmenTipi === 'sistem') {
        if (!$('#egitmen_user_id').val()) {
            alert('Sistem eğitmeni seçilmelidir.');
            e.preventDefault();
            return false;
        }
    } else {
        if (!$('#egitmen_adi').val() || !$('#egitmen_telefon').val()) {
            alert('Dış eğitmen için ad ve telefon numarası zorunludur.');
            e.preventDefault();
            return false;
        }
    }
});

// Telefon numarası formatı
$('#egitmen_telefon').on('input', function() {
    let value = $(this).val().replace(/\D/g, ''); // Sadece rakamlar
    if (value.length > 0 && !value.startsWith('0')) {
        value = '0' + value;
    }
    $(this).val(value);
});
</script>
{% endblock %}
