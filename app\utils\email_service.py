import smtplib
import uuid
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.mime.base import MIMEBase
from email import encoders
from flask import current_app
import logging

class EmailService:
    """E-posta gönderme servisi"""
    
    def __init__(self):
        self.smtp_server = current_app.config.get('MAIL_SERVER')
        self.smtp_port = current_app.config.get('MAIL_PORT', 587)
        self.use_tls = current_app.config.get('MAIL_USE_TLS', True)
        self.username = current_app.config.get('MAIL_USERNAME')
        self.password = current_app.config.get('MAIL_PASSWORD')
        self.app_name = current_app.config.get('APP_NAME', 'Eğitim Yönetim Sistemi')
        
        if not all([self.smtp_server, self.username, self.password]):
            logging.warning("E-posta konfigürasyonu eksik. E-posta servisi çalışmayacak.")
            self.configured = False
        else:
            self.configured = True
    
    def send_email(self, to_email, subject, html_content=None, text_content=None, attachments=None):
        """
        E-posta gönder
        
        Args:
            to_email (str): Alıcı e-posta adresi
            subject (str): E-posta konusu
            html_content (str): HTML içerik
            text_content (str): Metin içerik
            attachments (list): Ek dosyalar listesi
            
        Returns:
            dict: {'success': bool, 'message_id': str, 'error': str}
        """
        if not self.configured:
            return {
                'success': False,
                'error': 'E-posta konfigürasyonu eksik'
            }
        
        try:
            # E-posta mesajını oluştur
            msg = MIMEMultipart('alternative')
            msg['From'] = self.username
            msg['To'] = to_email
            msg['Subject'] = subject
            
            # Metin içerik ekle
            if text_content:
                text_part = MIMEText(text_content, 'plain', 'utf-8')
                msg.attach(text_part)
            
            # HTML içerik ekle
            if html_content:
                html_part = MIMEText(html_content, 'html', 'utf-8')
                msg.attach(html_part)
            
            # Ek dosyalar ekle
            if attachments:
                for attachment in attachments:
                    self._add_attachment(msg, attachment)
            
            # SMTP bağlantısı kur ve e-posta gönder
            with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
                if self.use_tls:
                    server.starttls()
                
                server.login(self.username, self.password)
                server.send_message(msg)
            
            # Benzersiz message ID oluştur
            message_id = str(uuid.uuid4())
            
            logging.info(f"E-posta gönderildi: {message_id} -> {to_email}")
            
            return {
                'success': True,
                'message_id': message_id
            }
            
        except smtplib.SMTPException as e:
            logging.error(f"SMTP e-posta hatası: {str(e)}")
            return {
                'success': False,
                'error': f"SMTP hatası: {str(e)}"
            }
        except Exception as e:
            logging.error(f"E-posta gönderme hatası: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _add_attachment(self, msg, attachment):
        """E-postaya ek dosya ekle"""
        try:
            if isinstance(attachment, dict):
                filename = attachment.get('filename')
                content = attachment.get('content')
                content_type = attachment.get('content_type', 'application/octet-stream')
            else:
                # Dosya yolu olarak kabul et
                filename = attachment
                with open(attachment, 'rb') as f:
                    content = f.read()
                content_type = 'application/octet-stream'
            
            part = MIMEBase('application', 'octet-stream')
            part.set_payload(content)
            encoders.encode_base64(part)
            part.add_header(
                'Content-Disposition',
                f'attachment; filename= {filename}'
            )
            msg.attach(part)
            
        except Exception as e:
            logging.error(f"Ek dosya ekleme hatası: {str(e)}")
    
    def send_bulk_email(self, recipients, subject, html_content=None, text_content=None):
        """
        Toplu e-posta gönder
        
        Args:
            recipients (list): Alıcı e-posta adresleri listesi
            subject (str): E-posta konusu
            html_content (str): HTML içerik
            text_content (str): Metin içerik
            
        Returns:
            dict: {'success_count': int, 'failed_count': int, 'results': list}
        """
        results = []
        success_count = 0
        failed_count = 0
        
        for email in recipients:
            result = self.send_email(email, subject, html_content, text_content)
            result['email'] = email
            results.append(result)
            
            if result['success']:
                success_count += 1
            else:
                failed_count += 1
        
        return {
            'success_count': success_count,
            'failed_count': failed_count,
            'results': results
        }
    
    def create_survey_email(self, manager_name, training_name, survey_url):
        """
        Anket e-postası oluştur
        
        Args:
            manager_name (str): Yönetici adı
            training_name (str): Eğitim adı
            survey_url (str): Anket linki
            
        Returns:
            dict: {'subject': str, 'html_content': str, 'text_content': str}
        """
        subject = f"{training_name} Eğitimi Değerlendirme Anketi"
        
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>{subject}</title>
            <style>
                body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
                .header {{ background-color: #007bff; color: white; padding: 20px; text-align: center; }}
                .content {{ padding: 20px; background-color: #f9f9f9; }}
                .button {{ 
                    display: inline-block; 
                    background-color: #007bff; 
                    color: white; 
                    padding: 12px 24px; 
                    text-decoration: none; 
                    border-radius: 5px; 
                    margin: 20px 0;
                }}
                .footer {{ padding: 20px; text-align: center; color: #666; font-size: 12px; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>{self.app_name}</h1>
                    <h2>Eğitim Değerlendirme Anketi</h2>
                </div>
                <div class="content">
                    <p>Merhaba <strong>{manager_name}</strong>,</p>
                    <p><strong>{training_name}</strong> eğitimi başarıyla tamamlanmıştır.</p>
                    <p>Eğitimin etkinliği ve çalışanlarınızın performansına etkisi hakkında geri bildiriminizi almak istiyoruz.</p>
                    <p>Lütfen aşağıdaki butona tıklayarak kısa anketimizi doldurun:</p>
                    <p style="text-align: center;">
                        <a href="{survey_url}" class="button">Anketi Doldur</a>
                    </p>
                    <p><strong>Not:</strong> Bu anket linki 7 gün boyunca geçerlidir.</p>
                    <p>Geri bildiriminiz bizim için çok değerli. Teşekkür ederiz.</p>
                </div>
                <div class="footer">
                    <p>Bu e-posta {self.app_name} tarafından otomatik olarak gönderilmiştir.</p>
                    <p>Sorularınız için lütfen sistem yöneticinizle iletişime geçin.</p>
                </div>
            </div>
        </body>
        </html>
        """
        
        text_content = f"""
        {self.app_name} - Eğitim Değerlendirme Anketi
        
        Merhaba {manager_name},
        
        {training_name} eğitimi başarıyla tamamlanmıştır.
        
        Eğitimin etkinliği hakkında geri bildiriminizi almak için lütfen aşağıdaki linke tıklayarak anketi doldurun:
        
        {survey_url}
        
        Bu anket linki 7 gün boyunca geçerlidir.
        
        Teşekkürler,
        {self.app_name}
        """
        
        return {
            'subject': subject,
            'html_content': html_content,
            'text_content': text_content
        }
    
    def validate_email(self, email):
        """
        E-posta adresini doğrula
        
        Args:
            email (str): Kontrol edilecek e-posta adresi
            
        Returns:
            dict: {'valid': bool, 'error': str}
        """
        import re
        
        if not email:
            return {'valid': False, 'error': 'E-posta adresi boş olamaz'}
        
        # Basit e-posta regex kontrolü
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        
        if re.match(pattern, email):
            return {'valid': True}
        else:
            return {'valid': False, 'error': 'Geçersiz e-posta adresi formatı'}
