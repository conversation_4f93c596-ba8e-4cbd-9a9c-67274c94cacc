import logging
import os
import json
from datetime import datetime
from logging.handlers import RotatingFileHandler, TimedRotatingFileHandler
from flask import current_app, request, g
from flask_login import current_user


class LogService:
    """Merkezi log yönetim servisi"""
    
    def __init__(self, app=None):
        self.app = app
        if app is not None:
            self.init_app(app)
    
    def init_app(self, app):
        """Flask uygulaması ile log servisini başlat"""
        self.app = app
        
        # Log klasörünü oluştur
        log_dir = app.config.get('LOG_DIR', 'logs')
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)
        
        # Ana logger'ı yapılandır
        self.setup_main_logger(log_dir)
        
        # Özel logger'ları yapılandır
        self.setup_custom_loggers(log_dir)
        
        # Flask request logging
        self.setup_request_logging(app)
    
    def setup_main_logger(self, log_dir):
        """Ana sistem logger'ın<PERSON> yapılandır"""
        # Ana log dosyası
        main_log_file = os.path.join(log_dir, 'app.log')
        
        # Rotating file handler (10MB, 5 backup)
        file_handler = RotatingFileHandler(
            main_log_file,
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5,
            encoding='utf-8'
        )
        
        # Log formatı
        formatter = logging.Formatter(
            '[%(asctime)s] %(levelname)s in %(module)s: %(message)s'
        )
        file_handler.setFormatter(formatter)
        
        # Flask app logger'ına ekle
        self.app.logger.addHandler(file_handler)
        self.app.logger.setLevel(logging.INFO)
    
    def setup_custom_loggers(self, log_dir):
        """Özel logger'ları yapılandır"""
        
        # Error logger
        error_logger = logging.getLogger('error')
        error_handler = RotatingFileHandler(
            os.path.join(log_dir, 'error.log'),
            maxBytes=5*1024*1024,
            backupCount=3,
            encoding='utf-8'
        )
        error_formatter = logging.Formatter(
            '[%(asctime)s] ERROR: %(message)s'
        )
        error_handler.setFormatter(error_formatter)
        error_logger.addHandler(error_handler)
        error_logger.setLevel(logging.ERROR)
        
        # Security logger
        security_logger = logging.getLogger('security')
        security_handler = RotatingFileHandler(
            os.path.join(log_dir, 'security.log'),
            maxBytes=5*1024*1024,
            backupCount=5,
            encoding='utf-8'
        )
        security_formatter = logging.Formatter(
            '[%(asctime)s] SECURITY: %(message)s'
        )
        security_handler.setFormatter(security_formatter)
        security_logger.addHandler(security_handler)
        security_logger.setLevel(logging.INFO)
        
        # User activity logger
        activity_logger = logging.getLogger('activity')
        activity_handler = TimedRotatingFileHandler(
            os.path.join(log_dir, 'activity.log'),
            when='midnight',
            interval=1,
            backupCount=30,
            encoding='utf-8'
        )
        activity_formatter = logging.Formatter(
            '[%(asctime)s] ACTIVITY: %(message)s'
        )
        activity_handler.setFormatter(activity_formatter)
        activity_logger.addHandler(activity_handler)
        activity_logger.setLevel(logging.INFO)
        
        # Training logger
        training_logger = logging.getLogger('training')
        training_handler = RotatingFileHandler(
            os.path.join(log_dir, 'training.log'),
            maxBytes=5*1024*1024,
            backupCount=3,
            encoding='utf-8'
        )
        training_formatter = logging.Formatter(
            '[%(asctime)s] TRAINING: %(message)s'
        )
        training_handler.setFormatter(training_formatter)
        training_logger.addHandler(training_handler)
        training_logger.setLevel(logging.INFO)
    
    def setup_request_logging(self, app):
        """HTTP request logging"""
        @app.before_request
        def log_request_info():
            # Request bilgilerini logla
            if request.endpoint not in ['static']:
                user_info = 'Anonymous'
                if current_user and current_user.is_authenticated:
                    user_info = f"{current_user.email} ({current_user.id})"
                
                self.log_activity(
                    'REQUEST',
                    f"{request.method} {request.path}",
                    user=user_info,
                    ip=request.remote_addr,
                    user_agent=request.headers.get('User-Agent', '')[:100]
                )
        
        @app.after_request
        def log_response_info(response):
            # Response bilgilerini logla
            if request.endpoint not in ['static']:
                user_info = 'Anonymous'
                if current_user and current_user.is_authenticated:
                    user_info = f"{current_user.email} ({current_user.id})"
                
                self.log_activity(
                    'RESPONSE',
                    f"{request.method} {request.path} -> {response.status_code}",
                    user=user_info,
                    ip=request.remote_addr
                )
            return response
    
    def log_info(self, message, category='GENERAL', **kwargs):
        """Bilgi seviyesi log"""
        self._log(logging.INFO, message, category, **kwargs)
    
    def log_warning(self, message, category='GENERAL', **kwargs):
        """Uyarı seviyesi log"""
        self._log(logging.WARNING, message, category, **kwargs)
    
    def log_error(self, message, category='GENERAL', **kwargs):
        """Hata seviyesi log"""
        self._log(logging.ERROR, message, category, **kwargs)
        
        # Error logger'a da gönder
        error_logger = logging.getLogger('error')
        error_logger.error(self._format_message(message, category, **kwargs))
    
    def log_security(self, action, details, **kwargs):
        """Güvenlik olayları"""
        security_logger = logging.getLogger('security')
        message = f"{action}: {details}"
        security_logger.info(self._format_message(message, 'SECURITY', **kwargs))
    
    def log_activity(self, action, details, **kwargs):
        """Kullanıcı aktiviteleri"""
        activity_logger = logging.getLogger('activity')
        message = f"{action}: {details}"
        activity_logger.info(self._format_message(message, 'ACTIVITY', **kwargs))
    
    def log_training(self, action, training_id, details, **kwargs):
        """Eğitim ile ilgili loglar"""
        training_logger = logging.getLogger('training')
        message = f"{action} (Training ID: {training_id}): {details}"
        training_logger.info(self._format_message(message, 'TRAINING', **kwargs))
    
    def _log(self, level, message, category='GENERAL', **kwargs):
        """İç log metodu"""
        formatted_message = self._format_message(message, category, **kwargs)
        self.app.logger.log(level, formatted_message)
    
    def _format_message(self, message, category='GENERAL', **kwargs):
        """Log mesajını formatla"""
        # Kullanıcı bilgisi
        user_info = kwargs.get('user', 'System')
        if not user_info or user_info == 'System':
            if current_user and current_user.is_authenticated:
                user_info = f"{current_user.email} ({current_user.id})"
        
        # IP adresi
        ip = kwargs.get('ip', 'Unknown')
        if not ip or ip == 'Unknown':
            if request:
                ip = request.remote_addr or 'Unknown'
        
        # Ek bilgiler
        extra_info = []
        for key, value in kwargs.items():
            if key not in ['user', 'ip']:
                extra_info.append(f"{key}={value}")
        
        # Mesajı birleştir
        parts = [f"[{category}]", f"User: {user_info}", f"IP: {ip}", message]
        if extra_info:
            parts.append(f"Extra: {', '.join(extra_info)}")
        
        return " | ".join(parts)
    
    def get_log_files(self, log_dir=None):
        """Log dosyalarının listesini getir"""
        if not log_dir:
            log_dir = self.app.config.get('LOG_DIR', 'logs')
        
        if not os.path.exists(log_dir):
            return []
        
        log_files = []
        for filename in os.listdir(log_dir):
            if filename.endswith('.log'):
                filepath = os.path.join(log_dir, filename)
                stat = os.stat(filepath)
                
                log_files.append({
                    'name': filename,
                    'size': round(stat.st_size / 1024, 2),  # KB
                    'modified': datetime.fromtimestamp(stat.st_mtime),
                    'path': filepath
                })
        
        # Son değişiklik tarihine göre sırala
        log_files.sort(key=lambda x: x['modified'], reverse=True)
        return log_files
    
    def read_log_file(self, filename, lines=100):
        """Log dosyasını oku"""
        log_dir = self.app.config.get('LOG_DIR', 'logs')
        filepath = os.path.join(log_dir, filename)
        
        if not os.path.exists(filepath):
            return None
        
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                all_lines = f.readlines()
                # Son N satırı al
                return ''.join(all_lines[-lines:]) if lines else ''.join(all_lines)
        except Exception as e:
            return f"Log dosyası okunamadı: {str(e)}"
    
    def clear_log_file(self, filename):
        """Log dosyasını temizle"""
        log_dir = self.app.config.get('LOG_DIR', 'logs')
        filepath = os.path.join(log_dir, filename)
        
        if os.path.exists(filepath):
            try:
                with open(filepath, 'w', encoding='utf-8') as f:
                    f.write('')
                return True
            except Exception:
                return False
        return False
    
    def delete_log_file(self, filename):
        """Log dosyasını sil"""
        log_dir = self.app.config.get('LOG_DIR', 'logs')
        filepath = os.path.join(log_dir, filename)
        
        if os.path.exists(filepath):
            try:
                os.remove(filepath)
                return True
            except Exception:
                return False
        return False


# Global log service instance
log_service = LogService()
