{% extends "base.html" %}

{% block title %}Sistem Ayarları - Eğitim Yönetim Sistemi{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-cogs"></i> Sistem Ayarları
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-outline-info" onclick="showSystemInfo()">
                <i class="fas fa-info-circle"></i> Sistem Bilgileri
            </button>
            <button type="button" class="btn btn-outline-warning" onclick="showLogs()">
                <i class="fas fa-file-alt"></i> Loglar
            </button>
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                    <i class="fas fa-tools"></i> Araçlar
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="#" onclick="backupSettings()">
                        <i class="fas fa-download"></i> Ayarları Yedekle
                    </a></li>
                    <li><a class="dropdown-item" href="#" onclick="showRestoreModal()">
                        <i class="fas fa-upload"></i> Ayarları Geri Yükle
                    </a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item text-danger" href="#" onclick="resetAllSettings()">
                        <i class="fas fa-undo"></i> Tüm Ayarları Sıfırla
                    </a></li>
                </ul>
            </div>
        </div>
    </div>
</div>

<!-- Ayar Kategorileri -->
<div class="row">
    {% for category_key, category_info in categories.items() %}
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card h-100 shadow-sm category-card" onclick="openCategory('{{ category_key }}')">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="{{ category_info.icon }} fa-3x text-primary"></i>
                </div>
                <h5 class="card-title">{{ category_info.name }}</h5>
                <p class="card-text text-muted">{{ category_info.description }}</p>
            </div>
            <div class="card-footer bg-transparent">
                <div class="d-flex justify-content-between align-items-center">
                    <small class="text-muted">
                        <i class="fas fa-cog"></i> Ayarları Düzenle
                    </small>
                    <i class="fas fa-arrow-right text-primary"></i>
                </div>
            </div>
        </div>
    </div>
    {% endfor %}
</div>

<!-- Hızlı Erişim Kartları -->
<div class="row mt-4">
    <div class="col-12">
        <h4 class="mb-3">
            <i class="fas fa-tachometer-alt"></i> Hızlı Erişim
        </h4>
    </div>
    
    <!-- SMS Test -->
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card border-info">
            <div class="card-body text-center">
                <i class="fas fa-sms fa-2x text-info mb-2"></i>
                <h6 class="card-title">SMS Testi</h6>
                <button class="btn btn-outline-info btn-sm" onclick="showSmsTestModal()">
                    <i class="fas fa-paper-plane"></i> Test Gönder
                </button>
            </div>
        </div>
    </div>
    
    <!-- Email Test -->
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card border-success">
            <div class="card-body text-center">
                <i class="fas fa-envelope fa-2x text-success mb-2"></i>
                <h6 class="card-title">Email Testi</h6>
                <button class="btn btn-outline-success btn-sm" onclick="showEmailTestModal()">
                    <i class="fas fa-paper-plane"></i> Test Gönder
                </button>
            </div>
        </div>
    </div>
    
    <!-- Sistem Durumu -->
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card border-warning">
            <div class="card-body text-center">
                <i class="fas fa-heartbeat fa-2x text-warning mb-2"></i>
                <h6 class="card-title">Sistem Durumu</h6>
                <button class="btn btn-outline-warning btn-sm" onclick="checkSystemHealth()">
                    <i class="fas fa-stethoscope"></i> Kontrol Et
                </button>
            </div>
        </div>
    </div>
    
    <!-- Cache Temizle -->
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card border-danger">
            <div class="card-body text-center">
                <i class="fas fa-broom fa-2x text-danger mb-2"></i>
                <h6 class="card-title">Cache Temizle</h6>
                <button class="btn btn-outline-danger btn-sm" onclick="clearCache()">
                    <i class="fas fa-trash"></i> Temizle
                </button>
            </div>
        </div>
    </div>
</div>

<!-- SMS Test Modal -->
<div class="modal fade" id="smsTestModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-sms"></i> SMS Testi
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="smsTestForm">
                    <div class="mb-3">
                        <label for="testPhone" class="form-label">Telefon Numarası</label>
                        <input type="tel" class="form-control" id="testPhone" placeholder="+90 555 123 45 67" required>
                        <div class="form-text">Ülke kodu ile birlikte giriniz</div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">İptal</button>
                <button type="button" class="btn btn-info" onclick="sendTestSms()">
                    <i class="fas fa-paper-plane"></i> Test SMS Gönder
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Email Test Modal -->
<div class="modal fade" id="emailTestModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-envelope"></i> Email Testi
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="emailTestForm">
                    <div class="mb-3">
                        <label for="testEmail" class="form-label">Email Adresi</label>
                        <input type="email" class="form-control" id="testEmail" placeholder="<EMAIL>" required>
                        <div class="form-text">Test emaili gönderilecek adres</div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">İptal</button>
                <button type="button" class="btn btn-success" onclick="sendTestEmail()">
                    <i class="fas fa-paper-plane"></i> Test Email Gönder
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Restore Modal -->
<div class="modal fade" id="restoreModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-upload"></i> Ayarları Geri Yükle
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="backupFile" class="form-label">Yedek Dosyası</label>
                    <input type="file" class="form-control" id="backupFile" accept=".json">
                    <div class="form-text">JSON formatında yedek dosyası seçin</div>
                </div>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>Uyarı:</strong> Bu işlem mevcut ayarları değiştirecektir. Devam etmeden önce mevcut ayarlarınızı yedeklemeniz önerilir.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">İptal</button>
                <button type="button" class="btn btn-warning" onclick="restoreSettings()">
                    <i class="fas fa-upload"></i> Geri Yükle
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.category-card {
    cursor: pointer;
    transition: all 0.3s ease;
}

.category-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
}

.category-card:hover .fa-arrow-right {
    transform: translateX(5px);
    transition: transform 0.3s ease;
}

.card-footer {
    border-top: none !important;
}

.btn-group .dropdown-menu {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}
</style>
{% endblock %}

{% block extra_js %}
<script>
function openCategory(category) {
    window.location.href = `/settings/category/${category}`;
}

function showSmsTestModal() {
    $('#smsTestModal').modal('show');
}

function showEmailTestModal() {
    $('#emailTestModal').modal('show');
}

function showRestoreModal() {
    $('#restoreModal').modal('show');
}

function sendTestSms() {
    const phone = $('#testPhone').val();
    if (!phone) {
        alert('Telefon numarası gerekli');
        return;
    }
    
    fetch('/settings/test-sms', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({phone: phone})
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('✅ ' + data.message);
            $('#smsTestModal').modal('hide');
        } else {
            alert('❌ ' + data.message);
        }
    })
    .catch(error => {
        alert('❌ Hata: ' + error);
    });
}

function sendTestEmail() {
    const email = $('#testEmail').val();
    if (!email) {
        alert('Email adresi gerekli');
        return;
    }
    
    fetch('/settings/test-email', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({email: email})
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('✅ ' + data.message);
            $('#emailTestModal').modal('hide');
        } else {
            alert('❌ ' + data.message);
        }
    })
    .catch(error => {
        alert('❌ Hata: ' + error);
    });
}

function backupSettings() {
    fetch('/settings/backup', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // JSON dosyası olarak indir
            const blob = new Blob([JSON.stringify(data.data, null, 2)], {type: 'application/json'});
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `settings_backup_${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
            
            alert('✅ ' + data.message);
        } else {
            alert('❌ ' + data.message);
        }
    })
    .catch(error => {
        alert('❌ Hata: ' + error);
    });
}

function restoreSettings() {
    const fileInput = $('#backupFile')[0];
    if (!fileInput.files[0]) {
        alert('Lütfen bir yedek dosyası seçin');
        return;
    }
    
    const file = fileInput.files[0];
    const reader = new FileReader();
    
    reader.onload = function(e) {
        try {
            const backupData = JSON.parse(e.target.result);
            
            fetch('/settings/restore', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({backup_data: backupData})
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('✅ ' + data.message);
                    $('#restoreModal').modal('hide');
                    location.reload();
                } else {
                    alert('❌ ' + data.message);
                }
            })
            .catch(error => {
                alert('❌ Hata: ' + error);
            });
            
        } catch (error) {
            alert('❌ Geçersiz JSON dosyası');
        }
    };
    
    reader.readAsText(file);
}

function showSystemInfo() {
    window.open('/settings/system-info', '_blank');
}

function showLogs() {
    window.open('/settings/logs', '_blank');
}

function checkSystemHealth() {
    // Sistem sağlık kontrolü
    alert('🔄 Sistem sağlık kontrolü başlatılıyor...');
    // TODO: Implement system health check
}

function clearCache() {
    if (confirm('Cache temizlensin mi?')) {
        alert('🔄 Cache temizleniyor...');
        // TODO: Implement cache clearing
    }
}

function resetAllSettings() {
    if (confirm('TÜM ayarları varsayılan değerlere sıfırlamak istediğinizden emin misiniz?\n\nBu işlem geri alınamaz!')) {
        alert('🔄 Ayarlar sıfırlanıyor...');
        // TODO: Implement reset all settings
    }
}
</script>
{% endblock %}
