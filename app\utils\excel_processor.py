#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""Excel dosyası işleme utility'si"""

import os
import pandas as pd
from werkzeug.utils import secure_filename
from app.models.user import User
from app import db

class ExcelUserProcessor:
    """Excel'den kullanıcı verilerini işleyen sınıf"""
    
    def __init__(self, upload_folder):
        self.upload_folder = upload_folder
        self.required_columns = ['ad', 'soyad', 'email', 'telefon_numarasi', 'rol']
        self.optional_columns = ['sifre', 'manager_email']
        self.valid_roles = ['participant', 'manager', 'admin']
        
    def allowed_file(self, filename):
        """Dosya uzantısı kontrolü"""
        return '.' in filename and \
               filename.rsplit('.', 1)[1].lower() in {'xlsx', 'xls'}
    
    def save_uploaded_file(self, file):
        """Upload edilen dosyayı kaydet"""
        if file and self.allowed_file(file.filename):
            filename = secure_filename(file.filename)
            filepath = os.path.join(self.upload_folder, filename)
            file.save(filepath)
            return filepath
        return None
    
    def read_excel_file(self, filepath):
        """Excel dosyasını oku"""
        try:
            # Excel dosyasını oku
            df = pd.read_excel(filepath)
            
            # Boş satırları temizle
            df = df.dropna(how='all')
            
            # Sütun isimlerini küçük harfe çevir ve boşlukları temizle
            df.columns = df.columns.str.lower().str.strip().str.replace(' ', '_')
            
            return df, None
        except Exception as e:
            return None, f"Excel dosyası okunamadı: {str(e)}"
    
    def validate_dataframe(self, df):
        """DataFrame'i doğrula"""
        errors = []
        
        # Gerekli sütunları kontrol et
        missing_columns = []
        for col in self.required_columns:
            if col not in df.columns:
                missing_columns.append(col)
        
        if missing_columns:
            errors.append(f"Eksik sütunlar: {', '.join(missing_columns)}")
        
        # Veri tiplerini kontrol et
        if not errors:
            for index, row in df.iterrows():
                row_errors = []
                
                # Ad kontrolü
                if pd.isna(row.get('ad')) or str(row.get('ad')).strip() == '':
                    row_errors.append("Ad boş olamaz")
                
                # Soyad kontrolü
                if pd.isna(row.get('soyad')) or str(row.get('soyad')).strip() == '':
                    row_errors.append("Soyad boş olamaz")
                
                # Email kontrolü
                email = str(row.get('email', '')).strip().lower()
                if not email or '@' not in email:
                    row_errors.append("Geçersiz email adresi")
                
                # Telefon kontrolü
                telefon = str(row.get('telefon_numarasi', '')).strip()
                if not telefon:
                    row_errors.append("Telefon numarası boş olamaz")
                
                # Rol kontrolü
                rol = str(row.get('rol', '')).strip().lower()
                if rol not in self.valid_roles:
                    row_errors.append(f"Geçersiz rol: {rol}. Geçerli roller: {', '.join(self.valid_roles)}")
                
                if row_errors:
                    errors.append(f"Satır {index + 2}: {'; '.join(row_errors)}")
        
        return errors
    
    def process_users(self, df):
        """Kullanıcıları işle ve veritabanına kaydet"""
        results = {
            'success_count': 0,
            'error_count': 0,
            'errors': [],
            'created_users': []
        }
        
        for index, row in df.iterrows():
            try:
                # Verileri temizle
                ad = str(row.get('ad', '')).strip()
                soyad = str(row.get('soyad', '')).strip()
                email = str(row.get('email', '')).strip().lower()
                telefon_numarasi = str(row.get('telefon_numarasi', '')).strip()
                rol = str(row.get('rol', '')).strip().lower()
                sifre = str(row.get('sifre', '123456')).strip()  # Varsayılan şifre
                manager_email = str(row.get('manager_email', '')).strip().lower() if pd.notna(row.get('manager_email')) else None
                
                # Email kontrolü - zaten var mı?
                existing_user = User.query.filter_by(email=email).first()
                if existing_user:
                    results['errors'].append(f"Satır {index + 2}: Email {email} zaten kullanılıyor")
                    results['error_count'] += 1
                    continue

                # Manager kontrolü
                manager_id = None
                if manager_email and rol == 'participant':
                    manager = User.query.filter_by(email=manager_email).first()
                    if manager and manager.rol in ['manager', 'admin']:
                        manager_id = manager.id
                    elif manager_email:
                        results['errors'].append(f"Satır {index + 2}: Yönetici email {manager_email} bulunamadı veya geçersiz")
                        results['error_count'] += 1
                        continue
                
                # Yeni kullanıcı oluştur
                user = User(
                    ad=ad,
                    soyad=soyad,
                    email=email,
                    telefon_numarasi=telefon_numarasi,
                    rol=rol,
                    manager_id=manager_id
                )
                user.set_password(sifre)
                
                db.session.add(user)
                db.session.flush()  # ID'yi al ama henüz commit etme
                
                results['created_users'].append({
                    'ad': ad,
                    'soyad': soyad,
                    'email': email,
                    'rol': rol
                })
                results['success_count'] += 1
                
            except Exception as e:
                results['errors'].append(f"Satır {index + 2}: {str(e)}")
                results['error_count'] += 1
                db.session.rollback()
        
        # Tüm işlemler başarılıysa commit et
        if results['error_count'] == 0:
            try:
                db.session.commit()
            except Exception as e:
                db.session.rollback()
                results['errors'].append(f"Veritabanı hatası: {str(e)}")
                results['error_count'] = results['success_count']
                results['success_count'] = 0
                results['created_users'] = []
        else:
            db.session.rollback()
        
        return results
    
    def process_excel_file(self, file):
        """Excel dosyasını tamamen işle"""
        # Dosyayı kaydet
        filepath = self.save_uploaded_file(file)
        if not filepath:
            return None, ["Geçersiz dosya formatı. Sadece .xlsx ve .xls dosyaları kabul edilir."]
        
        try:
            # Excel'i oku
            df, read_error = self.read_excel_file(filepath)
            if read_error:
                return None, [read_error]
            
            # Doğrula
            validation_errors = self.validate_dataframe(df)
            if validation_errors:
                return None, validation_errors
            
            # İşle
            results = self.process_users(df)
            return results, []
            
        finally:
            # Geçici dosyayı sil
            try:
                if filepath and os.path.exists(filepath):
                    os.remove(filepath)
            except:
                pass
    
    def create_sample_excel(self, filepath):
        """Örnek Excel dosyası oluştur"""
        sample_data = {
            'ad': ['Ahmet', 'Ayşe', 'Mehmet', 'Fatma'],
            'soyad': ['Yılmaz', 'Kaya', 'Demir', 'Şahin'],
            'email': ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>'],
            'telefon_numarasi': ['05551234567', '05551234568', '05551234569', '05551234570'],
            'rol': ['manager', 'participant', 'participant', 'participant'],
            'sifre': ['123456', '123456', '123456', '123456'],
            'manager_email': ['', '<EMAIL>', '<EMAIL>', '<EMAIL>']
        }
        
        df = pd.DataFrame(sample_data)
        df.to_excel(filepath, index=False, engine='openpyxl')
        return filepath
