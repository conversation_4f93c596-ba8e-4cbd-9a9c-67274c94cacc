r"""
    This code was generated by
   ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
    |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
    |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

    Twilio - Oauth
    This is the public Twilio REST API.

    NOTE: This class is auto generated by OpenAPI Generator.
    https://openapi-generator.tech
    Do not edit the class manually.
"""


from typing import Any, Dict, Optional
from twilio.base.instance_context import InstanceContext
from twilio.base.instance_resource import InstanceResource
from twilio.base.list_resource import ListResource
from twilio.base.version import Version


class UserInfoInstance(InstanceResource):

    """
    :ivar user_sid: The URL of the party that will create the token and sign it with its private key.
    :ivar first_name: The first name of the end-user.
    :ivar last_name: The last name of the end-user.
    :ivar friendly_name: The friendly name of the end-user.
    :ivar email: The end-user's preferred email address.
    :ivar url:
    """

    def __init__(self, version: Version, payload: Dict[str, Any]):
        super().__init__(version)

        self.user_sid: Optional[str] = payload.get("user_sid")
        self.first_name: Optional[str] = payload.get("first_name")
        self.last_name: Optional[str] = payload.get("last_name")
        self.friendly_name: Optional[str] = payload.get("friendly_name")
        self.email: Optional[str] = payload.get("email")
        self.url: Optional[str] = payload.get("url")

        self._context: Optional[UserInfoContext] = None

    @property
    def _proxy(self) -> "UserInfoContext":
        """
        Generate an instance context for the instance, the context is capable of
        performing various actions. All instance actions are proxied to the context

        :returns: UserInfoContext for this UserInfoInstance
        """
        if self._context is None:
            self._context = UserInfoContext(
                self._version,
            )
        return self._context

    def fetch(self) -> "UserInfoInstance":
        """
        Fetch the UserInfoInstance


        :returns: The fetched UserInfoInstance
        """
        return self._proxy.fetch()

    async def fetch_async(self) -> "UserInfoInstance":
        """
        Asynchronous coroutine to fetch the UserInfoInstance


        :returns: The fetched UserInfoInstance
        """
        return await self._proxy.fetch_async()

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """

        return "<Twilio.Oauth.V1.UserInfoInstance>"


class UserInfoContext(InstanceContext):
    def __init__(self, version: Version):
        """
        Initialize the UserInfoContext

        :param version: Version that contains the resource
        """
        super().__init__(version)

        self._uri = "/userinfo"

    def fetch(self) -> UserInfoInstance:
        """
        Fetch the UserInfoInstance


        :returns: The fetched UserInfoInstance
        """

        payload = self._version.fetch(
            method="GET",
            uri=self._uri,
        )

        return UserInfoInstance(
            self._version,
            payload,
        )

    async def fetch_async(self) -> UserInfoInstance:
        """
        Asynchronous coroutine to fetch the UserInfoInstance


        :returns: The fetched UserInfoInstance
        """

        payload = await self._version.fetch_async(
            method="GET",
            uri=self._uri,
        )

        return UserInfoInstance(
            self._version,
            payload,
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """

        return "<Twilio.Oauth.V1.UserInfoContext>"


class UserInfoList(ListResource):
    def __init__(self, version: Version):
        """
        Initialize the UserInfoList

        :param version: Version that contains the resource

        """
        super().__init__(version)

    def get(self) -> UserInfoContext:
        """
        Constructs a UserInfoContext

        """
        return UserInfoContext(self._version)

    def __call__(self) -> UserInfoContext:
        """
        Constructs a UserInfoContext

        """
        return UserInfoContext(self._version)

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Oauth.V1.UserInfoList>"
