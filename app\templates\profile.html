{% extends "base.html" %}

{% block title %}Profil - {{ user.tam_ad }}{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-user"></i> Profil
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('auth.edit_profile') }}" class="btn btn-outline-warning">
                <i class="fas fa-edit"></i> <PERSON><PERSON>
            </a>
            <a href="{{ url_for('auth.change_password') }}" class="btn btn-outline-primary">
                <i class="fas fa-key"></i> <PERSON><PERSON><PERSON>
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-4">
        <!-- <PERSON><PERSON> -->
        <div class="card shadow mb-4">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="fas fa-user-circle fa-5x text-muted"></i>
                </div>
                <h4 class="mb-1">{{ user.tam_ad }}</h4>
                <p class="text-muted mb-3">{{ user.email }}</p>
                
                <span class="badge 
                    {% if user.rol == 'admin' %}bg-danger
                    {% elif user.rol == 'manager' %}bg-warning
                    {% else %}bg-info{% endif %} mb-3">
                    {% if user.rol == 'admin' %}Admin
                    {% elif user.rol == 'manager' %}Manager
                    {% else %}Participant{% endif %}
                </span>
                
                <div class="row text-center">
                    <div class="col-6">
                        <div class="h5 mb-0 text-primary">{{ user.enrollments.filter_by(aktif_mi=True).count() }}</div>
                        <small class="text-muted">Eğitim</small>
                    </div>
                    <div class="col-6">
                        <div class="h5 mb-0 text-success">{{ user.quiz_attempts.filter_by(tamamlandi_mi=True).count() }}</div>
                        <small class="text-muted">Quiz</small>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- İletişim Bilgileri -->
        <div class="card shadow">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-address-card"></i> İletişim Bilgileri</h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label class="form-label text-muted">E-posta</label>
                    <div>{{ user.email }}</div>
                </div>
                <div class="mb-3">
                    <label class="form-label text-muted">Telefon</label>
                    <div>{{ user.telefon_numarasi }}</div>
                </div>
                <div class="mb-3">
                    <label class="form-label text-muted">Durum</label>
                    <div>
                        <span class="badge {{ 'bg-success' if user.aktif_mi else 'bg-secondary' }}">
                            {{ 'Aktif' if user.aktif_mi else 'Pasif' }}
                        </span>
                    </div>
                </div>
                <div class="mb-3">
                    <label class="form-label text-muted">Kayıt Tarihi</label>
                    <div>{{ user.olusturulma_tarihi.strftime('%d.%m.%Y %H:%M') if user.olusturulma_tarihi }}</div>
                </div>
                <div>
                    <label class="form-label text-muted">Son Giriş</label>
                    <div>
                        {% if user.son_giris_tarihi %}
                        {{ user.son_giris_tarihi.strftime('%d.%m.%Y %H:%M') }}
                        {% else %}
                        Hiç giriş yapmamış
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-8">
        <!-- Eğitimlerim -->
        <div class="card shadow mb-4">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-book"></i> Eğitimlerim</h6>
            </div>
            <div class="card-body">
                {% set my_enrollments = user.enrollments.filter_by(aktif_mi=True).limit(5).all() %}
                {% if my_enrollments %}
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Eğitim</th>
                                <th>Tarih</th>
                                <th>Durum</th>
                                <th>İşlem</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for enrollment in my_enrollments %}
                            <tr>
                                <td>{{ enrollment.training.ad }}</td>
                                <td>{{ enrollment.training.baslangic_tarihi.strftime('%d.%m.%Y') }}</td>
                                <td>
                                    <span class="badge 
                                        {% if enrollment.katilim_teyidi_durumu == 'teyit_edildi' %}bg-success
                                        {% elif enrollment.katilim_teyidi_durumu == 'reddedildi' %}bg-danger
                                        {% else %}bg-warning{% endif %}">
                                        {% if enrollment.katilim_teyidi_durumu == 'teyit_edildi' %}Teyit Edildi
                                        {% elif enrollment.katilim_teyidi_durumu == 'reddedildi' %}Reddedildi
                                        {% else %}Beklemede{% endif %}
                                    </span>
                                </td>
                                <td>
                                    <a href="{{ url_for('training.detail', training_id=enrollment.training.id) }}" 
                                       class="btn btn-outline-primary btn-sm">
                                        <i class="fas fa-eye"></i> Detay
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                {% if user.enrollments.filter_by(aktif_mi=True).count() > 5 %}
                <div class="text-center mt-3">
                    <a href="{{ url_for('training.list_trainings') }}" class="btn btn-outline-primary">
                        <i class="fas fa-list"></i> Tüm Eğitimlerim
                    </a>
                </div>
                {% endif %}
                {% else %}
                <div class="text-center text-muted">
                    <i class="fas fa-book fa-2x mb-3"></i>
                    <p>Henüz hiçbir eğitime kayıtlı değilsiniz.</p>
                </div>
                {% endif %}
            </div>
        </div>
        
        <!-- Quiz Sonuçlarım -->
        {% if user.is_participant %}
        <div class="card shadow mb-4">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-chart-line"></i> Quiz Performansım</h6>
            </div>
            <div class="card-body">
                {% set my_attempts = user.quiz_attempts.filter_by(tamamlandi_mi=True).limit(5).all() %}
                {% if my_attempts %}
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Quiz</th>
                                <th>Skor</th>
                                <th>Durum</th>
                                <th>Tarih</th>
                                <th>İşlem</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for attempt in my_attempts %}
                            <tr>
                                <td>{{ attempt.quiz.ad }}</td>
                                <td>
                                    <span class="badge {{ 'bg-success' if attempt.gecme_durumu else 'bg-danger' }}">
                                        {{ "%.1f"|format(attempt.skor) }}%
                                    </span>
                                </td>
                                <td>
                                    {% if attempt.gecme_durumu %}
                                    <i class="fas fa-check-circle text-success"></i> Geçti
                                    {% else %}
                                    <i class="fas fa-times-circle text-danger"></i> Kaldı
                                    {% endif %}
                                </td>
                                <td>{{ attempt.deneme_tarihi.strftime('%d.%m.%Y') }}</td>
                                <td>
                                    <a href="{{ url_for('quiz.result', attempt_id=attempt.id) }}" 
                                       class="btn btn-outline-info btn-sm">
                                        <i class="fas fa-eye"></i> Sonuç
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                <!-- İstatistikler -->
                {% set total_attempts = user.quiz_attempts.filter_by(tamamlandi_mi=True).count() %}
                {% set passed_attempts = user.quiz_attempts.filter_by(tamamlandi_mi=True, gecme_durumu=True).count() %}
                {% set avg_score = user.quiz_attempts.filter_by(tamamlandi_mi=True).all() %}
                
                <div class="row text-center mt-3">
                    <div class="col-3">
                        <div class="h5 mb-0 text-primary">{{ total_attempts }}</div>
                        <small class="text-muted">Toplam Quiz</small>
                    </div>
                    <div class="col-3">
                        <div class="h5 mb-0 text-success">{{ passed_attempts }}</div>
                        <small class="text-muted">Geçilen</small>
                    </div>
                    <div class="col-3">
                        <div class="h5 mb-0 text-info">
                            {% if total_attempts > 0 %}
                            {{ "%.1f"|format((passed_attempts / total_attempts) * 100) }}%
                            {% else %}
                            0%
                            {% endif %}
                        </div>
                        <small class="text-muted">Başarı Oranı</small>
                    </div>
                    <div class="col-3">
                        <div class="h5 mb-0 text-warning">
                            {% if avg_score %}
                            {{ "%.1f"|format(avg_score|map(attribute='skor')|sum / avg_score|length) }}%
                            {% else %}
                            0%
                            {% endif %}
                        </div>
                        <small class="text-muted">Ortalama</small>
                    </div>
                </div>
                {% else %}
                <div class="text-center text-muted">
                    <i class="fas fa-question-circle fa-2x mb-3"></i>
                    <p>Henüz hiç quiz çözmediniz.</p>
                </div>
                {% endif %}
            </div>
        </div>
        {% endif %}
        
        <!-- Yönetici İstatistikleri -->
        {% if user.can_manage_trainings() %}
        <div class="card shadow">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-chart-bar"></i> Yönetici İstatistiklerim</h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-3">
                        <div class="h4 mb-0 text-primary">{{ user.olusturulan_egitimler.filter_by(aktif_mi=True).count() }}</div>
                        <small class="text-muted">Oluşturulan Eğitim</small>
                    </div>
                    <div class="col-md-3">
                        <div class="h4 mb-0 text-success">{{ user.olusturulan_quizler.filter_by(aktif_mi=True).count() }}</div>
                        <small class="text-muted">Oluşturulan Quiz</small>
                    </div>
                    <div class="col-md-3">
                        <div class="h4 mb-0 text-info">
                            {% set total_participants = user.olusturulan_egitimler.join('enrollments').filter_by(aktif_mi=True).count() %}
                            {{ total_participants }}
                        </div>
                        <small class="text-muted">Toplam Katılımcı</small>
                    </div>
                    <div class="col-md-3">
                        <div class="h4 mb-0 text-warning">{{ user.olusturulan_anketler.filter_by(aktif_mi=True).count() }}</div>
                        <small class="text-muted">Oluşturulan Anket</small>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
