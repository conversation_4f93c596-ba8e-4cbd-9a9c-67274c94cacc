r"""
    This code was generated by
   ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
    |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
    |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

    Twilio - Trusthub
    This is the public Twilio REST API.

    NOTE: This class is auto generated by OpenAPI Generator.
    https://openapi-generator.tech
    Do not edit the class manually.
"""


from datetime import datetime
from typing import Any, Dict, List, Optional, Union, Iterator, AsyncIterator
from twilio.base import deserialize, values
from twilio.base.instance_context import InstanceContext
from twilio.base.instance_resource import InstanceResource
from twilio.base.list_resource import ListResource
from twilio.base.version import Version
from twilio.base.page import Page
from twilio.rest.trusthub.v1.customer_profiles.customer_profiles_channel_endpoint_assignment import (
    CustomerProfilesChannelEndpointAssignmentList,
)
from twilio.rest.trusthub.v1.customer_profiles.customer_profiles_entity_assignments import (
    CustomerProfilesEntityAssignmentsList,
)
from twilio.rest.trusthub.v1.customer_profiles.customer_profiles_evaluations import (
    CustomerProfilesEvaluationsList,
)


class CustomerProfilesInstance(InstanceResource):
    class Status(object):
        DRAFT = "draft"
        PENDING_REVIEW = "pending-review"
        IN_REVIEW = "in-review"
        TWILIO_REJECTED = "twilio-rejected"
        TWILIO_APPROVED = "twilio-approved"

    """
    :ivar sid: The unique string that we created to identify the Customer-Profile resource.
    :ivar account_sid: The SID of the [Account](https://www.twilio.com/docs/iam/api/account) that created the Customer-Profile resource.
    :ivar policy_sid: The unique string of a policy that is associated to the Customer-Profile resource.
    :ivar friendly_name: The string that you assigned to describe the resource.
    :ivar status: 
    :ivar valid_until: The date and time in GMT in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format when the resource will be valid until.
    :ivar email: The email address that will receive updates when the Customer-Profile resource changes status.
    :ivar status_callback: The URL we call to inform your application of status changes.
    :ivar date_created: The date and time in GMT when the resource was created specified in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format.
    :ivar date_updated: The date and time in GMT when the resource was last updated specified in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format.
    :ivar url: The absolute URL of the Customer-Profile resource.
    :ivar links: The URLs of the Assigned Items of the Customer-Profile resource.
    """

    def __init__(
        self, version: Version, payload: Dict[str, Any], sid: Optional[str] = None
    ):
        super().__init__(version)

        self.sid: Optional[str] = payload.get("sid")
        self.account_sid: Optional[str] = payload.get("account_sid")
        self.policy_sid: Optional[str] = payload.get("policy_sid")
        self.friendly_name: Optional[str] = payload.get("friendly_name")
        self.status: Optional["CustomerProfilesInstance.Status"] = payload.get("status")
        self.valid_until: Optional[datetime] = deserialize.iso8601_datetime(
            payload.get("valid_until")
        )
        self.email: Optional[str] = payload.get("email")
        self.status_callback: Optional[str] = payload.get("status_callback")
        self.date_created: Optional[datetime] = deserialize.iso8601_datetime(
            payload.get("date_created")
        )
        self.date_updated: Optional[datetime] = deserialize.iso8601_datetime(
            payload.get("date_updated")
        )
        self.url: Optional[str] = payload.get("url")
        self.links: Optional[Dict[str, object]] = payload.get("links")

        self._solution = {
            "sid": sid or self.sid,
        }
        self._context: Optional[CustomerProfilesContext] = None

    @property
    def _proxy(self) -> "CustomerProfilesContext":
        """
        Generate an instance context for the instance, the context is capable of
        performing various actions. All instance actions are proxied to the context

        :returns: CustomerProfilesContext for this CustomerProfilesInstance
        """
        if self._context is None:
            self._context = CustomerProfilesContext(
                self._version,
                sid=self._solution["sid"],
            )
        return self._context

    def delete(self) -> bool:
        """
        Deletes the CustomerProfilesInstance


        :returns: True if delete succeeds, False otherwise
        """
        return self._proxy.delete()

    async def delete_async(self) -> bool:
        """
        Asynchronous coroutine that deletes the CustomerProfilesInstance


        :returns: True if delete succeeds, False otherwise
        """
        return await self._proxy.delete_async()

    def fetch(self) -> "CustomerProfilesInstance":
        """
        Fetch the CustomerProfilesInstance


        :returns: The fetched CustomerProfilesInstance
        """
        return self._proxy.fetch()

    async def fetch_async(self) -> "CustomerProfilesInstance":
        """
        Asynchronous coroutine to fetch the CustomerProfilesInstance


        :returns: The fetched CustomerProfilesInstance
        """
        return await self._proxy.fetch_async()

    def update(
        self,
        status: Union["CustomerProfilesInstance.Status", object] = values.unset,
        status_callback: Union[str, object] = values.unset,
        friendly_name: Union[str, object] = values.unset,
        email: Union[str, object] = values.unset,
    ) -> "CustomerProfilesInstance":
        """
        Update the CustomerProfilesInstance

        :param status:
        :param status_callback: The URL we call to inform your application of status changes.
        :param friendly_name: The string that you assigned to describe the resource.
        :param email: The email address that will receive updates when the Customer-Profile resource changes status.

        :returns: The updated CustomerProfilesInstance
        """
        return self._proxy.update(
            status=status,
            status_callback=status_callback,
            friendly_name=friendly_name,
            email=email,
        )

    async def update_async(
        self,
        status: Union["CustomerProfilesInstance.Status", object] = values.unset,
        status_callback: Union[str, object] = values.unset,
        friendly_name: Union[str, object] = values.unset,
        email: Union[str, object] = values.unset,
    ) -> "CustomerProfilesInstance":
        """
        Asynchronous coroutine to update the CustomerProfilesInstance

        :param status:
        :param status_callback: The URL we call to inform your application of status changes.
        :param friendly_name: The string that you assigned to describe the resource.
        :param email: The email address that will receive updates when the Customer-Profile resource changes status.

        :returns: The updated CustomerProfilesInstance
        """
        return await self._proxy.update_async(
            status=status,
            status_callback=status_callback,
            friendly_name=friendly_name,
            email=email,
        )

    @property
    def customer_profiles_channel_endpoint_assignment(
        self,
    ) -> CustomerProfilesChannelEndpointAssignmentList:
        """
        Access the customer_profiles_channel_endpoint_assignment
        """
        return self._proxy.customer_profiles_channel_endpoint_assignment

    @property
    def customer_profiles_entity_assignments(
        self,
    ) -> CustomerProfilesEntityAssignmentsList:
        """
        Access the customer_profiles_entity_assignments
        """
        return self._proxy.customer_profiles_entity_assignments

    @property
    def customer_profiles_evaluations(self) -> CustomerProfilesEvaluationsList:
        """
        Access the customer_profiles_evaluations
        """
        return self._proxy.customer_profiles_evaluations

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Trusthub.V1.CustomerProfilesInstance {}>".format(context)


class CustomerProfilesContext(InstanceContext):
    def __init__(self, version: Version, sid: str):
        """
        Initialize the CustomerProfilesContext

        :param version: Version that contains the resource
        :param sid: The unique string that we created to identify the Customer-Profile resource.
        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "sid": sid,
        }
        self._uri = "/CustomerProfiles/{sid}".format(**self._solution)

        self._customer_profiles_channel_endpoint_assignment: Optional[
            CustomerProfilesChannelEndpointAssignmentList
        ] = None
        self._customer_profiles_entity_assignments: Optional[
            CustomerProfilesEntityAssignmentsList
        ] = None
        self._customer_profiles_evaluations: Optional[
            CustomerProfilesEvaluationsList
        ] = None

    def delete(self) -> bool:
        """
        Deletes the CustomerProfilesInstance


        :returns: True if delete succeeds, False otherwise
        """
        return self._version.delete(
            method="DELETE",
            uri=self._uri,
        )

    async def delete_async(self) -> bool:
        """
        Asynchronous coroutine that deletes the CustomerProfilesInstance


        :returns: True if delete succeeds, False otherwise
        """
        return await self._version.delete_async(
            method="DELETE",
            uri=self._uri,
        )

    def fetch(self) -> CustomerProfilesInstance:
        """
        Fetch the CustomerProfilesInstance


        :returns: The fetched CustomerProfilesInstance
        """

        payload = self._version.fetch(
            method="GET",
            uri=self._uri,
        )

        return CustomerProfilesInstance(
            self._version,
            payload,
            sid=self._solution["sid"],
        )

    async def fetch_async(self) -> CustomerProfilesInstance:
        """
        Asynchronous coroutine to fetch the CustomerProfilesInstance


        :returns: The fetched CustomerProfilesInstance
        """

        payload = await self._version.fetch_async(
            method="GET",
            uri=self._uri,
        )

        return CustomerProfilesInstance(
            self._version,
            payload,
            sid=self._solution["sid"],
        )

    def update(
        self,
        status: Union["CustomerProfilesInstance.Status", object] = values.unset,
        status_callback: Union[str, object] = values.unset,
        friendly_name: Union[str, object] = values.unset,
        email: Union[str, object] = values.unset,
    ) -> CustomerProfilesInstance:
        """
        Update the CustomerProfilesInstance

        :param status:
        :param status_callback: The URL we call to inform your application of status changes.
        :param friendly_name: The string that you assigned to describe the resource.
        :param email: The email address that will receive updates when the Customer-Profile resource changes status.

        :returns: The updated CustomerProfilesInstance
        """
        data = values.of(
            {
                "Status": status,
                "StatusCallback": status_callback,
                "FriendlyName": friendly_name,
                "Email": email,
            }
        )

        payload = self._version.update(
            method="POST",
            uri=self._uri,
            data=data,
        )

        return CustomerProfilesInstance(
            self._version, payload, sid=self._solution["sid"]
        )

    async def update_async(
        self,
        status: Union["CustomerProfilesInstance.Status", object] = values.unset,
        status_callback: Union[str, object] = values.unset,
        friendly_name: Union[str, object] = values.unset,
        email: Union[str, object] = values.unset,
    ) -> CustomerProfilesInstance:
        """
        Asynchronous coroutine to update the CustomerProfilesInstance

        :param status:
        :param status_callback: The URL we call to inform your application of status changes.
        :param friendly_name: The string that you assigned to describe the resource.
        :param email: The email address that will receive updates when the Customer-Profile resource changes status.

        :returns: The updated CustomerProfilesInstance
        """
        data = values.of(
            {
                "Status": status,
                "StatusCallback": status_callback,
                "FriendlyName": friendly_name,
                "Email": email,
            }
        )

        payload = await self._version.update_async(
            method="POST",
            uri=self._uri,
            data=data,
        )

        return CustomerProfilesInstance(
            self._version, payload, sid=self._solution["sid"]
        )

    @property
    def customer_profiles_channel_endpoint_assignment(
        self,
    ) -> CustomerProfilesChannelEndpointAssignmentList:
        """
        Access the customer_profiles_channel_endpoint_assignment
        """
        if self._customer_profiles_channel_endpoint_assignment is None:
            self._customer_profiles_channel_endpoint_assignment = (
                CustomerProfilesChannelEndpointAssignmentList(
                    self._version,
                    self._solution["sid"],
                )
            )
        return self._customer_profiles_channel_endpoint_assignment

    @property
    def customer_profiles_entity_assignments(
        self,
    ) -> CustomerProfilesEntityAssignmentsList:
        """
        Access the customer_profiles_entity_assignments
        """
        if self._customer_profiles_entity_assignments is None:
            self._customer_profiles_entity_assignments = (
                CustomerProfilesEntityAssignmentsList(
                    self._version,
                    self._solution["sid"],
                )
            )
        return self._customer_profiles_entity_assignments

    @property
    def customer_profiles_evaluations(self) -> CustomerProfilesEvaluationsList:
        """
        Access the customer_profiles_evaluations
        """
        if self._customer_profiles_evaluations is None:
            self._customer_profiles_evaluations = CustomerProfilesEvaluationsList(
                self._version,
                self._solution["sid"],
            )
        return self._customer_profiles_evaluations

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Trusthub.V1.CustomerProfilesContext {}>".format(context)


class CustomerProfilesPage(Page):
    def get_instance(self, payload: Dict[str, Any]) -> CustomerProfilesInstance:
        """
        Build an instance of CustomerProfilesInstance

        :param payload: Payload response from the API
        """
        return CustomerProfilesInstance(self._version, payload)

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Trusthub.V1.CustomerProfilesPage>"


class CustomerProfilesList(ListResource):
    def __init__(self, version: Version):
        """
        Initialize the CustomerProfilesList

        :param version: Version that contains the resource

        """
        super().__init__(version)

        self._uri = "/CustomerProfiles"

    def create(
        self,
        friendly_name: str,
        email: str,
        policy_sid: str,
        status_callback: Union[str, object] = values.unset,
    ) -> CustomerProfilesInstance:
        """
        Create the CustomerProfilesInstance

        :param friendly_name: The string that you assigned to describe the resource.
        :param email: The email address that will receive updates when the Customer-Profile resource changes status.
        :param policy_sid: The unique string of a policy that is associated to the Customer-Profile resource.
        :param status_callback: The URL we call to inform your application of status changes.

        :returns: The created CustomerProfilesInstance
        """
        data = values.of(
            {
                "FriendlyName": friendly_name,
                "Email": email,
                "PolicySid": policy_sid,
                "StatusCallback": status_callback,
            }
        )

        payload = self._version.create(
            method="POST",
            uri=self._uri,
            data=data,
        )

        return CustomerProfilesInstance(self._version, payload)

    async def create_async(
        self,
        friendly_name: str,
        email: str,
        policy_sid: str,
        status_callback: Union[str, object] = values.unset,
    ) -> CustomerProfilesInstance:
        """
        Asynchronously create the CustomerProfilesInstance

        :param friendly_name: The string that you assigned to describe the resource.
        :param email: The email address that will receive updates when the Customer-Profile resource changes status.
        :param policy_sid: The unique string of a policy that is associated to the Customer-Profile resource.
        :param status_callback: The URL we call to inform your application of status changes.

        :returns: The created CustomerProfilesInstance
        """
        data = values.of(
            {
                "FriendlyName": friendly_name,
                "Email": email,
                "PolicySid": policy_sid,
                "StatusCallback": status_callback,
            }
        )

        payload = await self._version.create_async(
            method="POST",
            uri=self._uri,
            data=data,
        )

        return CustomerProfilesInstance(self._version, payload)

    def stream(
        self,
        status: Union["CustomerProfilesInstance.Status", object] = values.unset,
        friendly_name: Union[str, object] = values.unset,
        policy_sid: Union[str, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> Iterator[CustomerProfilesInstance]:
        """
        Streams CustomerProfilesInstance records from the API as a generator stream.
        This operation lazily loads records as efficiently as possible until the limit
        is reached.
        The results are returned as a generator, so this operation is memory efficient.

        :param &quot;CustomerProfilesInstance.Status&quot; status: The verification status of the Customer-Profile resource.
        :param str friendly_name: The string that you assigned to describe the resource.
        :param str policy_sid: The unique string of a policy that is associated to the Customer-Profile resource.
        :param limit: Upper limit for the number of records to return. stream()
                      guarantees to never return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, stream() will attempt to read the
                          limit with the most efficient page size, i.e. min(limit, 1000)

        :returns: Generator that will yield up to limit results
        """
        limits = self._version.read_limits(limit, page_size)
        page = self.page(
            status=status,
            friendly_name=friendly_name,
            policy_sid=policy_sid,
            page_size=limits["page_size"],
        )

        return self._version.stream(page, limits["limit"])

    async def stream_async(
        self,
        status: Union["CustomerProfilesInstance.Status", object] = values.unset,
        friendly_name: Union[str, object] = values.unset,
        policy_sid: Union[str, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> AsyncIterator[CustomerProfilesInstance]:
        """
        Asynchronously streams CustomerProfilesInstance records from the API as a generator stream.
        This operation lazily loads records as efficiently as possible until the limit
        is reached.
        The results are returned as a generator, so this operation is memory efficient.

        :param &quot;CustomerProfilesInstance.Status&quot; status: The verification status of the Customer-Profile resource.
        :param str friendly_name: The string that you assigned to describe the resource.
        :param str policy_sid: The unique string of a policy that is associated to the Customer-Profile resource.
        :param limit: Upper limit for the number of records to return. stream()
                      guarantees to never return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, stream() will attempt to read the
                          limit with the most efficient page size, i.e. min(limit, 1000)

        :returns: Generator that will yield up to limit results
        """
        limits = self._version.read_limits(limit, page_size)
        page = await self.page_async(
            status=status,
            friendly_name=friendly_name,
            policy_sid=policy_sid,
            page_size=limits["page_size"],
        )

        return self._version.stream_async(page, limits["limit"])

    def list(
        self,
        status: Union["CustomerProfilesInstance.Status", object] = values.unset,
        friendly_name: Union[str, object] = values.unset,
        policy_sid: Union[str, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> List[CustomerProfilesInstance]:
        """
        Lists CustomerProfilesInstance records from the API as a list.
        Unlike stream(), this operation is eager and will load `limit` records into
        memory before returning.

        :param &quot;CustomerProfilesInstance.Status&quot; status: The verification status of the Customer-Profile resource.
        :param str friendly_name: The string that you assigned to describe the resource.
        :param str policy_sid: The unique string of a policy that is associated to the Customer-Profile resource.
        :param limit: Upper limit for the number of records to return. list() guarantees
                      never to return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, list() will attempt to read the limit
                          with the most efficient page size, i.e. min(limit, 1000)

        :returns: list that will contain up to limit results
        """
        return list(
            self.stream(
                status=status,
                friendly_name=friendly_name,
                policy_sid=policy_sid,
                limit=limit,
                page_size=page_size,
            )
        )

    async def list_async(
        self,
        status: Union["CustomerProfilesInstance.Status", object] = values.unset,
        friendly_name: Union[str, object] = values.unset,
        policy_sid: Union[str, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> List[CustomerProfilesInstance]:
        """
        Asynchronously lists CustomerProfilesInstance records from the API as a list.
        Unlike stream(), this operation is eager and will load `limit` records into
        memory before returning.

        :param &quot;CustomerProfilesInstance.Status&quot; status: The verification status of the Customer-Profile resource.
        :param str friendly_name: The string that you assigned to describe the resource.
        :param str policy_sid: The unique string of a policy that is associated to the Customer-Profile resource.
        :param limit: Upper limit for the number of records to return. list() guarantees
                      never to return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, list() will attempt to read the limit
                          with the most efficient page size, i.e. min(limit, 1000)

        :returns: list that will contain up to limit results
        """
        return [
            record
            async for record in await self.stream_async(
                status=status,
                friendly_name=friendly_name,
                policy_sid=policy_sid,
                limit=limit,
                page_size=page_size,
            )
        ]

    def page(
        self,
        status: Union["CustomerProfilesInstance.Status", object] = values.unset,
        friendly_name: Union[str, object] = values.unset,
        policy_sid: Union[str, object] = values.unset,
        page_token: Union[str, object] = values.unset,
        page_number: Union[int, object] = values.unset,
        page_size: Union[int, object] = values.unset,
    ) -> CustomerProfilesPage:
        """
        Retrieve a single page of CustomerProfilesInstance records from the API.
        Request is executed immediately

        :param status: The verification status of the Customer-Profile resource.
        :param friendly_name: The string that you assigned to describe the resource.
        :param policy_sid: The unique string of a policy that is associated to the Customer-Profile resource.
        :param page_token: PageToken provided by the API
        :param page_number: Page Number, this value is simply for client state
        :param page_size: Number of records to return, defaults to 50

        :returns: Page of CustomerProfilesInstance
        """
        data = values.of(
            {
                "Status": status,
                "FriendlyName": friendly_name,
                "PolicySid": policy_sid,
                "PageToken": page_token,
                "Page": page_number,
                "PageSize": page_size,
            }
        )

        response = self._version.page(method="GET", uri=self._uri, params=data)
        return CustomerProfilesPage(self._version, response)

    async def page_async(
        self,
        status: Union["CustomerProfilesInstance.Status", object] = values.unset,
        friendly_name: Union[str, object] = values.unset,
        policy_sid: Union[str, object] = values.unset,
        page_token: Union[str, object] = values.unset,
        page_number: Union[int, object] = values.unset,
        page_size: Union[int, object] = values.unset,
    ) -> CustomerProfilesPage:
        """
        Asynchronously retrieve a single page of CustomerProfilesInstance records from the API.
        Request is executed immediately

        :param status: The verification status of the Customer-Profile resource.
        :param friendly_name: The string that you assigned to describe the resource.
        :param policy_sid: The unique string of a policy that is associated to the Customer-Profile resource.
        :param page_token: PageToken provided by the API
        :param page_number: Page Number, this value is simply for client state
        :param page_size: Number of records to return, defaults to 50

        :returns: Page of CustomerProfilesInstance
        """
        data = values.of(
            {
                "Status": status,
                "FriendlyName": friendly_name,
                "PolicySid": policy_sid,
                "PageToken": page_token,
                "Page": page_number,
                "PageSize": page_size,
            }
        )

        response = await self._version.page_async(
            method="GET", uri=self._uri, params=data
        )
        return CustomerProfilesPage(self._version, response)

    def get_page(self, target_url: str) -> CustomerProfilesPage:
        """
        Retrieve a specific page of CustomerProfilesInstance records from the API.
        Request is executed immediately

        :param target_url: API-generated URL for the requested results page

        :returns: Page of CustomerProfilesInstance
        """
        response = self._version.domain.twilio.request("GET", target_url)
        return CustomerProfilesPage(self._version, response)

    async def get_page_async(self, target_url: str) -> CustomerProfilesPage:
        """
        Asynchronously retrieve a specific page of CustomerProfilesInstance records from the API.
        Request is executed immediately

        :param target_url: API-generated URL for the requested results page

        :returns: Page of CustomerProfilesInstance
        """
        response = await self._version.domain.twilio.request_async("GET", target_url)
        return CustomerProfilesPage(self._version, response)

    def get(self, sid: str) -> CustomerProfilesContext:
        """
        Constructs a CustomerProfilesContext

        :param sid: The unique string that we created to identify the Customer-Profile resource.
        """
        return CustomerProfilesContext(self._version, sid=sid)

    def __call__(self, sid: str) -> CustomerProfilesContext:
        """
        Constructs a CustomerProfilesContext

        :param sid: The unique string that we created to identify the Customer-Profile resource.
        """
        return CustomerProfilesContext(self._version, sid=sid)

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Trusthub.V1.CustomerProfilesList>"
