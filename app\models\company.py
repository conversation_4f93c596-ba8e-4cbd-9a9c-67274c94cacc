#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""Şirket modeli"""

from datetime import datetime
from app import db

class Company(db.Model):
    """Şirket modeli"""
    __tablename__ = 'companies'
    
    id = db.Column(db.Integer, primary_key=True)
    ad = db.Column(db.String(200), nullable=False)
    kod = db.Column(db.String(10), unique=True, nullable=False)  # Şirket kodu (örn: BYK, TAI)
    aciklama = db.Column(db.Text)
    adres = db.Column(db.Text)
    telefon = db.Column(db.String(20))
    email = db.Column(db.String(120))
    website = db.Column(db.String(200))
    vergi_no = db.Column(db.String(20))
    vergi_dairesi = db.Column(db.String(100))
    
    # Durum bilgileri
    aktif_mi = db.Column(db.<PERSON>, default=True, nullable=False)
    olusturulma_tarihi = db.Column(db.DateTime, default=datetime.utcnow)
    guncelleme_tarihi = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Oluşturan kullanıcı
    olusturan_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    olusturan = db.relationship('User', foreign_keys=[olusturan_id], backref='olusturulan_sirketler')

    # İlişkiler
    users = db.relationship('User', foreign_keys='User.company_id', backref='company', lazy='dynamic')
    trainings = db.relationship('Training', foreign_keys='Training.company_id', backref='company', lazy='dynamic')
    
    def __init__(self, **kwargs):
        super(Company, self).__init__(**kwargs)
    
    @property
    def tam_ad(self):
        """Kod ve ad birlikte"""
        return f"{self.kod} - {self.ad}"
    
    @property
    def kullanici_sayisi(self):
        """Şirketteki toplam kullanıcı sayısı"""
        return self.users.filter_by(aktif_mi=True).count()
    
    @property
    def aktif_kullanici_sayisi(self):
        """Şirketteki aktif kullanıcı sayısı"""
        return self.users.filter_by(aktif_mi=True).count()
    
    @property
    def egitim_sayisi(self):
        """Şirketteki toplam eğitim sayısı"""
        return self.trainings.filter_by(aktif_mi=True).count()
    
    @property
    def manager_sayisi(self):
        """Şirketteki manager sayısı"""
        return self.users.filter_by(rol='manager', aktif_mi=True).count()
    
    @property
    def participant_sayisi(self):
        """Şirketteki katılımcı sayısı"""
        return self.users.filter_by(rol='participant', aktif_mi=True).count()
    
    def get_managers(self):
        """Şirketteki manager'ları getir"""
        return self.users.filter_by(rol='manager', aktif_mi=True).all()
    
    def get_participants(self):
        """Şirketteki katılımcıları getir"""
        return self.users.filter_by(rol='participant', aktif_mi=True).all()
    
    def get_all_users(self):
        """Şirketteki tüm kullanıcıları getir"""
        return self.users.filter_by(aktif_mi=True).all()
    
    def can_be_deleted(self):
        """Şirket silinebilir mi? (kullanıcısı yoksa silinebilir)"""
        return self.users.count() == 0
    
    def __repr__(self):
        return f'<Company {self.kod} - {self.ad}>'
    
    def to_dict(self):
        """Şirket bilgilerini dictionary olarak döndür"""
        return {
            'id': self.id,
            'ad': self.ad,
            'kod': self.kod,
            'aciklama': self.aciklama,
            'adres': self.adres,
            'telefon': self.telefon,
            'email': self.email,
            'website': self.website,
            'vergi_no': self.vergi_no,
            'vergi_dairesi': self.vergi_dairesi,
            'aktif_mi': self.aktif_mi,
            'tam_ad': self.tam_ad,
            'kullanici_sayisi': self.kullanici_sayisi,
            'aktif_kullanici_sayisi': self.aktif_kullanici_sayisi,
            'egitim_sayisi': self.egitim_sayisi,
            'manager_sayisi': self.manager_sayisi,
            'participant_sayisi': self.participant_sayisi,
            'olusturulma_tarihi': self.olusturulma_tarihi.isoformat() if self.olusturulma_tarihi else None,
            'guncelleme_tarihi': self.guncelleme_tarihi.isoformat() if self.guncelleme_tarihi else None,
            'olusturan_ad': self.olusturan.tam_ad if self.olusturan else None,
            'can_be_deleted': self.can_be_deleted()
        }
    
    @classmethod
    def get_active_companies(cls):
        """Aktif şirketleri getir"""
        return cls.query.filter_by(aktif_mi=True).order_by(cls.kod).all()
    
    @classmethod
    def get_company_by_code(cls, kod):
        """Koda göre şirket getir"""
        return cls.query.filter_by(kod=kod, aktif_mi=True).first()
    
    @classmethod
    def get_companies_with_stats(cls):
        """İstatistiklerle birlikte şirketleri getir"""
        companies = cls.query.order_by(cls.kod).all()
        return [company.to_dict() for company in companies]
