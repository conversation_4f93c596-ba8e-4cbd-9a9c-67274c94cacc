{% extends "base.html" %}

{% block title %}<PERSON><PERSON><PERSON> - {{ current_user.tam_ad }}{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-key"></i> <PERSON><PERSON><PERSON>
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{{ url_for('auth.profile') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left"></i> Profil
        </a>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-lg-6">
        <div class="card shadow">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-shield-alt"></i> <PERSON><PERSON><PERSON>
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" id="changePasswordForm">
                    <div class="mb-3">
                        <label for="current_password" class="form-label">Mevcut Şifre *</label>
                        <div class="input-group">
                            <input type="password" class="form-control" id="current_password" 
                                   name="current_password" required>
                            <button class="btn btn-outline-secondary" type="button" 
                                    onclick="togglePassword('current_password')">
                                <i class="fas fa-eye" id="current_password_icon"></i>
                            </button>
                        </div>
                        <div class="form-text">Güvenlik için mevcut şifrenizi girin</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="new_password" class="form-label">Yeni Şifre *</label>
                        <div class="input-group">
                            <input type="password" class="form-control" id="new_password" 
                                   name="new_password" minlength="6" required>
                            <button class="btn btn-outline-secondary" type="button" 
                                    onclick="togglePassword('new_password')">
                                <i class="fas fa-eye" id="new_password_icon"></i>
                            </button>
                        </div>
                        <div class="form-text">En az 6 karakter olmalıdır</div>
                        
                        <!-- Şifre Güçlülük Göstergesi -->
                        <div class="mt-2">
                            <div class="progress" style="height: 5px;">
                                <div class="progress-bar" id="passwordStrength" role="progressbar" 
                                     style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
                            </div>
                            <small id="passwordStrengthText" class="text-muted">Şifre gücü</small>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="confirm_password" class="form-label">Yeni Şifre Tekrar *</label>
                        <div class="input-group">
                            <input type="password" class="form-control" id="confirm_password" 
                                   name="confirm_password" minlength="6" required>
                            <button class="btn btn-outline-secondary" type="button" 
                                    onclick="togglePassword('confirm_password')">
                                <i class="fas fa-eye" id="confirm_password_icon"></i>
                            </button>
                        </div>
                        <div class="form-text">Yeni şifrenizi tekrar girin</div>
                    </div>
                    
                    <!-- Şifre Gereksinimleri -->
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle"></i> Şifre Gereksinimleri:</h6>
                        <ul class="mb-0 small">
                            <li id="length-req" class="text-muted">
                                <i class="fas fa-times"></i> En az 6 karakter
                            </li>
                            <li id="uppercase-req" class="text-muted">
                                <i class="fas fa-times"></i> En az 1 büyük harf (önerilir)
                            </li>
                            <li id="lowercase-req" class="text-muted">
                                <i class="fas fa-times"></i> En az 1 küçük harf (önerilir)
                            </li>
                            <li id="number-req" class="text-muted">
                                <i class="fas fa-times"></i> En az 1 sayı (önerilir)
                            </li>
                            <li id="special-req" class="text-muted">
                                <i class="fas fa-times"></i> En az 1 özel karakter (önerilir)
                            </li>
                        </ul>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('auth.profile') }}" class="btn btn-secondary">
                            <i class="fas fa-times"></i> İptal
                        </a>
                        <button type="submit" class="btn btn-primary" id="submitBtn" disabled>
                            <i class="fas fa-save"></i> Şifreyi Değiştir
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-6">
        <div class="card shadow">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-lightbulb"></i> Güvenlik İpuçları</h6>
            </div>
            <div class="card-body">
                <h6>Güçlü Şifre Oluşturma:</h6>
                <ul class="small">
                    <li>En az 8-12 karakter kullanın</li>
                    <li>Büyük ve küçük harfleri karıştırın</li>
                    <li>Sayılar ve özel karakterler ekleyin</li>
                    <li>Kişisel bilgilerinizi kullanmayın</li>
                    <li>Sözlükte bulunan kelimeleri kullanmayın</li>
                </ul>
                
                <hr>
                
                <h6>Şifre Güvenliği:</h6>
                <ul class="small">
                    <li>Şifrenizi kimseyle paylaşmayın</li>
                    <li>Düzenli olarak değiştirin</li>
                    <li>Farklı hesaplar için farklı şifreler kullanın</li>
                    <li>Şifre yöneticisi kullanmayı düşünün</li>
                </ul>
                
                <hr>
                
                <h6>Şifre Örnekleri:</h6>
                <div class="small">
                    <div class="mb-2">
                        <span class="badge bg-danger">Zayıf:</span>
                        <code>123456, password, qwerty</code>
                    </div>
                    <div class="mb-2">
                        <span class="badge bg-warning">Orta:</span>
                        <code>Password123, Ankara2023</code>
                    </div>
                    <div class="mb-2">
                        <span class="badge bg-success">Güçlü:</span>
                        <code>M3rh@ba2023!, K0d$uN_G3l1$t1r</code>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card shadow mt-4">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-history"></i> Son Değişiklikler</h6>
            </div>
            <div class="card-body">
                <div class="small">
                    <p><strong>Son Şifre Değişikliği:</strong></p>
                    <p class="text-muted">
                        {% if current_user.son_giris_tarihi %}
                        Bilgi mevcut değil
                        {% else %}
                        Hiç değiştirilmemiş
                        {% endif %}
                    </p>
                    
                    <p class="mt-3"><strong>Güvenlik Önerileri:</strong></p>
                    <ul>
                        <li>Şifrenizi 3-6 ayda bir değiştirin</li>
                        <li>Şüpheli aktivite fark ederseniz hemen değiştirin</li>
                        <li>Başka cihazlardan giriş yaptıysanız çıkış yapmayı unutmayın</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Form validasyonu
    $('#changePasswordForm').on('submit', function(e) {
        if (!validateForm()) {
            e.preventDefault();
        }
    });
    
    // Şifre gücü kontrolü
    $('#new_password').on('input', function() {
        checkPasswordStrength();
        checkPasswordMatch();
        updateSubmitButton();
    });
    
    // Şifre eşleşme kontrolü
    $('#confirm_password').on('input', function() {
        checkPasswordMatch();
        updateSubmitButton();
    });
    
    // Mevcut şifre kontrolü
    $('#current_password').on('input', function() {
        updateSubmitButton();
    });
});

function togglePassword(fieldId) {
    let field = document.getElementById(fieldId);
    let icon = document.getElementById(fieldId + '_icon');
    
    if (field.type === 'password') {
        field.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        field.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
}

function checkPasswordStrength() {
    let password = $('#new_password').val();
    let strength = 0;
    let strengthText = '';
    let strengthClass = '';
    
    // Uzunluk kontrolü
    if (password.length >= 6) {
        strength += 20;
        updateRequirement('length-req', true);
    } else {
        updateRequirement('length-req', false);
    }
    
    // Büyük harf kontrolü
    if (/[A-Z]/.test(password)) {
        strength += 20;
        updateRequirement('uppercase-req', true);
    } else {
        updateRequirement('uppercase-req', false);
    }
    
    // Küçük harf kontrolü
    if (/[a-z]/.test(password)) {
        strength += 20;
        updateRequirement('lowercase-req', true);
    } else {
        updateRequirement('lowercase-req', false);
    }
    
    // Sayı kontrolü
    if (/[0-9]/.test(password)) {
        strength += 20;
        updateRequirement('number-req', true);
    } else {
        updateRequirement('number-req', false);
    }
    
    // Özel karakter kontrolü
    if (/[^A-Za-z0-9]/.test(password)) {
        strength += 20;
        updateRequirement('special-req', true);
    } else {
        updateRequirement('special-req', false);
    }
    
    // Güç seviyesi belirleme
    if (strength < 40) {
        strengthText = 'Çok Zayıf';
        strengthClass = 'bg-danger';
    } else if (strength < 60) {
        strengthText = 'Zayıf';
        strengthClass = 'bg-warning';
    } else if (strength < 80) {
        strengthText = 'Orta';
        strengthClass = 'bg-info';
    } else {
        strengthText = 'Güçlü';
        strengthClass = 'bg-success';
    }
    
    $('#passwordStrength').css('width', strength + '%')
                          .removeClass('bg-danger bg-warning bg-info bg-success')
                          .addClass(strengthClass);
    $('#passwordStrengthText').text(strengthText);
}

function updateRequirement(reqId, met) {
    let element = $('#' + reqId);
    if (met) {
        element.removeClass('text-muted').addClass('text-success');
        element.find('i').removeClass('fa-times').addClass('fa-check');
    } else {
        element.removeClass('text-success').addClass('text-muted');
        element.find('i').removeClass('fa-check').addClass('fa-times');
    }
}

function checkPasswordMatch() {
    let newPassword = $('#new_password').val();
    let confirmPassword = $('#confirm_password').val();
    
    if (confirmPassword && newPassword !== confirmPassword) {
        $('#confirm_password').addClass('is-invalid').removeClass('is-valid');
    } else if (confirmPassword) {
        $('#confirm_password').removeClass('is-invalid').addClass('is-valid');
    } else {
        $('#confirm_password').removeClass('is-invalid is-valid');
    }
}

function updateSubmitButton() {
    let currentPassword = $('#current_password').val();
    let newPassword = $('#new_password').val();
    let confirmPassword = $('#confirm_password').val();
    
    let isValid = currentPassword.length > 0 && 
                  newPassword.length >= 6 && 
                  confirmPassword.length >= 6 && 
                  newPassword === confirmPassword;
    
    $('#submitBtn').prop('disabled', !isValid);
}

function validateForm() {
    let currentPassword = $('#current_password').val();
    let newPassword = $('#new_password').val();
    let confirmPassword = $('#confirm_password').val();
    
    if (!currentPassword) {
        alert('Mevcut şifrenizi girin!');
        return false;
    }
    
    if (newPassword.length < 6) {
        alert('Yeni şifre en az 6 karakter olmalıdır!');
        return false;
    }
    
    if (newPassword !== confirmPassword) {
        alert('Yeni şifreler eşleşmiyor!');
        return false;
    }
    
    if (currentPassword === newPassword) {
        alert('Yeni şifre mevcut şifreden farklı olmalıdır!');
        return false;
    }
    
    return true;
}

// Caps Lock uyarısı
$(document).on('keypress', 'input[type="password"]', function(e) {
    let capsLock = e.originalEvent.getModifierState && e.originalEvent.getModifierState('CapsLock');
    if (capsLock) {
        if (!$('#capsLockWarning').length) {
            $(this).after('<div id="capsLockWarning" class="alert alert-warning alert-sm mt-1"><i class="fas fa-exclamation-triangle"></i> Caps Lock açık!</div>');
        }
    } else {
        $('#capsLockWarning').remove();
    }
});
</script>
{% endblock %}
