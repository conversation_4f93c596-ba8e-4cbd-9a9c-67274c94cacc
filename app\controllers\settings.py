from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from app import db
from app.models.settings import SystemSettings, SettingsCategory
from app.decorators import admin_required
import json

settings_bp = Blueprint('settings', __name__, url_prefix='/settings')


@settings_bp.route('/')
@login_required
@admin_required
def index():
    """Ayarlar ana sayfası"""
    # Ayar tablosu var mı kontrol et
    try:
        total_settings = SystemSettings.query.count()
        if total_settings == 0:
            # Varsayılan ayarları oluştur
            SystemSettings.initialize_default_settings()
            flash('Varsayılan sistem ayarları oluşturuldu.', 'success')
    except Exception as e:
        # Tablo yoksa oluştur
        db.create_all()
        SystemSettings.initialize_default_settings()
        flash('Sistem ayarları tablosu oluşturuldu ve varsayılan değerler yüklendi.', 'success')

    categories = SettingsCategory.CATEGORIES
    return render_template('settings/index.html', categories=categories)


@settings_bp.route('/category/<category>')
@login_required
@admin_required
def category_settings(category):
    """Kategori ayarları"""
    if category not in SettingsCategory.CATEGORIES:
        flash('Geçersiz kategori.', 'error')
        return redirect(url_for('settings.index'))

    category_info = SettingsCategory.get_category_info(category)
    settings = SystemSettings.get_category_settings(category)

    # Eğer ayar yoksa varsayılan ayarları oluştur
    if not settings:
        SystemSettings.initialize_default_settings()
        settings = SystemSettings.get_category_settings(category)
        if settings:
            flash(f'{category_info["name"]} için varsayılan ayarlar oluşturuldu.', 'success')

    return render_template('settings/category.html',
                         category=category,
                         category_info=category_info,
                         settings=settings)


@settings_bp.route('/category/<category>/save', methods=['POST'])
@login_required
@admin_required
def save_category_settings(category):
    """Kategori ayarlarını kaydet"""
    if category not in SettingsCategory.CATEGORIES:
        return jsonify({'success': False, 'message': 'Geçersiz kategori'}), 400
    
    try:
        settings = SystemSettings.get_category_settings(category)
        updated_count = 0
        
        for setting in settings:
            form_key = f'setting_{setting.key}'
            if form_key in request.form:
                new_value = request.form[form_key]
                
                # Boolean değerler için özel işlem
                if setting.data_type == 'boolean':
                    new_value = form_key in request.form
                
                # Değer değiştiyse güncelle
                if str(setting.parsed_value) != str(new_value):
                    setting.set_value(new_value)
                    setting.updated_by = current_user.id
                    updated_count += 1
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': f'{updated_count} ayar başarıyla güncellendi.',
            'updated_count': updated_count
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'Ayarlar kaydedilirken hata oluştu: {str(e)}'
        }), 500


@settings_bp.route('/test-sms', methods=['POST'])
@login_required
@admin_required
def test_sms():
    """SMS ayarlarını test et"""
    try:
        phone = request.json.get('phone')
        if not phone:
            return jsonify({'success': False, 'message': 'Telefon numarası gerekli'}), 400
        
        # SMS servisini import et
        from app.services.notification_service import NotificationService
        notification_service = NotificationService()
        
        # Test mesajı gönder
        message = f"Test mesajı - {SystemSettings.get_setting('system_name', 'Eğitim Sistemi')}"
        success = notification_service.send_sms(phone, message)
        
        if success:
            return jsonify({
                'success': True,
                'message': f'Test SMS başarıyla gönderildi: {phone}'
            })
        else:
            return jsonify({
                'success': False,
                'message': 'SMS gönderilemedi. Ayarları kontrol edin.'
            })
            
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'SMS testi sırasında hata: {str(e)}'
        }), 500


@settings_bp.route('/test-email', methods=['POST'])
@login_required
@admin_required
def test_email():
    """Email ayarlarını test et"""
    try:
        email = request.json.get('email')
        if not email:
            return jsonify({'success': False, 'message': 'Email adresi gerekli'}), 400
        
        # Email servisini import et
        from app.services.notification_service import NotificationService
        notification_service = NotificationService()
        
        # Test emaili gönder
        subject = f"Test Email - {SystemSettings.get_setting('system_name', 'Eğitim Sistemi')}"
        body = "Bu bir test emailidir. Email ayarlarınız doğru çalışıyor."
        
        success = notification_service.send_email(email, subject, body)
        
        if success:
            return jsonify({
                'success': True,
                'message': f'Test email başarıyla gönderildi: {email}'
            })
        else:
            return jsonify({
                'success': False,
                'message': 'Email gönderilemedi. Ayarları kontrol edin.'
            })
            
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Email testi sırasında hata: {str(e)}'
        }), 500


@settings_bp.route('/backup', methods=['POST'])
@login_required
@admin_required
def backup_settings():
    """Ayarları yedekle"""
    try:
        all_settings = SystemSettings.query.all()
        backup_data = {
            'timestamp': datetime.utcnow().isoformat(),
            'settings': {}
        }
        
        for setting in all_settings:
            backup_data['settings'][setting.key] = {
                'value': setting.value,
                'description': setting.description,
                'category': setting.category,
                'data_type': setting.data_type
            }
        
        return jsonify({
            'success': True,
            'data': backup_data,
            'message': f'{len(all_settings)} ayar yedeklendi.'
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Yedekleme sırasında hata: {str(e)}'
        }), 500


@settings_bp.route('/restore', methods=['POST'])
@login_required
@admin_required
def restore_settings():
    """Ayarları geri yükle"""
    try:
        backup_data = request.json.get('backup_data')
        if not backup_data or 'settings' not in backup_data:
            return jsonify({'success': False, 'message': 'Geçersiz yedek dosyası'}), 400
        
        restored_count = 0
        
        for key, data in backup_data['settings'].items():
            success = SystemSettings.set_setting(
                key=key,
                value=data['value'],
                description=data.get('description'),
                category=data.get('category', 'general'),
                data_type=data.get('data_type', 'string'),
                user_id=current_user.id
            )
            if success:
                restored_count += 1
        
        return jsonify({
            'success': True,
            'message': f'{restored_count} ayar başarıyla geri yüklendi.',
            'restored_count': restored_count
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Geri yükleme sırasında hata: {str(e)}'
        }), 500


@settings_bp.route('/reset-category/<category>', methods=['POST'])
@login_required
@admin_required
def reset_category(category):
    """Kategori ayarlarını sıfırla"""
    if category not in SettingsCategory.CATEGORIES:
        return jsonify({'success': False, 'message': 'Geçersiz kategori'}), 400
    
    try:
        # Kategori ayarlarını sil
        SystemSettings.query.filter_by(category=category).delete()
        db.session.commit()
        
        # Varsayılan ayarları yeniden oluştur
        SystemSettings.initialize_default_settings()
        
        return jsonify({
            'success': True,
            'message': f'{SettingsCategory.get_category_info(category)["name"]} varsayılan değerlere sıfırlandı.'
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'Sıfırlama sırasında hata: {str(e)}'
        }), 500


@settings_bp.route('/system-info')
@login_required
@admin_required
def system_info():
    """Sistem bilgileri"""
    import platform
    import os
    from datetime import datetime

    try:
        # Temel sistem bilgileri
        system_info = {
            'platform': platform.platform(),
            'python_version': platform.python_version(),
            'architecture': platform.architecture()[0],
            'processor': platform.processor() or 'Bilinmiyor',
            'system': platform.system(),
            'release': platform.release(),
        }

        # psutil varsa ek bilgiler
        try:
            import psutil
            system_info.update({
                'cpu_count': psutil.cpu_count(),
                'memory_total': round(psutil.virtual_memory().total / (1024**3), 2),
                'memory_available': round(psutil.virtual_memory().available / (1024**3), 2),
                'memory_percent': round(psutil.virtual_memory().percent, 1),
                'disk_usage': round(psutil.disk_usage('/').percent, 2) if os.name != 'nt' else round(psutil.disk_usage('C:').percent, 2),
                'uptime': str(datetime.now() - datetime.fromtimestamp(psutil.boot_time())).split('.')[0]
            })
        except ImportError:
            system_info.update({
                'cpu_count': 'psutil gerekli',
                'memory_total': 'psutil gerekli',
                'memory_available': 'psutil gerekli',
                'memory_percent': 'psutil gerekli',
                'disk_usage': 'psutil gerekli',
                'uptime': 'psutil gerekli'
            })

        # Veritabanı bilgileri
        try:
            db_info = {
                'users_count': db.session.execute('SELECT COUNT(*) FROM users').scalar(),
                'trainings_count': db.session.execute('SELECT COUNT(*) FROM trainings').scalar(),
                'materials_count': db.session.execute('SELECT COUNT(*) FROM training_materials').scalar(),
                'companies_count': db.session.execute('SELECT COUNT(*) FROM companies').scalar(),
                'settings_count': db.session.execute('SELECT COUNT(*) FROM system_settings').scalar(),
            }
        except Exception as e:
            db_info = {
                'error': f'Veritabanı bilgileri alınamadı: {str(e)}'
            }

        # Flask bilgileri
        flask_info = {
            'debug_mode': current_app.debug,
            'testing': current_app.testing,
            'secret_key_set': bool(current_app.secret_key),
            'database_uri': current_app.config.get('SQLALCHEMY_DATABASE_URI', 'Ayarlanmamış')[:50] + '...' if len(current_app.config.get('SQLALCHEMY_DATABASE_URI', '')) > 50 else current_app.config.get('SQLALCHEMY_DATABASE_URI', 'Ayarlanmamış')
        }

        return render_template('settings/system_info.html',
                             system_info=system_info,
                             db_info=db_info,
                             flask_info=flask_info)

    except Exception as e:
        flash(f'Sistem bilgileri alınırken hata: {str(e)}', 'error')
        return redirect(url_for('settings.index'))


@settings_bp.route('/logs')
@login_required
@admin_required
def view_logs():
    """Sistem loglarını görüntüle"""
    try:
        import os
        from datetime import datetime

        log_files = []
        log_dir = 'logs'

        if os.path.exists(log_dir):
            for file in os.listdir(log_dir):
                if file.endswith('.log'):
                    file_path = os.path.join(log_dir, file)
                    file_size = os.path.getsize(file_path)
                    file_modified = datetime.fromtimestamp(os.path.getmtime(file_path))

                    log_files.append({
                        'name': file,
                        'size': round(file_size / 1024, 2),  # KB
                        'modified': file_modified
                    })

        return render_template('settings/logs.html', log_files=log_files)

    except Exception as e:
        flash(f'Log dosyaları listelenirken hata: {str(e)}', 'error')
        return redirect(url_for('settings.index'))
