{% extends "base.html" %}

{% block title %}Katılımcı Yönetimi - {{ training.ad }}{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-users"></i> Katılımcı Yönetimi
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{{ url_for('training.detail', training_id=training.id) }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left"></i> Eğitime Dön
        </a>
    </div>
</div>

<!-- Eğitim Bilgisi -->
<div class="alert alert-info">
    <h5><i class="fas fa-book"></i> {{ training.ad }}</h5>
    <p class="mb-0">
        <i class="fas fa-calendar-alt"></i> 
        {{ training.baslangic_tarihi.strftime('%d.%m.%Y %H:%M') }} - 
        {{ training.bitis_tarihi.strftime('%d.%m.%Y %H:%M') }}
    </p>
</div>

<div class="row">
    <!-- Mevcut Katılımcılar -->
    <div class="col-lg-8">
        <div class="card shadow">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-users"></i> Mevcut Katılımcılar ({{ current_participants|length }})
                </h5>
            </div>
            <div class="card-body">
                {% if current_participants %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Ad Soyad</th>
                                <th>E-posta</th>
                                <th>Telefon</th>
                                <th>Rol</th>
                                <th>Durum</th>
                                <th>İşlemler</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for participant in current_participants %}
                            <tr id="participant-{{ participant.id }}">
                                <td>
                                    <strong>{{ participant.tam_ad }}</strong>
                                </td>
                                <td>{{ participant.email }}</td>
                                <td>{{ participant.telefon_numarasi }}</td>
                                <td>
                                    <span class="badge 
                                        {% if participant.rol == 'admin' %}bg-danger
                                        {% elif participant.rol == 'manager' %}bg-warning
                                        {% else %}bg-info{% endif %}">
                                        {{ participant.rol.title() }}
                                    </span>
                                </td>
                                <td>
                                    {% set enrollment = training.enrollments.filter_by(user_id=participant.id, aktif_mi=True).first() %}
                                    {% if enrollment %}
                                    <span class="badge 
                                        {% if enrollment.katilim_teyidi_durumu == 'teyit_edildi' %}bg-success
                                        {% elif enrollment.katilim_teyidi_durumu == 'reddedildi' %}bg-danger
                                        {% else %}bg-warning{% endif %}">
                                        {% if enrollment.katilim_teyidi_durumu == 'teyit_edildi' %}Teyit Edildi
                                        {% elif enrollment.katilim_teyidi_durumu == 'reddedildi' %}Reddedildi
                                        {% else %}Beklemede{% endif %}
                                    </span>
                                    {% endif %}
                                </td>
                                <td>
                                    <button class="btn btn-outline-danger btn-sm" 
                                            onclick="removeParticipant({{ training.id }}, {{ participant.id }}, '{{ participant.tam_ad }}')">
                                        <i class="fas fa-trash"></i> Çıkar
                                    </button>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center text-muted py-4">
                    <i class="fas fa-users fa-3x mb-3"></i>
                    <p>Henüz katılımcı eklenmemiş.</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- Katılımcı Ekleme -->
    <div class="col-lg-4">
        <div class="card shadow">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-user-plus"></i> Katılımcı Ekle
                </h6>
            </div>
            <div class="card-body">
                {% if available_users %}
                <form id="addParticipantForm">
                    <div class="mb-3">
                        <label for="user_id" class="form-label">Kullanıcı Seç</label>
                        <select class="form-select" id="user_id" name="user_id" required>
                            <option value="">Kullanıcı seçin...</option>
                            {% for user in available_users %}
                            <option value="{{ user.id }}">
                                {{ user.tam_ad }} ({{ user.email }})
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary" id="addBtn">
                            <i class="fas fa-plus"></i> Katılımcı Ekle
                        </button>
                    </div>
                </form>
                
                <hr>
                
                <!-- Arama -->
                <div class="mb-3">
                    <label for="userSearch" class="form-label">Kullanıcı Ara</label>
                    <input type="text" class="form-control" id="userSearch" placeholder="Ad, soyad veya e-posta...">
                </div>
                
                <!-- Filtreleme -->
                <div class="mb-3">
                    <label for="roleFilter" class="form-label">Role Göre Filtrele</label>
                    <select class="form-select" id="roleFilter">
                        <option value="">Tüm Roller</option>
                        <option value="participant">Participant</option>
                        <option value="manager">Manager</option>
                        <option value="admin">Admin</option>
                    </select>
                </div>
                {% else %}
                <div class="text-center text-muted">
                    <i class="fas fa-info-circle fa-2x mb-3"></i>
                    <p>Eklenebilecek kullanıcı bulunmuyor.</p>
                    <small>Tüm aktif kullanıcılar zaten bu eğitime kayıtlı.</small>
                </div>
                {% endif %}
            </div>
        </div>
        
        <!-- İstatistikler -->
        <div class="card shadow mt-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-chart-pie"></i> İstatistikler
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6 mb-3">
                        <div class="h4 mb-0 text-primary">{{ current_participants|length }}</div>
                        <small class="text-muted">Toplam Katılımcı</small>
                    </div>
                    <div class="col-6 mb-3">
                        <div class="h4 mb-0 text-success">{{ training.teyit_eden_sayisi }}</div>
                        <small class="text-muted">Teyit Eden</small>
                    </div>
                    <div class="col-12">
                        <div class="h4 mb-0 text-info">{{ "%.1f"|format(training.teyit_orani) }}%</div>
                        <small class="text-muted">Teyit Oranı</small>
                    </div>
                </div>
                
                <hr>
                
                <div class="d-grid gap-2">
                    <button class="btn btn-outline-primary btn-sm" onclick="sendSMSToAll()">
                        <i class="fas fa-sms"></i> Tümüne SMS Gönder
                    </button>
                    <button class="btn btn-outline-info btn-sm" onclick="exportParticipants()">
                        <i class="fas fa-download"></i> Listeyi İndir
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Katılımcı ekleme formu
    $('#addParticipantForm').on('submit', function(e) {
        e.preventDefault();
        addParticipant();
    });
    
    // Kullanıcı arama
    $('#userSearch').on('input', function() {
        filterUsers();
    });
    
    // Rol filtresi
    $('#roleFilter').on('change', function() {
        filterUsers();
    });
});

function addParticipant() {
    let userId = $('#user_id').val();
    
    if (!userId) {
        alert('Lütfen bir kullanıcı seçin.');
        return;
    }
    
    $('#addBtn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Ekleniyor...');
    
    let formData = new FormData();
    formData.append('user_id', userId);
    
    fetch(`/training/{{ training.id }}/add-participant`, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(data.message);
            location.reload();
        } else {
            alert('Hata: ' + data.message);
        }
    })
    .catch(error => {
        alert('Bir hata oluştu: ' + error);
    })
    .finally(() => {
        $('#addBtn').prop('disabled', false).html('<i class="fas fa-plus"></i> Katılımcı Ekle');
    });
}

function removeParticipant(trainingId, userId, userName) {
    if (confirm(`${userName} kullanıcısını bu eğitimden çıkarmak istediğinizden emin misiniz?`)) {
        fetch(`/training/${trainingId}/remove-participant/${userId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(data.message);
                $(`#participant-${userId}`).fadeOut(300, function() {
                    $(this).remove();
                });
            } else {
                alert('Hata: ' + data.message);
            }
        })
        .catch(error => {
            alert('Bir hata oluştu: ' + error);
        });
    }
}

function filterUsers() {
    let searchTerm = $('#userSearch').val().toLowerCase();
    let roleFilter = $('#roleFilter').val();
    
    $('#user_id option').each(function() {
        if ($(this).val() === '') return; // Skip empty option
        
        let text = $(this).text().toLowerCase();
        let matchesSearch = text.includes(searchTerm);
        let matchesRole = !roleFilter || text.includes(roleFilter);
        
        if (matchesSearch && matchesRole) {
            $(this).show();
        } else {
            $(this).hide();
        }
    });
}

function sendSMSToAll() {
    if (confirm('Tüm katılımcılara SMS göndermek istediğinizden emin misiniz?')) {
        fetch('/api/send-sms-confirmations', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('SMS\'ler başarıyla gönderildi: ' + data.message);
            } else {
                alert('Hata: ' + data.message);
            }
        })
        .catch(error => {
            alert('Bir hata oluştu: ' + error);
        });
    }
}

function exportParticipants() {
    // CSV formatında katılımcı listesini indir
    let csvContent = "data:text/csv;charset=utf-8,";
    csvContent += "Ad Soyad,E-posta,Telefon,Rol,Durum\n";
    
    $('tbody tr').each(function() {
        let row = [];
        $(this).find('td').slice(0, 5).each(function() {
            let text = $(this).text().trim().replace(/\n/g, ' ').replace(/,/g, ';');
            row.push(text);
        });
        csvContent += row.join(',') + '\n';
    });
    
    let encodedUri = encodeURI(csvContent);
    let link = document.createElement("a");
    link.setAttribute("href", encodedUri);
    link.setAttribute("download", "{{ training.ad }}_katilimcilar.csv");
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}
</script>
{% endblock %}
