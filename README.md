# Eğitim Yönetim Sistemi

Şirket içi eğitimlerin etkinliğini artırmak ve takip süreçlerini otomatikleştirmek için geliştirilmiş web uygulaması.

## 🚀 Özellikler

### 📚 Eğitim Yönetimi
- Eğitim oluşturma ve düzenleme
- Katılımcı yönetimi
- Eğitim durumu takibi (planlanan, devam eden, tamamlanan)

### 📝 Dinamik Quiz Sistemi
- **Dinamik soru ekleme**: JavaScript ile sınırsız soru ve seçenek ekleme
- **Çoktan seçmeli sorular**: Her soru için özelleştirilebilir seçenekler
- **Otomatik puanlama**: Anlık skor hesaplama ve geçme durumu
- **Quiz sonuçları**: Detaylı raporlama ve istatistikler

### 📱 SMS Entegrasyonu (Twilio)
- Otomatik katılım teyidi SMS'leri
- Eğitim başlangıcından belirli gün önce gönderim
- SMS durumu takibi
- Toplu SMS gönderimi

### 📧 E-posta Entegrasyonu
- Yöneticilere otomatik anket e-postaları
- HTML e-posta şablonları
- Eğitim tamamlandıktan sonra otomatik gönderim

### ⏰ Zamanlanmış Görevler
- Günlük otomatik SMS gönderimi
- Otomatik anket e-postası gönderimi
- SMS durum kontrolü
- Eski log temizleme

### 👥 Kullanıcı Rolleri
- **Admin**: Tam sistem yönetimi
- **Manager**: Eğitim ve quiz yönetimi
- **Participant**: Eğitimlere katılım ve quiz çözme

## 🛠️ Teknolojiler

- **Backend**: Python Flask (MVC Mimarisi)
- **Veritabanı**: SQLite (SQLAlchemy ORM)
- **Frontend**: HTML, CSS, Bootstrap 5, Vanilla JavaScript/jQuery
- **SMS**: Twilio API
- **E-posta**: SMTP (Gmail, Outlook vb.)
- **Zamanlanmış Görevler**: APScheduler
- **Güvenlik**: Flask-Login, Token tabanlı doğrulama

## 📋 Gereksinimler

- Python 3.8+
- Sanal ortam (venv)
- Twilio hesabı (SMS için)
- SMTP e-posta hesabı

## 🔧 Kurulum

### 1. Projeyi İndirin
```bash
git clone <repository-url>
cd EGITIM
```

### 2. Sanal Ortam Oluşturun ve Aktive Edin
```bash
# Windows
python -m venv venv
venv\Scripts\activate

# Linux/Mac
python3 -m venv venv
source venv/bin/activate
```

### 3. Gerekli Paketleri Kurun
```bash
pip install -r requirements.txt
```

### 4. Ortam Değişkenlerini Ayarlayın
`.env.example` dosyasını `.env` olarak kopyalayın ve değerleri düzenleyin:

```bash
cp .env.example .env
```

`.env` dosyasında aşağıdaki değerleri güncelleyin:
```env
SECRET_KEY=your-secret-key-here
TWILIO_ACCOUNT_SID=your-twilio-account-sid
TWILIO_AUTH_TOKEN=your-twilio-auth-token
TWILIO_PHONE_NUMBER=your-twilio-phone-number
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-email-password
```

### 5. Veritabanını Başlatın
```bash
python run.py init-db
```

### 6. Admin Kullanıcısı Oluşturun
```bash
python run.py create-admin
```

### 7. Varsayılan Ayarları Oluşturun
```bash
python run.py init-settings
```

### 8. (Opsiyonel) Örnek Veri Oluşturun
```bash
python run.py create-sample-data
```

## 🚀 Çalıştırma

### Geliştirme Ortamında
```bash
python run.py
```

### Windows'ta Kolay Başlatma
```bash
activate_venv.bat
```

Uygulama `http://localhost:5000` adresinde çalışacaktır.

## 📖 Kullanım

### 1. Giriş Yapın
- Admin hesabıyla giriş yapın
- Dashboard'da sistem istatistiklerini görün

### 2. Kullanıcı Yönetimi (Admin)
- Yeni kullanıcılar oluşturun
- Roller atayın (admin, manager, participant)
- Kullanıcı durumlarını yönetin

### 3. Eğitim Oluşturma (Admin/Manager)
- Yeni eğitim oluşturun
- Katılımcıları ekleyin
- Eğitim tarihlerini belirleyin

### 4. Quiz Oluşturma (Admin/Manager)
- Dinamik quiz formu ile sorular ekleyin
- Her soru için çoktan seçmeli seçenekler oluşturun
- Doğru cevapları işaretleyin
- Geçer notu belirleyin

### 5. SMS ve E-posta Gönderimi
- Dashboard'dan manuel gönderim yapın
- Otomatik zamanlanmış gönderimler için bekleyin

## 🔧 Konfigürasyon

### SMS Ayarları (Twilio)
1. [Twilio Console](https://console.twilio.com/) hesabı oluşturun
2. Phone Number satın alın
3. Account SID ve Auth Token'ı alın
4. `.env` dosyasına ekleyin

### E-posta Ayarları
Gmail için:
1. 2FA aktif edin
2. App Password oluşturun
3. `.env` dosyasında kullanın

### Zamanlanmış Görevler
Varsayılan zamanlamalar:
- SMS gönderimi: Her gün 09:00
- Anket e-postası: Her gün 10:00
- SMS durum kontrolü: Her saat başı
- Log temizleme: Her gece 00:00

## 📁 Proje Yapısı

```
EGITIM/
├── app/
│   ├── __init__.py              # Flask app factory
│   ├── models/                  # Veritabanı modelleri
│   │   ├── user.py             # Kullanıcı modeli
│   │   ├── training.py         # Eğitim ve kayıt modelleri
│   │   ├── quiz.py             # Quiz ve deneme modelleri
│   │   └── communication.py    # İletişim ve anket modelleri
│   ├── controllers/            # Route'lar ve iş mantığı
│   │   ├── auth.py            # Kimlik doğrulama
│   │   ├── training.py        # Eğitim yönetimi
│   │   ├── quiz.py            # Quiz yönetimi
│   │   └── api.py             # API endpoint'leri
│   ├── templates/             # HTML şablonları
│   │   ├── base.html          # Ana şablon
│   │   ├── quiz_form.html     # Dinamik quiz formu
│   │   └── ...
│   ├── static/               # CSS, JS, resimler
│   └── utils/                # Yardımcı servisler
│       ├── sms_service.py    # Twilio SMS servisi
│       ├── email_service.py  # E-posta servisi
│       └── scheduler.py      # Zamanlanmış görevler
├── config.py                 # Konfigürasyon
├── run.py                   # Ana çalıştırma dosyası
├── requirements.txt         # Python bağımlılıkları
└── README.md               # Bu dosya
```

## 🔒 Güvenlik

- Şifreler hash'lenerek saklanır
- Token tabanlı doğrulama (7 gün geçerlilik)
- CSRF koruması
- SQL injection koruması (SQLAlchemy ORM)
- XSS koruması (Jinja2 auto-escape)

## 🐛 Sorun Giderme

### Yaygın Sorunlar

1. **SMS gönderilmiyor**
   - Twilio hesap bakiyesini kontrol edin
   - Phone number doğruluğunu kontrol edin
   - `.env` dosyasındaki Twilio ayarlarını kontrol edin

2. **E-posta gönderilmiyor**
   - SMTP ayarlarını kontrol edin
   - Gmail için App Password kullanın
   - Firewall/antivirus ayarlarını kontrol edin

3. **Veritabanı hatası**
   - `python run.py init-db` komutunu çalıştırın
   - Dosya izinlerini kontrol edin

## 📞 Destek

Sorularınız için:
- GitHub Issues oluşturun
- Dokümantasyonu inceleyin
- Log dosyalarını kontrol edin

## 📄 Lisans

Bu proje MIT lisansı altında lisanslanmıştır.

## 🤝 Katkıda Bulunma

1. Fork yapın
2. Feature branch oluşturun
3. Değişikliklerinizi commit edin
4. Pull request gönderin

## 📈 Gelecek Özellikler

- [ ] Çoklu dil desteği
- [ ] Gelişmiş raporlama
- [ ] Mobil uygulama
- [ ] Video eğitim desteği
- [ ] Sertifika sistemi
- [ ] API dokümantasyonu
