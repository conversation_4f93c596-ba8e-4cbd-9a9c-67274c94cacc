from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.cron import CronTrigger
from datetime import datetime, timedelta
import logging
import atexit

class SchedulerService:
    """Zamanlanmış görevler servisi"""
    
    def __init__(self, app=None):
        self.scheduler = None
        self.app = app
        if app:
            self.init_app(app)
    
    def init_app(self, app):
        """Flask uygulaması ile başlat"""
        self.app = app
        self.scheduler = BackgroundScheduler()
        
        # Uygulama kapanırken scheduler'ı durdur
        atexit.register(lambda: self.scheduler.shutdown())
        
        # Zamanlanmış görevleri ekle
        self._add_scheduled_jobs()
        
        # Scheduler'ı başlat
        try:
            self.scheduler.start()
            logging.info("Scheduler başlatıldı")
        except Exception as e:
            logging.error(f"Scheduler başlatma hatası: {str(e)}")
    
    def _add_scheduled_jobs(self):
        """Zamanlanmış görevleri ekle"""
        
        # Her gün saat 09:00'da SMS teyitlerini gönder
        self.scheduler.add_job(
            func=self._send_sms_confirmations,
            trigger=CronTrigger(hour=9, minute=0),
            id='send_sms_confirmations',
            name='SMS Katılım Teyitlerini Gönder',
            replace_existing=True
        )
        
        # Her gün saat 10:00'da anket e-postalarını gönder
        self.scheduler.add_job(
            func=self._send_survey_emails,
            trigger=CronTrigger(hour=10, minute=0),
            id='send_survey_emails',
            name='Anket E-postalarını Gönder',
            replace_existing=True
        )
        
        # Her saat başında SMS durumlarını kontrol et
        self.scheduler.add_job(
            func=self._check_sms_status,
            trigger=CronTrigger(minute=0),
            id='check_sms_status',
            name='SMS Durumlarını Kontrol Et',
            replace_existing=True
        )
        
        # Her gün gece yarısı eski logları temizle
        self.scheduler.add_job(
            func=self._cleanup_old_logs,
            trigger=CronTrigger(hour=0, minute=0),
            id='cleanup_old_logs',
            name='Eski Logları Temizle',
            replace_existing=True
        )
    
    def _send_sms_confirmations(self):
        """SMS katılım teyitlerini gönder"""
        try:
            with self.app.app_context():
                from app.models.training import Training, Enrollment
                from app.models.communication import CommunicationLog
                from app.utils.sms_service import SMSService
                from app import db
                from flask import current_app
                
                # Yaklaşan eğitimleri bul
                days_before = current_app.config.get('SMS_SEND_DAYS_BEFORE', 3)
                target_date = datetime.utcnow() + timedelta(days=days_before)
                
                upcoming_trainings = Training.query.filter(
                    Training.baslangic_tarihi >= datetime.utcnow(),
                    Training.baslangic_tarihi <= target_date,
                    Training.aktif_mi == True
                ).all()
                
                sms_service = SMSService()
                sent_count = 0
                
                for training in upcoming_trainings:
                    # Henüz SMS gönderilmemiş katılımcıları bul
                    enrollments = training.enrollments.filter_by(
                        aktif_mi=True,
                        katilim_teyidi_durumu='beklemede'
                    ).filter(
                        Enrollment.teyit_sms_id.is_(None)
                    ).all()
                    
                    for enrollment in enrollments:
                        user = enrollment.user
                        
                        # Token oluştur ve SMS gönder
                        from app.controllers.api import generate_token
                        token_data = {
                            'enrollment_id': enrollment.id,
                            'user_id': user.id,
                            'training_id': training.id
                        }
                        token = generate_token(token_data, salt='sms_confirmation')
                        
                        confirmation_url = f"{current_app.config['BASE_URL']}/api/participant/confirm/{token}"
                        message = sms_service.create_confirmation_message(
                            user.ad, training.ad, confirmation_url
                        )
                        
                        result = sms_service.send_sms(user.telefon_numarasi, message)
                        
                        # Log kaydı oluştur
                        log = CommunicationLog(
                            type='sms',
                            recipient_id=user.id,
                            content=message,
                            status='sent' if result['success'] else 'failed',
                            external_id=result.get('sid'),
                            error_message=result.get('error'),
                            training_id=training.id,
                            enrollment_id=enrollment.id
                        )
                        db.session.add(log)
                        
                        if result['success']:
                            enrollment.teyit_sms_id = result.get('sid')
                            sent_count += 1
                
                db.session.commit()
                logging.info(f"Scheduler: {sent_count} SMS teyidi gönderildi")
                
        except Exception as e:
            logging.error(f"SMS teyit gönderme hatası: {str(e)}")
    
    def _send_survey_emails(self):
        """Anket e-postalarını gönder"""
        try:
            with self.app.app_context():
                from app.models.training import Training
                from app.models.user import User
                from app.models.communication import CommunicationLog
                from app.utils.email_service import EmailService
                from app import db
                from flask import current_app
                
                # Tamamlanan eğitimleri bul
                days_after = current_app.config.get('SURVEY_SEND_DAYS_AFTER', 1)
                target_date = datetime.utcnow() - timedelta(days=days_after)
                
                completed_trainings = Training.query.filter(
                    Training.bitis_tarihi <= target_date,
                    Training.aktif_mi == True
                ).all()
                
                email_service = EmailService()
                sent_count = 0
                
                for training in completed_trainings:
                    # Bu eğitim için daha önce anket e-postası gönderilmiş mi kontrol et
                    existing_logs = CommunicationLog.query.filter_by(
                        type='email',
                        training_id=training.id,
                        status='sent'
                    ).first()
                    
                    if existing_logs:
                        continue  # Bu eğitim için zaten e-posta gönderilmiş
                    
                    # Manager'lara e-posta gönder
                    managers = User.query.filter_by(rol='manager', aktif_mi=True).all()
                    
                    for manager in managers:
                        from app.controllers.api import generate_token
                        token_data = {
                            'manager_id': manager.id,
                            'training_id': training.id
                        }
                        token = generate_token(token_data, salt='survey_email')
                        
                        survey_url = f"{current_app.config['BASE_URL']}/api/manager/survey/{token}"
                        email_data = email_service.create_survey_email(
                            manager.tam_ad, training.ad, survey_url
                        )
                        
                        result = email_service.send_email(
                            to_email=manager.email,
                            subject=email_data['subject'],
                            html_content=email_data['html_content'],
                            text_content=email_data['text_content']
                        )
                        
                        # Log kaydı oluştur
                        log = CommunicationLog(
                            type='email',
                            recipient_id=manager.id,
                            content=email_data['html_content'],
                            status='sent' if result['success'] else 'failed',
                            external_id=result.get('message_id'),
                            error_message=result.get('error'),
                            training_id=training.id
                        )
                        db.session.add(log)
                        
                        if result['success']:
                            sent_count += 1
                
                db.session.commit()
                logging.info(f"Scheduler: {sent_count} anket e-postası gönderildi")
                
        except Exception as e:
            logging.error(f"Anket e-postası gönderme hatası: {str(e)}")
    
    def _check_sms_status(self):
        """SMS durumlarını kontrol et"""
        try:
            with self.app.app_context():
                from app.models.communication import CommunicationLog
                from app.utils.sms_service import SMSService
                from app import db
                
                # Son 24 saat içinde gönderilen SMS'leri kontrol et
                yesterday = datetime.utcnow() - timedelta(days=1)
                pending_sms = CommunicationLog.query.filter(
                    CommunicationLog.type == 'sms',
                    CommunicationLog.status == 'sent',
                    CommunicationLog.timestamp >= yesterday,
                    CommunicationLog.external_id.isnot(None)
                ).all()
                
                sms_service = SMSService()
                updated_count = 0
                
                for log in pending_sms:
                    status_result = sms_service.get_message_status(log.external_id)
                    
                    if status_result['success']:
                        status = status_result['status']
                        if status in ['delivered', 'failed', 'undelivered']:
                            # Durumu güncelle
                            if status == 'delivered':
                                log.status = 'sent'
                            else:
                                log.status = 'failed'
                                log.error_message = status_result.get('error_message')
                            
                            updated_count += 1
                
                if updated_count > 0:
                    db.session.commit()
                    logging.info(f"Scheduler: {updated_count} SMS durumu güncellendi")
                
        except Exception as e:
            logging.error(f"SMS durum kontrol hatası: {str(e)}")
    
    def _cleanup_old_logs(self):
        """Eski logları temizle"""
        try:
            with self.app.app_context():
                from app.models.communication import CommunicationLog
                from app import db
                
                # 30 günden eski logları sil
                cutoff_date = datetime.utcnow() - timedelta(days=30)
                old_logs = CommunicationLog.query.filter(
                    CommunicationLog.timestamp < cutoff_date
                ).all()
                
                deleted_count = len(old_logs)
                
                for log in old_logs:
                    db.session.delete(log)
                
                db.session.commit()
                
                if deleted_count > 0:
                    logging.info(f"Scheduler: {deleted_count} eski log kaydı temizlendi")
                
        except Exception as e:
            logging.error(f"Log temizleme hatası: {str(e)}")
    
    def add_job(self, func, trigger, job_id, name, **kwargs):
        """Yeni zamanlanmış görev ekle"""
        if self.scheduler:
            self.scheduler.add_job(
                func=func,
                trigger=trigger,
                id=job_id,
                name=name,
                replace_existing=True,
                **kwargs
            )
    
    def remove_job(self, job_id):
        """Zamanlanmış görevi kaldır"""
        if self.scheduler:
            try:
                self.scheduler.remove_job(job_id)
                return True
            except:
                return False
        return False
    
    def get_jobs(self):
        """Tüm zamanlanmış görevleri listele"""
        if self.scheduler:
            return self.scheduler.get_jobs()
        return []
