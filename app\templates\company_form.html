{% extends "base.html" %}

{% block title %}{{ 'Şirket Düzenle' if company else 'Yeni Şirket' }}{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-{{ 'edit' if company else 'plus' }}"></i> 
        {{ 'Şirket Düzenle' if company else 'Yeni Şirket' }}
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('company.companies') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> Şirket Listesi
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card shadow">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-building"></i> 
                    {{ company.tam_ad if company else 'Şirket Bilgileri' }}
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" id="companyForm">
                    <!-- Temel Bilgiler -->
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="kod" class="form-label">Şirket Kodu <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="kod" name="kod" 
                                       value="{{ company.kod if company else '' }}" 
                                       maxlength="10" style="text-transform: uppercase;" required>
                                <div class="form-text">Maksimum 10 karakter (örn: BYK, TAI)</div>
                            </div>
                        </div>
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label for="ad" class="form-label">Şirket Adı <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="ad" name="ad" 
                                       value="{{ company.ad if company else '' }}" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="aciklama" class="form-label">Açıklama</label>
                        <textarea class="form-control" id="aciklama" name="aciklama" rows="3">{{ company.aciklama if company else '' }}</textarea>
                        <div class="form-text">Şirket hakkında kısa açıklama</div>
                    </div>
                    
                    <!-- İletişim Bilgileri -->
                    <h6 class="mt-4 mb-3">
                        <i class="fas fa-address-book"></i> İletişim Bilgileri
                    </h6>
                    
                    <div class="mb-3">
                        <label for="adres" class="form-label">Adres</label>
                        <textarea class="form-control" id="adres" name="adres" rows="2">{{ company.adres if company else '' }}</textarea>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="telefon" class="form-label">Telefon</label>
                                <input type="tel" class="form-control" id="telefon" name="telefon" 
                                       value="{{ company.telefon if company else '' }}">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="email" class="form-label">E-posta</label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       value="{{ company.email if company else '' }}">
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="website" class="form-label">Website</label>
                        <input type="url" class="form-control" id="website" name="website" 
                               value="{{ company.website if company else '' }}" 
                               placeholder="https://www.example.com">
                    </div>
                    
                    <!-- Vergi Bilgileri -->
                    <h6 class="mt-4 mb-3">
                        <i class="fas fa-file-invoice"></i> Vergi Bilgileri
                    </h6>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="vergi_no" class="form-label">Vergi Numarası</label>
                                <input type="text" class="form-control" id="vergi_no" name="vergi_no" 
                                       value="{{ company.vergi_no if company else '' }}">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="vergi_dairesi" class="form-label">Vergi Dairesi</label>
                                <input type="text" class="form-control" id="vergi_dairesi" name="vergi_dairesi" 
                                       value="{{ company.vergi_dairesi if company else '' }}">
                            </div>
                        </div>
                    </div>
                    
                    <!-- Durum -->
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="aktif_mi" name="aktif_mi" 
                                   {{ 'checked' if not company or company.aktif_mi else '' }}>
                            <label class="form-check-label" for="aktif_mi">
                                Şirket aktif olsun
                            </label>
                            <div class="form-text">Pasif şirketler kullanıcı listelerinde görünmez</div>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('company.companies') }}" class="btn btn-secondary me-md-2">
                            <i class="fas fa-times"></i> İptal
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> 
                            {{ 'Değişiklikleri Kaydet' if company else 'Şirketi Oluştur' }}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        {% if company %}
        <!-- Mevcut Bilgiler -->
        <div class="card shadow">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle"></i> Mevcut Bilgiler
                </h6>
            </div>
            <div class="card-body">
                <table class="table table-sm">
                    <tr>
                        <td><strong>ID:</strong></td>
                        <td>{{ company.id }}</td>
                    </tr>
                    <tr>
                        <td><strong>Oluşturulma:</strong></td>
                        <td>{{ company.olusturulma_tarihi.strftime('%d.%m.%Y %H:%M') if company.olusturulma_tarihi else 'Bilinmiyor' }}</td>
                    </tr>
                    <tr>
                        <td><strong>Güncelleme:</strong></td>
                        <td>{{ company.guncelleme_tarihi.strftime('%d.%m.%Y %H:%M') if company.guncelleme_tarihi else 'Hiç güncellenmemiş' }}</td>
                    </tr>
                    <tr>
                        <td><strong>Oluşturan:</strong></td>
                        <td>{{ company.olusturan.tam_ad if company.olusturan else 'Bilinmiyor' }}</td>
                    </tr>
                    <tr>
                        <td><strong>Durum:</strong></td>
                        <td>
                            <span class="badge {{ 'bg-success' if company.aktif_mi else 'bg-secondary' }}">
                                {{ 'Aktif' if company.aktif_mi else 'Pasif' }}
                            </span>
                        </td>
                    </tr>
                </table>
            </div>
        </div>
        
        <!-- İstatistikler -->
        <div class="card shadow mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-chart-bar"></i> İstatistikler
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="h4 mb-0 text-primary">{{ company.kullanici_sayisi }}</div>
                        <small class="text-muted">Kullanıcı</small>
                    </div>
                    <div class="col-6">
                        <div class="h4 mb-0 text-success">{{ company.egitim_sayisi }}</div>
                        <small class="text-muted">Eğitim</small>
                    </div>
                </div>
                <hr>
                <div class="row text-center">
                    <div class="col-6">
                        <div class="h5 mb-0 text-info">{{ company.manager_sayisi }}</div>
                        <small class="text-muted">Manager</small>
                    </div>
                    <div class="col-6">
                        <div class="h5 mb-0 text-warning">{{ company.participant_sayisi }}</div>
                        <small class="text-muted">Katılımcı</small>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}
        
        <!-- Yönergeler -->
        <div class="card shadow mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-lightbulb"></i> Yönergeler
                </h6>
            </div>
            <div class="card-body">
                <div class="alert alert-info small">
                    <i class="fas fa-info-circle"></i>
                    <strong>Şirket Kodu:</strong> Benzersiz olmalı ve maksimum 10 karakter olabilir. 
                    Genellikle şirketin kısaltması kullanılır.
                </div>
                
                {% if company and company.kullanici_sayisi > 0 %}
                <div class="alert alert-warning small">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>Dikkat:</strong> Bu şirkette {{ company.kullanici_sayisi }} kullanıcı bulunmaktadır. 
                    Şirket bilgilerini değiştirirken dikkatli olun.
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Şirket kodu otomatik büyük harf
$('#kod').on('input', function() {
    $(this).val($(this).val().toUpperCase());
});

// Form submit öncesi onay
$('#companyForm').on('submit', function(e) {
    const companyName = $('#ad').val() + ' (' + $('#kod').val() + ')';
    const action = {{ 'güncellenmesini' if company else 'oluşturulmasını' }};
    
    if (!confirm(companyName + ' şirketinin ' + action + ' istediğinizden emin misiniz?')) {
        e.preventDefault();
        return false;
    }
});

// Website URL formatı
$('#website').on('blur', function() {
    let url = $(this).val().trim();
    if (url && !url.startsWith('http://') && !url.startsWith('https://')) {
        $(this).val('https://' + url);
    }
});
</script>
{% endblock %}
