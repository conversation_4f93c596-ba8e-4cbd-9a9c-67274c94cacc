import json
from datetime import datetime
from app import db

class CommunicationLog(db.Model):
    """İletişim log modeli (SMS ve Email)"""
    __tablename__ = 'communication_logs'
    
    id = db.Column(db.Integer, primary_key=True)
    type = db.Column(db.<PERSON>um('sms', 'email', name='communication_types'), nullable=False)
    recipient_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    content = db.Column(db.Text, nullable=False)
    status = db.Column(db.Enum('sent', 'failed', 'pending', name='communication_status'), 
                      default='pending', nullable=False)
    external_id = db.Column(db.String(100))  # Twilio SID veya Email Message ID
    error_message = db.Column(db.Text)
    timestamp = db.Column(db.DateTime, default=datetime.utcnow)
    
    # <PERSON><PERSON><PERSON><PERSON><PERSON> (opsiyonel)
    training_id = db.Column(db.<PERSON><PERSON><PERSON>, db.<PERSON>('trainings.id'))
    enrollment_id = db.Column(db.In<PERSON>ger, db.<PERSON>('enrollments.id'))
    
    def __init__(self, **kwargs):
        super(CommunicationLog, self).__init__(**kwargs)
    
    def mark_as_sent(self, external_id=None):
        """Gönderildi olarak işaretle"""
        self.status = 'sent'
        if external_id:
            self.external_id = external_id
        db.session.commit()
    
    def mark_as_failed(self, error_message=None):
        """Başarısız olarak işaretle"""
        self.status = 'failed'
        if error_message:
            self.error_message = error_message
        db.session.commit()
    
    @property
    def is_sent(self):
        """Gönderildi mi"""
        return self.status == 'sent'
    
    @property
    def is_failed(self):
        """Başarısız mı"""
        return self.status == 'failed'
    
    @property
    def is_pending(self):
        """Beklemede mi"""
        return self.status == 'pending'
    
    def __repr__(self):
        return f'<CommunicationLog {self.type} - {self.recipient.email} - {self.status}>'
    
    def to_dict(self):
        """Log bilgilerini dictionary olarak döndür"""
        return {
            'id': self.id,
            'type': self.type,
            'recipient_id': self.recipient_id,
            'content': self.content[:100] + '...' if len(self.content) > 100 else self.content,
            'status': self.status,
            'external_id': self.external_id,
            'error_message': self.error_message,
            'timestamp': self.timestamp.isoformat() if self.timestamp else None,
            'recipient_name': self.recipient.tam_ad if self.recipient else None
        }


class Survey(db.Model):
    """Anket modeli"""
    __tablename__ = 'surveys'
    
    id = db.Column(db.Integer, primary_key=True)
    training_id = db.Column(db.Integer, db.ForeignKey('trainings.id'), nullable=False)
    ad = db.Column(db.String(200), nullable=False)
    aciklama = db.Column(db.Text)
    sorular_json = db.Column(db.Text, nullable=False)  # JSON string olarak sorular
    aktif_mi = db.Column(db.Boolean, default=True, nullable=False)
    olusturulma_tarihi = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Anketi oluşturan kullanıcı
    olusturan_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    olusturan = db.relationship('User', backref='olusturulan_anketler')
    
    # İlişkiler
    responses = db.relationship('SurveyResponse', backref='survey', lazy='dynamic', cascade='all, delete-orphan')
    
    def __init__(self, **kwargs):
        super(Survey, self).__init__(**kwargs)
    
    @property
    def sorular(self):
        """Soruları Python objesi olarak döndür"""
        try:
            return json.loads(self.sorular_json) if self.sorular_json else []
        except json.JSONDecodeError:
            return []
    
    @sorular.setter
    def sorular(self, value):
        """Soruları JSON string olarak kaydet"""
        self.sorular_json = json.dumps(value, ensure_ascii=False)
    
    @property
    def soru_sayisi(self):
        """Toplam soru sayısı"""
        return len(self.sorular)
    
    @property
    def yanit_sayisi(self):
        """Toplam yanıt sayısı"""
        return self.responses.count()
    
    def get_manager_response(self, manager_id):
        """Yöneticinin yanıtını getir"""
        return self.responses.filter_by(manager_id=manager_id).first()
    
    def has_manager_responded(self, manager_id):
        """Yönetici yanıt verdi mi"""
        return self.get_manager_response(manager_id) is not None
    
    def __repr__(self):
        return f'<Survey {self.ad}>'
    
    def to_dict(self, include_questions=False):
        """Anket bilgilerini dictionary olarak döndür"""
        data = {
            'id': self.id,
            'training_id': self.training_id,
            'ad': self.ad,
            'aciklama': self.aciklama,
            'aktif_mi': self.aktif_mi,
            'olusturulma_tarihi': self.olusturulma_tarihi.isoformat() if self.olusturulma_tarihi else None,
            'soru_sayisi': self.soru_sayisi,
            'yanit_sayisi': self.yanit_sayisi
        }
        
        if include_questions:
            data['sorular'] = self.sorular
            
        return data


class SurveyResponse(db.Model):
    """Anket yanıt modeli"""
    __tablename__ = 'survey_responses'
    
    id = db.Column(db.Integer, primary_key=True)
    manager_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    training_id = db.Column(db.Integer, db.ForeignKey('trainings.id'), nullable=False)
    survey_id = db.Column(db.Integer, db.ForeignKey('surveys.id'), nullable=False)
    yanitlar_json = db.Column(db.Text, nullable=False)  # JSON string olarak yanıtlar
    yanit_tarihi = db.Column(db.DateTime, default=datetime.utcnow)
    anket_mail_id = db.Column(db.String(100))  # Email Message ID
    
    # Benzersiz constraint
    __table_args__ = (db.UniqueConstraint('manager_id', 'survey_id', name='unique_manager_survey'),)
    
    def __init__(self, **kwargs):
        super(SurveyResponse, self).__init__(**kwargs)
    
    @property
    def yanitlar(self):
        """Yanıtları Python objesi olarak döndür"""
        try:
            return json.loads(self.yanitlar_json) if self.yanitlar_json else {}
        except json.JSONDecodeError:
            return {}
    
    @yanitlar.setter
    def yanitlar(self, value):
        """Yanıtları JSON string olarak kaydet"""
        self.yanitlar_json = json.dumps(value, ensure_ascii=False)
    
    def __repr__(self):
        return f'<SurveyResponse {self.manager.tam_ad} - {self.survey.ad}>'
    
    def to_dict(self):
        """Yanıt bilgilerini dictionary olarak döndür"""
        return {
            'id': self.id,
            'manager_id': self.manager_id,
            'training_id': self.training_id,
            'survey_id': self.survey_id,
            'yanit_tarihi': self.yanit_tarihi.isoformat() if self.yanit_tarihi else None,
            'anket_mail_id': self.anket_mail_id,
            'manager_name': self.manager.tam_ad if self.manager else None,
            'survey_name': self.survey.ad if self.survey else None,
            'training_name': self.training.ad if self.training else None
        }


class Setting(db.Model):
    """Uygulama ayarları modeli"""
    __tablename__ = 'settings'
    
    id = db.Column(db.Integer, primary_key=True)
    key = db.Column(db.String(100), unique=True, nullable=False)
    value = db.Column(db.Text, nullable=False)
    description = db.Column(db.Text)
    data_type = db.Column(db.Enum('string', 'integer', 'float', 'boolean', 'json', 
                                 name='setting_types'), default='string', nullable=False)
    olusturulma_tarihi = db.Column(db.DateTime, default=datetime.utcnow)
    guncelleme_tarihi = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def __init__(self, **kwargs):
        super(Setting, self).__init__(**kwargs)
    
    @property
    def typed_value(self):
        """Tipine göre değeri döndür"""
        if self.data_type == 'integer':
            return int(self.value)
        elif self.data_type == 'float':
            return float(self.value)
        elif self.data_type == 'boolean':
            return self.value.lower() in ['true', '1', 'yes', 'on']
        elif self.data_type == 'json':
            try:
                return json.loads(self.value)
            except json.JSONDecodeError:
                return {}
        else:
            return self.value
    
    @classmethod
    def get_value(cls, key, default=None):
        """Ayar değerini getir"""
        setting = cls.query.filter_by(key=key).first()
        if setting:
            return setting.typed_value
        return default
    
    @classmethod
    def set_value(cls, key, value, description=None, data_type='string'):
        """Ayar değerini kaydet"""
        setting = cls.query.filter_by(key=key).first()
        if setting:
            setting.value = str(value)
            setting.guncelleme_tarihi = datetime.utcnow()
        else:
            setting = cls(
                key=key,
                value=str(value),
                description=description,
                data_type=data_type
            )
            db.session.add(setting)
        db.session.commit()
        return setting
    
    def __repr__(self):
        return f'<Setting {self.key}={self.value}>'
    
    def to_dict(self):
        """Ayar bilgilerini dictionary olarak döndür"""
        return {
            'id': self.id,
            'key': self.key,
            'value': self.value,
            'typed_value': self.typed_value,
            'description': self.description,
            'data_type': self.data_type,
            'olusturulma_tarihi': self.olusturulma_tarihi.isoformat() if self.olusturulma_tarihi else None,
            'guncelleme_tarihi': self.guncelleme_tarihi.isoformat() if self.guncelleme_tarihi else None
        }
