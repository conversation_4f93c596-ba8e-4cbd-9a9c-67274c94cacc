r"""
    This code was generated by
   ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
    |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
    |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

    Twilio - Preview
    This is the public Twilio REST API.

    NOTE: This class is auto generated by OpenAPI Generator.
    https://openapi-generator.tech
    Do not edit the class manually.
"""


from typing import Any, Dict, Optional, Union
from twilio.base import serialize, values
from twilio.base.instance_context import InstanceContext
from twilio.base.instance_resource import InstanceResource
from twilio.base.list_resource import ListResource
from twilio.base.version import Version


class AssistantFallbackActionsInstance(InstanceResource):

    """
    :ivar account_sid:
    :ivar assistant_sid:
    :ivar url:
    :ivar data:
    """

    def __init__(self, version: Version, payload: Dict[str, Any], assistant_sid: str):
        super().__init__(version)

        self.account_sid: Optional[str] = payload.get("account_sid")
        self.assistant_sid: Optional[str] = payload.get("assistant_sid")
        self.url: Optional[str] = payload.get("url")
        self.data: Optional[Dict[str, object]] = payload.get("data")

        self._solution = {
            "assistant_sid": assistant_sid,
        }
        self._context: Optional[AssistantFallbackActionsContext] = None

    @property
    def _proxy(self) -> "AssistantFallbackActionsContext":
        """
        Generate an instance context for the instance, the context is capable of
        performing various actions. All instance actions are proxied to the context

        :returns: AssistantFallbackActionsContext for this AssistantFallbackActionsInstance
        """
        if self._context is None:
            self._context = AssistantFallbackActionsContext(
                self._version,
                assistant_sid=self._solution["assistant_sid"],
            )
        return self._context

    def fetch(self) -> "AssistantFallbackActionsInstance":
        """
        Fetch the AssistantFallbackActionsInstance


        :returns: The fetched AssistantFallbackActionsInstance
        """
        return self._proxy.fetch()

    async def fetch_async(self) -> "AssistantFallbackActionsInstance":
        """
        Asynchronous coroutine to fetch the AssistantFallbackActionsInstance


        :returns: The fetched AssistantFallbackActionsInstance
        """
        return await self._proxy.fetch_async()

    def update(
        self, fallback_actions: Union[object, object] = values.unset
    ) -> "AssistantFallbackActionsInstance":
        """
        Update the AssistantFallbackActionsInstance

        :param fallback_actions:

        :returns: The updated AssistantFallbackActionsInstance
        """
        return self._proxy.update(
            fallback_actions=fallback_actions,
        )

    async def update_async(
        self, fallback_actions: Union[object, object] = values.unset
    ) -> "AssistantFallbackActionsInstance":
        """
        Asynchronous coroutine to update the AssistantFallbackActionsInstance

        :param fallback_actions:

        :returns: The updated AssistantFallbackActionsInstance
        """
        return await self._proxy.update_async(
            fallback_actions=fallback_actions,
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Preview.Understand.AssistantFallbackActionsInstance {}>".format(
            context
        )


class AssistantFallbackActionsContext(InstanceContext):
    def __init__(self, version: Version, assistant_sid: str):
        """
        Initialize the AssistantFallbackActionsContext

        :param version: Version that contains the resource
        :param assistant_sid:
        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "assistant_sid": assistant_sid,
        }
        self._uri = "/Assistants/{assistant_sid}/FallbackActions".format(
            **self._solution
        )

    def fetch(self) -> AssistantFallbackActionsInstance:
        """
        Fetch the AssistantFallbackActionsInstance


        :returns: The fetched AssistantFallbackActionsInstance
        """

        payload = self._version.fetch(
            method="GET",
            uri=self._uri,
        )

        return AssistantFallbackActionsInstance(
            self._version,
            payload,
            assistant_sid=self._solution["assistant_sid"],
        )

    async def fetch_async(self) -> AssistantFallbackActionsInstance:
        """
        Asynchronous coroutine to fetch the AssistantFallbackActionsInstance


        :returns: The fetched AssistantFallbackActionsInstance
        """

        payload = await self._version.fetch_async(
            method="GET",
            uri=self._uri,
        )

        return AssistantFallbackActionsInstance(
            self._version,
            payload,
            assistant_sid=self._solution["assistant_sid"],
        )

    def update(
        self, fallback_actions: Union[object, object] = values.unset
    ) -> AssistantFallbackActionsInstance:
        """
        Update the AssistantFallbackActionsInstance

        :param fallback_actions:

        :returns: The updated AssistantFallbackActionsInstance
        """
        data = values.of(
            {
                "FallbackActions": serialize.object(fallback_actions),
            }
        )

        payload = self._version.update(
            method="POST",
            uri=self._uri,
            data=data,
        )

        return AssistantFallbackActionsInstance(
            self._version, payload, assistant_sid=self._solution["assistant_sid"]
        )

    async def update_async(
        self, fallback_actions: Union[object, object] = values.unset
    ) -> AssistantFallbackActionsInstance:
        """
        Asynchronous coroutine to update the AssistantFallbackActionsInstance

        :param fallback_actions:

        :returns: The updated AssistantFallbackActionsInstance
        """
        data = values.of(
            {
                "FallbackActions": serialize.object(fallback_actions),
            }
        )

        payload = await self._version.update_async(
            method="POST",
            uri=self._uri,
            data=data,
        )

        return AssistantFallbackActionsInstance(
            self._version, payload, assistant_sid=self._solution["assistant_sid"]
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Preview.Understand.AssistantFallbackActionsContext {}>".format(
            context
        )


class AssistantFallbackActionsList(ListResource):
    def __init__(self, version: Version, assistant_sid: str):
        """
        Initialize the AssistantFallbackActionsList

        :param version: Version that contains the resource
        :param assistant_sid:

        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "assistant_sid": assistant_sid,
        }

    def get(self) -> AssistantFallbackActionsContext:
        """
        Constructs a AssistantFallbackActionsContext

        """
        return AssistantFallbackActionsContext(
            self._version, assistant_sid=self._solution["assistant_sid"]
        )

    def __call__(self) -> AssistantFallbackActionsContext:
        """
        Constructs a AssistantFallbackActionsContext

        """
        return AssistantFallbackActionsContext(
            self._version, assistant_sid=self._solution["assistant_sid"]
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Preview.Understand.AssistantFallbackActionsList>"
