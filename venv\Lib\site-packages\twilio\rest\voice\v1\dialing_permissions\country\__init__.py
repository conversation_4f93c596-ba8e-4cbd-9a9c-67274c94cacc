r"""
    This code was generated by
   ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
    |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
    |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

    Twilio - Voice
    This is the public Twilio REST API.

    NOTE: This class is auto generated by OpenAPI Generator.
    https://openapi-generator.tech
    Do not edit the class manually.
"""


from typing import Any, Dict, List, Optional, Union, Iterator, AsyncIterator
from twilio.base import values
from twilio.base.instance_context import InstanceContext
from twilio.base.instance_resource import InstanceResource
from twilio.base.list_resource import ListResource
from twilio.base.version import Version
from twilio.base.page import Page
from twilio.rest.voice.v1.dialing_permissions.country.highrisk_special_prefix import (
    HighriskSpecialPrefixList,
)


class CountryInstance(InstanceResource):

    """
    :ivar iso_code: The [ISO country code](https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2).
    :ivar name: The name of the country.
    :ivar continent: The name of the continent in which the country is located.
    :ivar country_codes: The E.164 assigned [country codes(s)](https://www.itu.int/itudoc/itu-t/ob-lists/icc/e164_763.html)
    :ivar low_risk_numbers_enabled: Whether dialing to low-risk numbers is enabled.
    :ivar high_risk_special_numbers_enabled: Whether dialing to high-risk special services numbers is enabled. These prefixes include number ranges allocated by the country and include premium numbers, special services, shared cost, and others
    :ivar high_risk_tollfraud_numbers_enabled: Whether dialing to high-risk [toll fraud](https://www.twilio.com/blog/how-to-protect-your-account-from-toll-fraud-with-voice-dialing-geo-permissions-html) numbers is enabled. These prefixes include narrow number ranges that have a high-risk of international revenue sharing fraud (IRSF) attacks, also known as [toll fraud](https://www.twilio.com/blog/how-to-protect-your-account-from-toll-fraud-with-voice-dialing-geo-permissions-html). These prefixes are collected from anti-fraud databases and verified by analyzing calls on our network. These prefixes are not available for download and are updated frequently
    :ivar url: The absolute URL of this resource.
    :ivar links: A list of URLs related to this resource.
    """

    def __init__(
        self, version: Version, payload: Dict[str, Any], iso_code: Optional[str] = None
    ):
        super().__init__(version)

        self.iso_code: Optional[str] = payload.get("iso_code")
        self.name: Optional[str] = payload.get("name")
        self.continent: Optional[str] = payload.get("continent")
        self.country_codes: Optional[List[str]] = payload.get("country_codes")
        self.low_risk_numbers_enabled: Optional[bool] = payload.get(
            "low_risk_numbers_enabled"
        )
        self.high_risk_special_numbers_enabled: Optional[bool] = payload.get(
            "high_risk_special_numbers_enabled"
        )
        self.high_risk_tollfraud_numbers_enabled: Optional[bool] = payload.get(
            "high_risk_tollfraud_numbers_enabled"
        )
        self.url: Optional[str] = payload.get("url")
        self.links: Optional[Dict[str, object]] = payload.get("links")

        self._solution = {
            "iso_code": iso_code or self.iso_code,
        }
        self._context: Optional[CountryContext] = None

    @property
    def _proxy(self) -> "CountryContext":
        """
        Generate an instance context for the instance, the context is capable of
        performing various actions. All instance actions are proxied to the context

        :returns: CountryContext for this CountryInstance
        """
        if self._context is None:
            self._context = CountryContext(
                self._version,
                iso_code=self._solution["iso_code"],
            )
        return self._context

    def fetch(self) -> "CountryInstance":
        """
        Fetch the CountryInstance


        :returns: The fetched CountryInstance
        """
        return self._proxy.fetch()

    async def fetch_async(self) -> "CountryInstance":
        """
        Asynchronous coroutine to fetch the CountryInstance


        :returns: The fetched CountryInstance
        """
        return await self._proxy.fetch_async()

    @property
    def highrisk_special_prefixes(self) -> HighriskSpecialPrefixList:
        """
        Access the highrisk_special_prefixes
        """
        return self._proxy.highrisk_special_prefixes

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Voice.V1.CountryInstance {}>".format(context)


class CountryContext(InstanceContext):
    def __init__(self, version: Version, iso_code: str):
        """
        Initialize the CountryContext

        :param version: Version that contains the resource
        :param iso_code: The [ISO country code](https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2) of the DialingPermissions Country resource to fetch
        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "iso_code": iso_code,
        }
        self._uri = "/DialingPermissions/Countries/{iso_code}".format(**self._solution)

        self._highrisk_special_prefixes: Optional[HighriskSpecialPrefixList] = None

    def fetch(self) -> CountryInstance:
        """
        Fetch the CountryInstance


        :returns: The fetched CountryInstance
        """

        payload = self._version.fetch(
            method="GET",
            uri=self._uri,
        )

        return CountryInstance(
            self._version,
            payload,
            iso_code=self._solution["iso_code"],
        )

    async def fetch_async(self) -> CountryInstance:
        """
        Asynchronous coroutine to fetch the CountryInstance


        :returns: The fetched CountryInstance
        """

        payload = await self._version.fetch_async(
            method="GET",
            uri=self._uri,
        )

        return CountryInstance(
            self._version,
            payload,
            iso_code=self._solution["iso_code"],
        )

    @property
    def highrisk_special_prefixes(self) -> HighriskSpecialPrefixList:
        """
        Access the highrisk_special_prefixes
        """
        if self._highrisk_special_prefixes is None:
            self._highrisk_special_prefixes = HighriskSpecialPrefixList(
                self._version,
                self._solution["iso_code"],
            )
        return self._highrisk_special_prefixes

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Voice.V1.CountryContext {}>".format(context)


class CountryPage(Page):
    def get_instance(self, payload: Dict[str, Any]) -> CountryInstance:
        """
        Build an instance of CountryInstance

        :param payload: Payload response from the API
        """
        return CountryInstance(self._version, payload)

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Voice.V1.CountryPage>"


class CountryList(ListResource):
    def __init__(self, version: Version):
        """
        Initialize the CountryList

        :param version: Version that contains the resource

        """
        super().__init__(version)

        self._uri = "/DialingPermissions/Countries"

    def stream(
        self,
        iso_code: Union[str, object] = values.unset,
        continent: Union[str, object] = values.unset,
        country_code: Union[str, object] = values.unset,
        low_risk_numbers_enabled: Union[bool, object] = values.unset,
        high_risk_special_numbers_enabled: Union[bool, object] = values.unset,
        high_risk_tollfraud_numbers_enabled: Union[bool, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> Iterator[CountryInstance]:
        """
        Streams CountryInstance records from the API as a generator stream.
        This operation lazily loads records as efficiently as possible until the limit
        is reached.
        The results are returned as a generator, so this operation is memory efficient.

        :param str iso_code: Filter to retrieve the country permissions by specifying the [ISO country code](https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2)
        :param str continent: Filter to retrieve the country permissions by specifying the continent
        :param str country_code: Filter the results by specified [country codes](https://www.itu.int/itudoc/itu-t/ob-lists/icc/e164_763.html)
        :param bool low_risk_numbers_enabled: Filter to retrieve the country permissions with dialing to low-risk numbers enabled. Can be: `true` or `false`.
        :param bool high_risk_special_numbers_enabled: Filter to retrieve the country permissions with dialing to high-risk special service numbers enabled. Can be: `true` or `false`
        :param bool high_risk_tollfraud_numbers_enabled: Filter to retrieve the country permissions with dialing to high-risk [toll fraud](https://www.twilio.com/blog/how-to-protect-your-account-from-toll-fraud-with-voice-dialing-geo-permissions-html) numbers enabled. Can be: `true` or `false`.
        :param limit: Upper limit for the number of records to return. stream()
                      guarantees to never return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, stream() will attempt to read the
                          limit with the most efficient page size, i.e. min(limit, 1000)

        :returns: Generator that will yield up to limit results
        """
        limits = self._version.read_limits(limit, page_size)
        page = self.page(
            iso_code=iso_code,
            continent=continent,
            country_code=country_code,
            low_risk_numbers_enabled=low_risk_numbers_enabled,
            high_risk_special_numbers_enabled=high_risk_special_numbers_enabled,
            high_risk_tollfraud_numbers_enabled=high_risk_tollfraud_numbers_enabled,
            page_size=limits["page_size"],
        )

        return self._version.stream(page, limits["limit"])

    async def stream_async(
        self,
        iso_code: Union[str, object] = values.unset,
        continent: Union[str, object] = values.unset,
        country_code: Union[str, object] = values.unset,
        low_risk_numbers_enabled: Union[bool, object] = values.unset,
        high_risk_special_numbers_enabled: Union[bool, object] = values.unset,
        high_risk_tollfraud_numbers_enabled: Union[bool, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> AsyncIterator[CountryInstance]:
        """
        Asynchronously streams CountryInstance records from the API as a generator stream.
        This operation lazily loads records as efficiently as possible until the limit
        is reached.
        The results are returned as a generator, so this operation is memory efficient.

        :param str iso_code: Filter to retrieve the country permissions by specifying the [ISO country code](https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2)
        :param str continent: Filter to retrieve the country permissions by specifying the continent
        :param str country_code: Filter the results by specified [country codes](https://www.itu.int/itudoc/itu-t/ob-lists/icc/e164_763.html)
        :param bool low_risk_numbers_enabled: Filter to retrieve the country permissions with dialing to low-risk numbers enabled. Can be: `true` or `false`.
        :param bool high_risk_special_numbers_enabled: Filter to retrieve the country permissions with dialing to high-risk special service numbers enabled. Can be: `true` or `false`
        :param bool high_risk_tollfraud_numbers_enabled: Filter to retrieve the country permissions with dialing to high-risk [toll fraud](https://www.twilio.com/blog/how-to-protect-your-account-from-toll-fraud-with-voice-dialing-geo-permissions-html) numbers enabled. Can be: `true` or `false`.
        :param limit: Upper limit for the number of records to return. stream()
                      guarantees to never return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, stream() will attempt to read the
                          limit with the most efficient page size, i.e. min(limit, 1000)

        :returns: Generator that will yield up to limit results
        """
        limits = self._version.read_limits(limit, page_size)
        page = await self.page_async(
            iso_code=iso_code,
            continent=continent,
            country_code=country_code,
            low_risk_numbers_enabled=low_risk_numbers_enabled,
            high_risk_special_numbers_enabled=high_risk_special_numbers_enabled,
            high_risk_tollfraud_numbers_enabled=high_risk_tollfraud_numbers_enabled,
            page_size=limits["page_size"],
        )

        return self._version.stream_async(page, limits["limit"])

    def list(
        self,
        iso_code: Union[str, object] = values.unset,
        continent: Union[str, object] = values.unset,
        country_code: Union[str, object] = values.unset,
        low_risk_numbers_enabled: Union[bool, object] = values.unset,
        high_risk_special_numbers_enabled: Union[bool, object] = values.unset,
        high_risk_tollfraud_numbers_enabled: Union[bool, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> List[CountryInstance]:
        """
        Lists CountryInstance records from the API as a list.
        Unlike stream(), this operation is eager and will load `limit` records into
        memory before returning.

        :param str iso_code: Filter to retrieve the country permissions by specifying the [ISO country code](https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2)
        :param str continent: Filter to retrieve the country permissions by specifying the continent
        :param str country_code: Filter the results by specified [country codes](https://www.itu.int/itudoc/itu-t/ob-lists/icc/e164_763.html)
        :param bool low_risk_numbers_enabled: Filter to retrieve the country permissions with dialing to low-risk numbers enabled. Can be: `true` or `false`.
        :param bool high_risk_special_numbers_enabled: Filter to retrieve the country permissions with dialing to high-risk special service numbers enabled. Can be: `true` or `false`
        :param bool high_risk_tollfraud_numbers_enabled: Filter to retrieve the country permissions with dialing to high-risk [toll fraud](https://www.twilio.com/blog/how-to-protect-your-account-from-toll-fraud-with-voice-dialing-geo-permissions-html) numbers enabled. Can be: `true` or `false`.
        :param limit: Upper limit for the number of records to return. list() guarantees
                      never to return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, list() will attempt to read the limit
                          with the most efficient page size, i.e. min(limit, 1000)

        :returns: list that will contain up to limit results
        """
        return list(
            self.stream(
                iso_code=iso_code,
                continent=continent,
                country_code=country_code,
                low_risk_numbers_enabled=low_risk_numbers_enabled,
                high_risk_special_numbers_enabled=high_risk_special_numbers_enabled,
                high_risk_tollfraud_numbers_enabled=high_risk_tollfraud_numbers_enabled,
                limit=limit,
                page_size=page_size,
            )
        )

    async def list_async(
        self,
        iso_code: Union[str, object] = values.unset,
        continent: Union[str, object] = values.unset,
        country_code: Union[str, object] = values.unset,
        low_risk_numbers_enabled: Union[bool, object] = values.unset,
        high_risk_special_numbers_enabled: Union[bool, object] = values.unset,
        high_risk_tollfraud_numbers_enabled: Union[bool, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> List[CountryInstance]:
        """
        Asynchronously lists CountryInstance records from the API as a list.
        Unlike stream(), this operation is eager and will load `limit` records into
        memory before returning.

        :param str iso_code: Filter to retrieve the country permissions by specifying the [ISO country code](https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2)
        :param str continent: Filter to retrieve the country permissions by specifying the continent
        :param str country_code: Filter the results by specified [country codes](https://www.itu.int/itudoc/itu-t/ob-lists/icc/e164_763.html)
        :param bool low_risk_numbers_enabled: Filter to retrieve the country permissions with dialing to low-risk numbers enabled. Can be: `true` or `false`.
        :param bool high_risk_special_numbers_enabled: Filter to retrieve the country permissions with dialing to high-risk special service numbers enabled. Can be: `true` or `false`
        :param bool high_risk_tollfraud_numbers_enabled: Filter to retrieve the country permissions with dialing to high-risk [toll fraud](https://www.twilio.com/blog/how-to-protect-your-account-from-toll-fraud-with-voice-dialing-geo-permissions-html) numbers enabled. Can be: `true` or `false`.
        :param limit: Upper limit for the number of records to return. list() guarantees
                      never to return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, list() will attempt to read the limit
                          with the most efficient page size, i.e. min(limit, 1000)

        :returns: list that will contain up to limit results
        """
        return [
            record
            async for record in await self.stream_async(
                iso_code=iso_code,
                continent=continent,
                country_code=country_code,
                low_risk_numbers_enabled=low_risk_numbers_enabled,
                high_risk_special_numbers_enabled=high_risk_special_numbers_enabled,
                high_risk_tollfraud_numbers_enabled=high_risk_tollfraud_numbers_enabled,
                limit=limit,
                page_size=page_size,
            )
        ]

    def page(
        self,
        iso_code: Union[str, object] = values.unset,
        continent: Union[str, object] = values.unset,
        country_code: Union[str, object] = values.unset,
        low_risk_numbers_enabled: Union[bool, object] = values.unset,
        high_risk_special_numbers_enabled: Union[bool, object] = values.unset,
        high_risk_tollfraud_numbers_enabled: Union[bool, object] = values.unset,
        page_token: Union[str, object] = values.unset,
        page_number: Union[int, object] = values.unset,
        page_size: Union[int, object] = values.unset,
    ) -> CountryPage:
        """
        Retrieve a single page of CountryInstance records from the API.
        Request is executed immediately

        :param iso_code: Filter to retrieve the country permissions by specifying the [ISO country code](https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2)
        :param continent: Filter to retrieve the country permissions by specifying the continent
        :param country_code: Filter the results by specified [country codes](https://www.itu.int/itudoc/itu-t/ob-lists/icc/e164_763.html)
        :param low_risk_numbers_enabled: Filter to retrieve the country permissions with dialing to low-risk numbers enabled. Can be: `true` or `false`.
        :param high_risk_special_numbers_enabled: Filter to retrieve the country permissions with dialing to high-risk special service numbers enabled. Can be: `true` or `false`
        :param high_risk_tollfraud_numbers_enabled: Filter to retrieve the country permissions with dialing to high-risk [toll fraud](https://www.twilio.com/blog/how-to-protect-your-account-from-toll-fraud-with-voice-dialing-geo-permissions-html) numbers enabled. Can be: `true` or `false`.
        :param page_token: PageToken provided by the API
        :param page_number: Page Number, this value is simply for client state
        :param page_size: Number of records to return, defaults to 50

        :returns: Page of CountryInstance
        """
        data = values.of(
            {
                "IsoCode": iso_code,
                "Continent": continent,
                "CountryCode": country_code,
                "LowRiskNumbersEnabled": low_risk_numbers_enabled,
                "HighRiskSpecialNumbersEnabled": high_risk_special_numbers_enabled,
                "HighRiskTollfraudNumbersEnabled": high_risk_tollfraud_numbers_enabled,
                "PageToken": page_token,
                "Page": page_number,
                "PageSize": page_size,
            }
        )

        response = self._version.page(method="GET", uri=self._uri, params=data)
        return CountryPage(self._version, response)

    async def page_async(
        self,
        iso_code: Union[str, object] = values.unset,
        continent: Union[str, object] = values.unset,
        country_code: Union[str, object] = values.unset,
        low_risk_numbers_enabled: Union[bool, object] = values.unset,
        high_risk_special_numbers_enabled: Union[bool, object] = values.unset,
        high_risk_tollfraud_numbers_enabled: Union[bool, object] = values.unset,
        page_token: Union[str, object] = values.unset,
        page_number: Union[int, object] = values.unset,
        page_size: Union[int, object] = values.unset,
    ) -> CountryPage:
        """
        Asynchronously retrieve a single page of CountryInstance records from the API.
        Request is executed immediately

        :param iso_code: Filter to retrieve the country permissions by specifying the [ISO country code](https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2)
        :param continent: Filter to retrieve the country permissions by specifying the continent
        :param country_code: Filter the results by specified [country codes](https://www.itu.int/itudoc/itu-t/ob-lists/icc/e164_763.html)
        :param low_risk_numbers_enabled: Filter to retrieve the country permissions with dialing to low-risk numbers enabled. Can be: `true` or `false`.
        :param high_risk_special_numbers_enabled: Filter to retrieve the country permissions with dialing to high-risk special service numbers enabled. Can be: `true` or `false`
        :param high_risk_tollfraud_numbers_enabled: Filter to retrieve the country permissions with dialing to high-risk [toll fraud](https://www.twilio.com/blog/how-to-protect-your-account-from-toll-fraud-with-voice-dialing-geo-permissions-html) numbers enabled. Can be: `true` or `false`.
        :param page_token: PageToken provided by the API
        :param page_number: Page Number, this value is simply for client state
        :param page_size: Number of records to return, defaults to 50

        :returns: Page of CountryInstance
        """
        data = values.of(
            {
                "IsoCode": iso_code,
                "Continent": continent,
                "CountryCode": country_code,
                "LowRiskNumbersEnabled": low_risk_numbers_enabled,
                "HighRiskSpecialNumbersEnabled": high_risk_special_numbers_enabled,
                "HighRiskTollfraudNumbersEnabled": high_risk_tollfraud_numbers_enabled,
                "PageToken": page_token,
                "Page": page_number,
                "PageSize": page_size,
            }
        )

        response = await self._version.page_async(
            method="GET", uri=self._uri, params=data
        )
        return CountryPage(self._version, response)

    def get_page(self, target_url: str) -> CountryPage:
        """
        Retrieve a specific page of CountryInstance records from the API.
        Request is executed immediately

        :param target_url: API-generated URL for the requested results page

        :returns: Page of CountryInstance
        """
        response = self._version.domain.twilio.request("GET", target_url)
        return CountryPage(self._version, response)

    async def get_page_async(self, target_url: str) -> CountryPage:
        """
        Asynchronously retrieve a specific page of CountryInstance records from the API.
        Request is executed immediately

        :param target_url: API-generated URL for the requested results page

        :returns: Page of CountryInstance
        """
        response = await self._version.domain.twilio.request_async("GET", target_url)
        return CountryPage(self._version, response)

    def get(self, iso_code: str) -> CountryContext:
        """
        Constructs a CountryContext

        :param iso_code: The [ISO country code](https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2) of the DialingPermissions Country resource to fetch
        """
        return CountryContext(self._version, iso_code=iso_code)

    def __call__(self, iso_code: str) -> CountryContext:
        """
        Constructs a CountryContext

        :param iso_code: The [ISO country code](https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2) of the DialingPermissions Country resource to fetch
        """
        return CountryContext(self._version, iso_code=iso_code)

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Voice.V1.CountryList>"
