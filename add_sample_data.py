#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Dashboard için sample data ekleme scripti
"""

import os
import sys
from datetime import datetime, timedelta
import random

# Flask app'i import et
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from app import create_app, db
from app.models.user import User
from app.models.company import Company
from app.models.training import Training, Enrollment
from app.models.quiz import Quiz, QuizAttempt

def create_sample_data():
    """Dashboard için sample data oluştur"""

    print("🚀 Sample data oluşturuluyor...")

    # Admin kullanıcısını bul
    admin_user = User.query.filter_by(rol='admin').first()
    if not admin_user:
        print("❌ Admin kullanıcısı bulunamadı! Önce admin kullanıcısı oluşturun.")
        return

    # 1. Şirketler ekle
    companies = [
        {'ad': 'BAYRAKTAR Teknoloji', 'kod': 'BYKT', 'aciklama': 'Savunma sanayi teknoloji şirketi'},
        {'ad': 'Teknoloji A.Ş.', 'kod': 'TEKN', 'aciklama': 'Yazılım geliştirme şirketi'},
        {'ad': 'Endüstri Ltd.', 'kod': 'ENDT', 'aciklama': 'İmalat sanayi şirketi'},
        {'ad': 'Güvenlik Corp.', 'kod': 'GUVN', 'aciklama': 'Güvenlik hizmetleri şirketi'},
        {'ad': 'İnşaat Holding', 'kod': 'INST', 'aciklama': 'İnşaat ve yapı şirketi'},
    ]
    
    company_objects = []
    for comp_data in companies:
        company = Company.query.filter_by(ad=comp_data['ad']).first()
        if not company:
            company = Company(**comp_data, olusturan_id=admin_user.id)
            db.session.add(company)
            print(f"✅ Şirket eklendi: {comp_data['ad']}")
        company_objects.append(company)
    
    db.session.commit()
    
    # 2. Kullanıcılar ekle
    users_data = [
        {'ad': 'Ahmet', 'soyad': 'Yılmaz', 'email': '<EMAIL>', 'rol': 'participant'},
        {'ad': 'Mehmet', 'soyad': 'Kaya', 'email': '<EMAIL>', 'rol': 'participant'},
        {'ad': 'Ayşe', 'soyad': 'Demir', 'email': '<EMAIL>', 'rol': 'participant'},
        {'ad': 'Fatma', 'soyad': 'Şahin', 'email': '<EMAIL>', 'rol': 'participant'},
        {'ad': 'Ali', 'soyad': 'Özkan', 'email': '<EMAIL>', 'rol': 'participant'},
        {'ad': 'Zeynep', 'soyad': 'Arslan', 'email': '<EMAIL>', 'rol': 'participant'},
        {'ad': 'Mustafa', 'soyad': 'Çelik', 'email': '<EMAIL>', 'rol': 'participant'},
        {'ad': 'Elif', 'soyad': 'Koç', 'email': '<EMAIL>', 'rol': 'participant'},
        {'ad': 'Hasan', 'soyad': 'Aydın', 'email': '<EMAIL>', 'rol': 'participant'},
        {'ad': 'Seda', 'soyad': 'Polat', 'email': '<EMAIL>', 'rol': 'participant'},
    ]
    
    user_objects = []
    for i, user_data in enumerate(users_data):
        user = User.query.filter_by(email=user_data['email']).first()
        if not user:
            user = User(
                ad=user_data['ad'],
                soyad=user_data['soyad'],
                email=user_data['email'],
                telefon_numarasi=f'555{random.randint(1000000, 9999999)}',
                rol=user_data['rol'],
                company_id=company_objects[i % len(company_objects)].id,
                aktif_mi=True
            )
            user.set_password('123456')  # Basit şifre
            db.session.add(user)
            print(f"✅ Kullanıcı eklendi: {user_data['ad']} {user_data['soyad']}")
        user_objects.append(user)
    
    db.session.commit()
    
    # 3. Eğitimler ekle
    trainings_data = [
        {
            'ad': 'İSG Temel Eğitimi',
            'aciklama': 'İş sağlığı ve güvenliği temel bilgileri',
            'baslangic_tarihi': datetime.utcnow() - timedelta(days=30),
            'bitis_tarihi': datetime.utcnow() - timedelta(days=29),
        },
        {
            'ad': 'İSG İleri Düzey',
            'aciklama': 'İş sağlığı ve güvenliği ileri seviye',
            'baslangic_tarihi': datetime.utcnow() - timedelta(days=20),
            'bitis_tarihi': datetime.utcnow() - timedelta(days=19),
        },
        {
            'ad': 'Yangın Güvenliği',
            'aciklama': 'Yangın önleme ve müdahale teknikleri',
            'baslangic_tarihi': datetime.utcnow() - timedelta(days=15),
            'bitis_tarihi': datetime.utcnow() - timedelta(days=14),
        },
        {
            'ad': 'İlk Yardım Eğitimi',
            'aciklama': 'Temel ilk yardım bilgileri',
            'baslangic_tarihi': datetime.utcnow() - timedelta(days=10),
            'bitis_tarihi': datetime.utcnow() - timedelta(days=9),
        },
        {
            'ad': 'Çevre Güvenliği',
            'aciklama': 'Çevresel risk yönetimi',
            'baslangic_tarihi': datetime.utcnow() + timedelta(days=5),
            'bitis_tarihi': datetime.utcnow() + timedelta(days=6),
        },
    ]
    
    training_objects = []
    for training_data in trainings_data:
        training = Training.query.filter_by(ad=training_data['ad']).first()
        if not training:
            training = Training(
                **training_data,
                egitmen_adi='Uzman Eğitmen',
                egitmen_telefon='5551234567',
                aktif_mi=True,
                olusturan_id=admin_user.id,
                olusturulma_tarihi=training_data['baslangic_tarihi'] - timedelta(days=random.randint(1, 10))
            )
            db.session.add(training)
            print(f"✅ Eğitim eklendi: {training_data['ad']}")
        training_objects.append(training)
    
    db.session.commit()
    
    # 4. Katılımcıları eğitimlere kaydet
    statuses = ['teyit_edildi', 'beklemede', 'reddedildi']
    weights = [0.7, 0.2, 0.1]  # %70 teyit edildi, %20 beklemede, %10 reddedildi
    
    for training in training_objects:
        # Her eğitime rastgele katılımcı ekle
        participant_count = random.randint(5, 8)
        selected_users = random.sample(user_objects, participant_count)
        
        for user in selected_users:
            enrollment = Enrollment.query.filter_by(training_id=training.id, user_id=user.id).first()
            if not enrollment:
                status = random.choices(statuses, weights=weights)[0]
                enrollment = Enrollment(
                    training_id=training.id,
                    user_id=user.id,
                    katilim_teyidi_durumu=status,
                    kayit_tarihi=training.olusturulma_tarihi + timedelta(days=random.randint(0, 3)),
                    aktif_mi=True
                )
                db.session.add(enrollment)
    
    db.session.commit()
    print("✅ Katılımcı kayıtları eklendi")
    
    # 5. Quizler ekle
    for training in training_objects:
        quiz = Quiz.query.filter_by(training_id=training.id).first()
        if not quiz:
            quiz = Quiz(
                ad=f"{training.ad} Quiz",
                aciklama=f"{training.ad} için değerlendirme quizi",
                training_id=training.id,
                gecer_notu=random.randint(60, 80),
                questions_json='[]',  # Boş sorular listesi
                olusturan_id=admin_user.id,
                aktif_mi=True
            )
            db.session.add(quiz)
    
    db.session.commit()
    print("✅ Quizler eklendi")
    
    # 6. Quiz denemelerini ekle
    quizzes = Quiz.query.all()
    for quiz in quizzes:
        # Bu quiz'in eğitimine kayıtlı katılımcıları al
        enrollments = Enrollment.query.filter_by(training_id=quiz.training_id, aktif_mi=True).all()
        
        for enrollment in enrollments:
            # %80 ihtimalle quiz çözülmüş olsun
            if random.random() < 0.8:
                attempt = QuizAttempt.query.filter_by(quiz_id=quiz.id, user_id=enrollment.user_id).first()
                if not attempt:
                    score = random.randint(40, 100)
                    attempt = QuizAttempt(
                        quiz_id=quiz.id,
                        user_id=enrollment.user_id,
                        skor=score,
                        gecme_durumu=score >= quiz.gecer_notu,
                        tamamlandi_mi=True,
                        deneme_tarihi=enrollment.kayit_tarihi + timedelta(days=random.randint(1, 5))
                    )
                    db.session.add(attempt)
    
    db.session.commit()
    print("✅ Quiz denemeleri eklendi")
    
    print("🎉 Sample data başarıyla oluşturuldu!")
    print("\n📊 Oluşturulan veriler:")
    print(f"   • {len(companies)} şirket")
    print(f"   • {len(users_data)} kullanıcı")
    print(f"   • {len(trainings_data)} eğitim")
    print(f"   • ~{sum(random.randint(5, 8) for _ in training_objects)} katılım kaydı")
    print(f"   • {len(trainings_data)} quiz")
    print(f"   • Çok sayıda quiz denemesi")

if __name__ == '__main__':
    app = create_app()
    with app.app_context():
        create_sample_data()
