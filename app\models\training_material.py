from datetime import datetime
from app import db
import os
import uuid

class TrainingMaterial(db.Model):
    __tablename__ = 'training_materials'
    
    id = db.Column(db.Integer, primary_key=True)
    training_id = db.Column(db.<PERSON><PERSON><PERSON>, db.<PERSON><PERSON>('trainings.id'), nullable=False)
    uploaded_by_id = db.Column(db.Integer, db.Foreign<PERSON>ey('users.id'), nullable=False)
    
    # Dosya bilgileri
    file_name = db.Column(db.String(255), nullable=False)  # Orijinal dosya adı
    file_path = db.Column(db.String(500), nullable=False)  # Sunucudaki dosya yolu
    file_size = db.Column(db.BigInteger, nullable=False)   # Dosya boyutu (bytes)
    file_type = db.Column(db.Enum('presentation', 'pdf', 'document', 'spreadsheet', 'video', 'image', 'other', 
                                 name='material_types'), nullable=False)
    mime_type = db.Column(db.String(100), nullable=False)
    
    # Metadata
    description = db.Column(db.Text)
    upload_date = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    is_active = db.Column(db.Boolean, default=True, nullable=False)
    
    # İstatistikler
    download_count = db.Column(db.Integer, default=0, nullable=False)
    view_count = db.Column(db.Integer, default=0, nullable=False)
    
    # İlişkiler
    training = db.relationship('Training', backref='materials')
    uploaded_by = db.relationship('User', backref='uploaded_materials')
    
    def __init__(self, **kwargs):
        super(TrainingMaterial, self).__init__(**kwargs)
    
    @property
    def file_extension(self):
        """Dosya uzantısını döndür"""
        return os.path.splitext(self.file_name)[1].lower()
    
    @property
    def file_size_formatted(self):
        """Dosya boyutunu okunabilir formatta döndür"""
        size = self.file_size
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size < 1024.0:
                return f"{size:.1f} {unit}"
            size /= 1024.0
        return f"{size:.1f} TB"
    
    @property
    def file_icon(self):
        """Dosya tipine göre ikon döndür"""
        icons = {
            'presentation': 'fas fa-file-powerpoint',
            'pdf': 'fas fa-file-pdf',
            'document': 'fas fa-file-word',
            'spreadsheet': 'fas fa-file-excel',
            'video': 'fas fa-file-video',
            'image': 'fas fa-file-image',
            'other': 'fas fa-file'
        }
        return icons.get(self.file_type, 'fas fa-file')
    
    @property
    def file_color(self):
        """Dosya tipine göre renk döndür"""
        colors = {
            'presentation': 'text-warning',
            'pdf': 'text-danger',
            'document': 'text-primary',
            'spreadsheet': 'text-success',
            'video': 'text-info',
            'image': 'text-secondary',
            'other': 'text-muted'
        }
        return colors.get(self.file_type, 'text-muted')
    
    @property
    def is_viewable_online(self):
        """Online görüntülenebilir mi?"""
        viewable_types = ['pdf', 'image']
        viewable_extensions = ['.txt', '.md']
        return (self.file_type in viewable_types or 
                self.file_extension in viewable_extensions)
    
    @property
    def is_video(self):
        """Video dosyası mı?"""
        return self.file_type == 'video'
    
    @property
    def is_image(self):
        """Resim dosyası mı?"""
        return self.file_type == 'image'
    
    @staticmethod
    def get_file_type_from_extension(filename):
        """Dosya uzantısından dosya tipini belirle"""
        ext = os.path.splitext(filename)[1].lower()
        
        type_mapping = {
            # Sunumlar
            '.ppt': 'presentation',
            '.pptx': 'presentation',
            '.odp': 'presentation',
            
            # PDF
            '.pdf': 'pdf',
            
            # Dökümanlar
            '.doc': 'document',
            '.docx': 'document',
            '.odt': 'document',
            '.txt': 'document',
            '.rtf': 'document',
            
            # Tablolar
            '.xls': 'spreadsheet',
            '.xlsx': 'spreadsheet',
            '.ods': 'spreadsheet',
            '.csv': 'spreadsheet',
            
            # Videolar
            '.mp4': 'video',
            '.avi': 'video',
            '.mov': 'video',
            '.wmv': 'video',
            '.webm': 'video',
            '.mkv': 'video',
            '.flv': 'video',
            
            # Resimler
            '.jpg': 'image',
            '.jpeg': 'image',
            '.png': 'image',
            '.gif': 'image',
            '.bmp': 'image',
            '.svg': 'image',
            '.webp': 'image'
        }
        
        return type_mapping.get(ext, 'other')
    
    @staticmethod
    def is_allowed_file(filename):
        """Dosya yüklenebilir mi?"""
        allowed_extensions = {
            '.ppt', '.pptx', '.odp',  # Sunumlar
            '.pdf',  # PDF
            '.doc', '.docx', '.odt', '.txt', '.rtf',  # Dökümanlar
            '.xls', '.xlsx', '.ods', '.csv',  # Tablolar
            '.mp4', '.avi', '.mov', '.wmv', '.webm', '.mkv', '.flv',  # Videolar
            '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.svg', '.webp'  # Resimler
        }
        
        ext = os.path.splitext(filename)[1].lower()
        return ext in allowed_extensions
    
    @staticmethod
    def get_max_file_size(file_type):
        """Dosya tipine göre maksimum boyut (bytes)"""
        max_sizes = {
            'video': 500 * 1024 * 1024,  # 500MB
            'presentation': 50 * 1024 * 1024,  # 50MB
            'pdf': 50 * 1024 * 1024,  # 50MB
            'document': 25 * 1024 * 1024,  # 25MB
            'spreadsheet': 25 * 1024 * 1024,  # 25MB
            'image': 10 * 1024 * 1024,  # 10MB
            'other': 25 * 1024 * 1024  # 25MB
        }
        return max_sizes.get(file_type, 25 * 1024 * 1024)
    
    def increment_download_count(self):
        """İndirme sayısını artır"""
        self.download_count += 1
        db.session.commit()
    
    def increment_view_count(self):
        """Görüntülenme sayısını artır"""
        self.view_count += 1
        db.session.commit()
    
    @staticmethod
    def generate_secure_filename(original_filename):
        """Güvenli dosya adı oluştur"""
        # UUID + timestamp + orijinal uzantı
        ext = os.path.splitext(original_filename)[1].lower()
        secure_name = f"{uuid.uuid4().hex}_{int(datetime.utcnow().timestamp())}{ext}"
        return secure_name
    
    def to_dict(self):
        """Dictionary'ye çevir"""
        return {
            'id': self.id,
            'training_id': self.training_id,
            'file_name': self.file_name,
            'file_size': self.file_size,
            'file_size_formatted': self.file_size_formatted,
            'file_type': self.file_type,
            'mime_type': self.mime_type,
            'description': self.description,
            'upload_date': self.upload_date.strftime('%d.%m.%Y %H:%M'),
            'uploaded_by': self.uploaded_by.tam_ad,
            'download_count': self.download_count,
            'view_count': self.view_count,
            'file_icon': self.file_icon,
            'file_color': self.file_color,
            'is_viewable_online': self.is_viewable_online,
            'is_video': self.is_video,
            'is_image': self.is_image
        }
    
    def __repr__(self):
        return f'<TrainingMaterial {self.file_name}>'
