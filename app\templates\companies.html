{% extends "base.html" %}

{% block title %}Şirket Yönetimi{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-building"></i> Şirket Yönetimi
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('company.create_company') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> Yeni Şirket
            </a>
        </div>
    </div>
</div>

<!-- Filtreleme -->
<div class="card shadow mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-4">
                <label for="search" class="form-label">Arama</label>
                <input type="text" class="form-control" id="search" name="search" 
                       value="{{ search }}" placeholder="Şirket adı, kodu veya açıklama...">
            </div>
            <div class="col-md-3">
                <label for="status" class="form-label">Durum</label>
                <select class="form-select" id="status" name="status">
                    <option value="">Tümü</option>
                    <option value="active" {{ 'selected' if status == 'active' else '' }}>Aktif</option>
                    <option value="inactive" {{ 'selected' if status == 'inactive' else '' }}>Pasif</option>
                </select>
            </div>
            <div class="col-md-5 d-flex align-items-end">
                <button type="submit" class="btn btn-outline-primary me-2">
                    <i class="fas fa-search"></i> Filtrele
                </button>
                <a href="{{ url_for('company.companies') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-times"></i> Temizle
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Şirket Listesi -->
<div class="card shadow">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-list"></i> Şirketler 
            <span class="badge bg-primary">{{ companies.total }}</span>
        </h5>
    </div>
    <div class="card-body">
        {% if companies.items %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead class="table-light">
                    <tr>
                        <th>Kod</th>
                        <th>Şirket Adı</th>
                        <th>İletişim</th>
                        <th>Kullanıcılar</th>
                        <th>Eğitimler</th>
                        <th>Durum</th>
                        <th>Oluşturulma</th>
                        <th>İşlemler</th>
                    </tr>
                </thead>
                <tbody>
                    {% for company in companies.items %}
                    <tr>
                        <td>
                            <strong class="text-primary">{{ company.kod }}</strong>
                        </td>
                        <td>
                            <div>
                                <strong>{{ company.ad }}</strong>
                                {% if company.aciklama %}
                                <br><small class="text-muted">{{ company.aciklama[:50] }}{% if company.aciklama|length > 50 %}...{% endif %}</small>
                                {% endif %}
                            </div>
                        </td>
                        <td>
                            {% if company.telefon %}
                            <div><i class="fas fa-phone"></i> {{ company.telefon }}</div>
                            {% endif %}
                            {% if company.email %}
                            <div><i class="fas fa-envelope"></i> {{ company.email }}</div>
                            {% endif %}
                            {% if company.website %}
                            <div><i class="fas fa-globe"></i> <a href="{{ company.website }}" target="_blank">{{ company.website }}</a></div>
                            {% endif %}
                        </td>
                        <td>
                            <div class="d-flex justify-content-between">
                                <span class="badge bg-info">{{ company.kullanici_sayisi }}</span>
                                <small class="text-muted">
                                    M:{{ company.manager_sayisi }} / K:{{ company.participant_sayisi }}
                                </small>
                            </div>
                        </td>
                        <td>
                            <span class="badge bg-success">{{ company.egitim_sayisi }}</span>
                        </td>
                        <td>
                            <span class="badge {{ 'bg-success' if company.aktif_mi else 'bg-secondary' }}">
                                {{ 'Aktif' if company.aktif_mi else 'Pasif' }}
                            </span>
                        </td>
                        <td>
                            <small class="text-muted">
                                {{ company.olusturulma_tarihi.strftime('%d.%m.%Y') if company.olusturulma_tarihi else '-' }}
                            </small>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                <!-- Detay -->
                                <a href="{{ url_for('company.company_detail', company_id=company.id) }}" 
                                   class="btn btn-outline-info" title="Şirket Detayları">
                                    <i class="fas fa-eye"></i>
                                </a>
                                
                                <!-- Düzenle -->
                                <a href="{{ url_for('company.edit_company', company_id=company.id) }}" 
                                   class="btn btn-outline-primary" title="Şirketi Düzenle">
                                    <i class="fas fa-edit"></i>
                                </a>
                                
                                <!-- Aktif/Pasif -->
                                <button type="button" class="btn btn-outline-{{ 'warning' if company.aktif_mi else 'success' }}" 
                                        onclick="toggleCompanyStatus({{ company.id }}, '{{ company.tam_ad }}', {{ company.aktif_mi|lower }})"
                                        title="{{ 'Pasifleştir' if company.aktif_mi else 'Aktifleştir' }}">
                                    <i class="fas fa-{{ 'ban' if company.aktif_mi else 'check' }}"></i>
                                </button>
                                
                                <!-- Sil -->
                                {% if company.can_be_deleted %}
                                <button type="button" class="btn btn-outline-danger" 
                                        onclick="deleteCompany({{ company.id }}, '{{ company.tam_ad }}')"
                                        title="Şirketi Sil">
                                    <i class="fas fa-trash"></i>
                                </button>
                                {% else %}
                                <button type="button" class="btn btn-outline-secondary" 
                                        title="Kullanıcıları olan şirket silinemez" disabled>
                                    <i class="fas fa-trash"></i>
                                </button>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        {% if companies.pages > 1 %}
        <nav aria-label="Şirket listesi sayfalama" class="mt-4">
            <ul class="pagination justify-content-center">
                {% if companies.has_prev %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('company.companies', page=companies.prev_num, 
                        search=search, status=status) }}">Önceki</a>
                </li>
                {% endif %}
                
                {% for page_num in companies.iter_pages() %}
                    {% if page_num %}
                        {% if page_num != companies.page %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('company.companies', page=page_num, 
                                search=search, status=status) }}">{{ page_num }}</a>
                        </li>
                        {% else %}
                        <li class="page-item active">
                            <span class="page-link">{{ page_num }}</span>
                        </li>
                        {% endif %}
                    {% else %}
                    <li class="page-item disabled">
                        <span class="page-link">…</span>
                    </li>
                    {% endif %}
                {% endfor %}
                
                {% if companies.has_next %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('company.companies', page=companies.next_num, 
                        search=search, status=status) }}">Sonraki</a>
                </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}
        
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-building fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">Şirket bulunamadı</h5>
            <p class="text-muted">
                {% if search or status %}
                Arama kriterlerinize uygun şirket bulunamadı.
                {% else %}
                Henüz hiç şirket eklenmemiş.
                {% endif %}
            </p>
            <a href="{{ url_for('company.create_company') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> İlk Şirketi Ekle
            </a>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function toggleCompanyStatus(companyId, companyName, isActive) {
    const action = isActive ? 'pasifleştirmek' : 'aktifleştirmek';
    
    if (confirm(`${companyName} şirketini ${action} istediğinizden emin misiniz?`)) {
        fetch(`/company/${companyId}/toggle-status`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('✅ ' + data.message);
                location.reload();
            } else {
                alert('❌ Hata: ' + data.message);
            }
        })
        .catch(error => {
            alert('❌ Bir hata oluştu: ' + error);
        });
    }
}

function deleteCompany(companyId, companyName) {
    if (confirm(`${companyName} şirketini silmek istediğinizden emin misiniz?\n\nBu işlem geri alınamaz!`)) {
        fetch(`/company/${companyId}/delete`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('✅ ' + data.message);
                location.reload();
            } else {
                alert('❌ Hata: ' + data.message);
            }
        })
        .catch(error => {
            alert('❌ Bir hata oluştu: ' + error);
        });
    }
}
</script>
{% endblock %}
