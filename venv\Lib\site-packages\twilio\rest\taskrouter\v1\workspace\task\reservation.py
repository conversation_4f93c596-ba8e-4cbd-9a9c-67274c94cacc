r"""
    This code was generated by
   ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
    |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
    |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

    Twilio - Taskrouter
    This is the public Twilio REST API.

    NOTE: This class is auto generated by OpenAPI Generator.
    https://openapi-generator.tech
    Do not edit the class manually.
"""


from datetime import datetime
from typing import Any, Dict, List, Optional, Union, Iterator, AsyncIterator
from twilio.base import deserialize, serialize, values
from twilio.base.instance_context import InstanceContext
from twilio.base.instance_resource import InstanceResource
from twilio.base.list_resource import ListResource
from twilio.base.version import Version
from twilio.base.page import Page


class ReservationInstance(InstanceResource):
    class CallStatus(object):
        INITIATED = "initiated"
        RINGING = "ringing"
        ANSWERED = "answered"
        COMPLETED = "completed"

    class ConferenceEvent(object):
        START = "start"
        END = "end"
        JOIN = "join"
        LEAVE = "leave"
        MUTE = "mute"
        HOLD = "hold"
        SPEAKER = "speaker"

    class Status(object):
        PENDING = "pending"
        ACCEPTED = "accepted"
        REJECTED = "rejected"
        TIMEOUT = "timeout"
        CANCELED = "canceled"
        RESCINDED = "rescinded"
        WRAPPING = "wrapping"
        COMPLETED = "completed"

    class SupervisorMode(object):
        MONITOR = "monitor"
        WHISPER = "whisper"
        BARGE = "barge"

    """
    :ivar account_sid: The SID of the [Account](https://www.twilio.com/docs/iam/api/account) that created the TaskReservation resource.
    :ivar date_created: The date and time in GMT when the resource was created specified in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format.
    :ivar date_updated: The date and time in GMT when the resource was last updated specified in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format.
    :ivar reservation_status: 
    :ivar sid: The unique string that we created to identify the TaskReservation resource.
    :ivar task_sid: The SID of the reserved Task resource.
    :ivar worker_name: The `friendly_name` of the Worker that is reserved.
    :ivar worker_sid: The SID of the reserved Worker resource.
    :ivar workspace_sid: The SID of the Workspace that this task is contained within.
    :ivar url: The absolute URL of the TaskReservation reservation.
    :ivar links: The URLs of related resources.
    """

    def __init__(
        self,
        version: Version,
        payload: Dict[str, Any],
        workspace_sid: str,
        task_sid: str,
        sid: Optional[str] = None,
    ):
        super().__init__(version)

        self.account_sid: Optional[str] = payload.get("account_sid")
        self.date_created: Optional[datetime] = deserialize.iso8601_datetime(
            payload.get("date_created")
        )
        self.date_updated: Optional[datetime] = deserialize.iso8601_datetime(
            payload.get("date_updated")
        )
        self.reservation_status: Optional["ReservationInstance.Status"] = payload.get(
            "reservation_status"
        )
        self.sid: Optional[str] = payload.get("sid")
        self.task_sid: Optional[str] = payload.get("task_sid")
        self.worker_name: Optional[str] = payload.get("worker_name")
        self.worker_sid: Optional[str] = payload.get("worker_sid")
        self.workspace_sid: Optional[str] = payload.get("workspace_sid")
        self.url: Optional[str] = payload.get("url")
        self.links: Optional[Dict[str, object]] = payload.get("links")

        self._solution = {
            "workspace_sid": workspace_sid,
            "task_sid": task_sid,
            "sid": sid or self.sid,
        }
        self._context: Optional[ReservationContext] = None

    @property
    def _proxy(self) -> "ReservationContext":
        """
        Generate an instance context for the instance, the context is capable of
        performing various actions. All instance actions are proxied to the context

        :returns: ReservationContext for this ReservationInstance
        """
        if self._context is None:
            self._context = ReservationContext(
                self._version,
                workspace_sid=self._solution["workspace_sid"],
                task_sid=self._solution["task_sid"],
                sid=self._solution["sid"],
            )
        return self._context

    def fetch(self) -> "ReservationInstance":
        """
        Fetch the ReservationInstance


        :returns: The fetched ReservationInstance
        """
        return self._proxy.fetch()

    async def fetch_async(self) -> "ReservationInstance":
        """
        Asynchronous coroutine to fetch the ReservationInstance


        :returns: The fetched ReservationInstance
        """
        return await self._proxy.fetch_async()

    def update(
        self,
        if_match: Union[str, object] = values.unset,
        reservation_status: Union["ReservationInstance.Status", object] = values.unset,
        worker_activity_sid: Union[str, object] = values.unset,
        instruction: Union[str, object] = values.unset,
        dequeue_post_work_activity_sid: Union[str, object] = values.unset,
        dequeue_from: Union[str, object] = values.unset,
        dequeue_record: Union[str, object] = values.unset,
        dequeue_timeout: Union[int, object] = values.unset,
        dequeue_to: Union[str, object] = values.unset,
        dequeue_status_callback_url: Union[str, object] = values.unset,
        call_from: Union[str, object] = values.unset,
        call_record: Union[str, object] = values.unset,
        call_timeout: Union[int, object] = values.unset,
        call_to: Union[str, object] = values.unset,
        call_url: Union[str, object] = values.unset,
        call_status_callback_url: Union[str, object] = values.unset,
        call_accept: Union[bool, object] = values.unset,
        redirect_call_sid: Union[str, object] = values.unset,
        redirect_accept: Union[bool, object] = values.unset,
        redirect_url: Union[str, object] = values.unset,
        to: Union[str, object] = values.unset,
        from_: Union[str, object] = values.unset,
        status_callback: Union[str, object] = values.unset,
        status_callback_method: Union[str, object] = values.unset,
        status_callback_event: Union[
            List["ReservationInstance.CallStatus"], object
        ] = values.unset,
        timeout: Union[int, object] = values.unset,
        record: Union[bool, object] = values.unset,
        muted: Union[bool, object] = values.unset,
        beep: Union[str, object] = values.unset,
        start_conference_on_enter: Union[bool, object] = values.unset,
        end_conference_on_exit: Union[bool, object] = values.unset,
        wait_url: Union[str, object] = values.unset,
        wait_method: Union[str, object] = values.unset,
        early_media: Union[bool, object] = values.unset,
        max_participants: Union[int, object] = values.unset,
        conference_status_callback: Union[str, object] = values.unset,
        conference_status_callback_method: Union[str, object] = values.unset,
        conference_status_callback_event: Union[
            List["ReservationInstance.ConferenceEvent"], object
        ] = values.unset,
        conference_record: Union[str, object] = values.unset,
        conference_trim: Union[str, object] = values.unset,
        recording_channels: Union[str, object] = values.unset,
        recording_status_callback: Union[str, object] = values.unset,
        recording_status_callback_method: Union[str, object] = values.unset,
        conference_recording_status_callback: Union[str, object] = values.unset,
        conference_recording_status_callback_method: Union[str, object] = values.unset,
        region: Union[str, object] = values.unset,
        sip_auth_username: Union[str, object] = values.unset,
        sip_auth_password: Union[str, object] = values.unset,
        dequeue_status_callback_event: Union[List[str], object] = values.unset,
        post_work_activity_sid: Union[str, object] = values.unset,
        supervisor_mode: Union[
            "ReservationInstance.SupervisorMode", object
        ] = values.unset,
        supervisor: Union[str, object] = values.unset,
        end_conference_on_customer_exit: Union[bool, object] = values.unset,
        beep_on_customer_entrance: Union[bool, object] = values.unset,
    ) -> "ReservationInstance":
        """
        Update the ReservationInstance

        :param if_match: The If-Match HTTP request header
        :param reservation_status:
        :param worker_activity_sid: The new worker activity SID if rejecting a reservation.
        :param instruction: The assignment instruction for reservation.
        :param dequeue_post_work_activity_sid: The SID of the Activity resource to start after executing a Dequeue instruction.
        :param dequeue_from: The Caller ID of the call to the worker when executing a Dequeue instruction.
        :param dequeue_record: Whether to record both legs of a call when executing a Dequeue instruction or which leg to record.
        :param dequeue_timeout: Timeout for call when executing a Dequeue instruction.
        :param dequeue_to: The Contact URI of the worker when executing a Dequeue instruction. Can be the URI of the Twilio Client, the SIP URI for Programmable SIP, or the [E.164](https://www.twilio.com/docs/glossary/what-e164) formatted phone number, depending on the destination.
        :param dequeue_status_callback_url: The Callback URL for completed call event when executing a Dequeue instruction.
        :param call_from: The Caller ID of the outbound call when executing a Call instruction.
        :param call_record: Whether to record both legs of a call when executing a Call instruction or which leg to record.
        :param call_timeout: Timeout for call when executing a Call instruction.
        :param call_to: The Contact URI of the worker when executing a Call instruction.  Can be the URI of the Twilio Client, the SIP URI for Programmable SIP, or the [E.164](https://www.twilio.com/docs/glossary/what-e164) formatted phone number, depending on the destination.
        :param call_url: TwiML URI executed on answering the worker's leg as a result of the Call instruction.
        :param call_status_callback_url: The URL to call  for the completed call event when executing a Call instruction.
        :param call_accept: Whether to accept a reservation when executing a Call instruction.
        :param redirect_call_sid: The Call SID of the call parked in the queue when executing a Redirect instruction.
        :param redirect_accept: Whether the reservation should be accepted when executing a Redirect instruction.
        :param redirect_url: TwiML URI to redirect the call to when executing the Redirect instruction.
        :param to: The Contact URI of the worker when executing a Conference instruction. Can be the URI of the Twilio Client, the SIP URI for Programmable SIP, or the [E.164](https://www.twilio.com/docs/glossary/what-e164) formatted phone number, depending on the destination.
        :param from_: The Caller ID of the call to the worker when executing a Conference instruction.
        :param status_callback: The URL we should call using the `status_callback_method` to send status information to your application.
        :param status_callback_method: The HTTP method we should use to call `status_callback`. Can be: `POST` or `GET` and the default is `POST`.
        :param status_callback_event: The call progress events that we will send to `status_callback`. Can be: `initiated`, `ringing`, `answered`, or `completed`.
        :param timeout: Timeout for call when executing a Conference instruction.
        :param record: Whether to record the participant and their conferences, including the time between conferences. The default is `false`.
        :param muted: Whether the agent is muted in the conference. The default is `false`.
        :param beep: Whether to play a notification beep when the participant joins or when to play a beep. Can be: `true`, `false`, `onEnter`, or `onExit`. The default value is `true`.
        :param start_conference_on_enter: Whether to start the conference when the participant joins, if it has not already started. The default is `true`. If `false` and the conference has not started, the participant is muted and hears background music until another participant starts the conference.
        :param end_conference_on_exit: Whether to end the conference when the agent leaves.
        :param wait_url: The URL we should call using the `wait_method` for the music to play while participants are waiting for the conference to start. The default value is the URL of our standard hold music. [Learn more about hold music](https://www.twilio.com/labs/twimlets/holdmusic).
        :param wait_method: The HTTP method we should use to call `wait_url`. Can be `GET` or `POST` and the default is `POST`. When using a static audio file, this should be `GET` so that we can cache the file.
        :param early_media: Whether to allow an agent to hear the state of the outbound call, including ringing or disconnect messages. The default is `true`.
        :param max_participants: The maximum number of participants in the conference. Can be a positive integer from `2` to `250`. The default value is `250`.
        :param conference_status_callback: The URL we should call using the `conference_status_callback_method` when the conference events in `conference_status_callback_event` occur. Only the value set by the first participant to join the conference is used. Subsequent `conference_status_callback` values are ignored.
        :param conference_status_callback_method: The HTTP method we should use to call `conference_status_callback`. Can be: `GET` or `POST` and defaults to `POST`.
        :param conference_status_callback_event: The conference status events that we will send to `conference_status_callback`. Can be: `start`, `end`, `join`, `leave`, `mute`, `hold`, `speaker`.
        :param conference_record: Whether to record the conference the participant is joining or when to record the conference. Can be: `true`, `false`, `record-from-start`, and `do-not-record`. The default value is `false`.
        :param conference_trim: How to trim the leading and trailing silence from your recorded conference audio files. Can be: `trim-silence` or `do-not-trim` and defaults to `trim-silence`.
        :param recording_channels: The recording channels for the final recording. Can be: `mono` or `dual` and the default is `mono`.
        :param recording_status_callback: The URL that we should call using the `recording_status_callback_method` when the recording status changes.
        :param recording_status_callback_method: The HTTP method we should use when we call `recording_status_callback`. Can be: `GET` or `POST` and defaults to `POST`.
        :param conference_recording_status_callback: The URL we should call using the `conference_recording_status_callback_method` when the conference recording is available.
        :param conference_recording_status_callback_method: The HTTP method we should use to call `conference_recording_status_callback`. Can be: `GET` or `POST` and defaults to `POST`.
        :param region: The [region](https://support.twilio.com/hc/en-us/articles/*********-How-global-low-latency-routing-and-region-selection-work-for-conferences-and-Client-calls) where we should mix the recorded audio. Can be:`us1`, `ie1`, `de1`, `sg1`, `br1`, `au1`, or `jp1`.
        :param sip_auth_username: The SIP username used for authentication.
        :param sip_auth_password: The SIP password for authentication.
        :param dequeue_status_callback_event: The Call progress events sent via webhooks as a result of a Dequeue instruction.
        :param post_work_activity_sid: The new worker activity SID after executing a Conference instruction.
        :param supervisor_mode:
        :param supervisor: The Supervisor SID/URI when executing the Supervise instruction.
        :param end_conference_on_customer_exit: Whether to end the conference when the customer leaves.
        :param beep_on_customer_entrance: Whether to play a notification beep when the customer joins.

        :returns: The updated ReservationInstance
        """
        return self._proxy.update(
            if_match=if_match,
            reservation_status=reservation_status,
            worker_activity_sid=worker_activity_sid,
            instruction=instruction,
            dequeue_post_work_activity_sid=dequeue_post_work_activity_sid,
            dequeue_from=dequeue_from,
            dequeue_record=dequeue_record,
            dequeue_timeout=dequeue_timeout,
            dequeue_to=dequeue_to,
            dequeue_status_callback_url=dequeue_status_callback_url,
            call_from=call_from,
            call_record=call_record,
            call_timeout=call_timeout,
            call_to=call_to,
            call_url=call_url,
            call_status_callback_url=call_status_callback_url,
            call_accept=call_accept,
            redirect_call_sid=redirect_call_sid,
            redirect_accept=redirect_accept,
            redirect_url=redirect_url,
            to=to,
            from_=from_,
            status_callback=status_callback,
            status_callback_method=status_callback_method,
            status_callback_event=status_callback_event,
            timeout=timeout,
            record=record,
            muted=muted,
            beep=beep,
            start_conference_on_enter=start_conference_on_enter,
            end_conference_on_exit=end_conference_on_exit,
            wait_url=wait_url,
            wait_method=wait_method,
            early_media=early_media,
            max_participants=max_participants,
            conference_status_callback=conference_status_callback,
            conference_status_callback_method=conference_status_callback_method,
            conference_status_callback_event=conference_status_callback_event,
            conference_record=conference_record,
            conference_trim=conference_trim,
            recording_channels=recording_channels,
            recording_status_callback=recording_status_callback,
            recording_status_callback_method=recording_status_callback_method,
            conference_recording_status_callback=conference_recording_status_callback,
            conference_recording_status_callback_method=conference_recording_status_callback_method,
            region=region,
            sip_auth_username=sip_auth_username,
            sip_auth_password=sip_auth_password,
            dequeue_status_callback_event=dequeue_status_callback_event,
            post_work_activity_sid=post_work_activity_sid,
            supervisor_mode=supervisor_mode,
            supervisor=supervisor,
            end_conference_on_customer_exit=end_conference_on_customer_exit,
            beep_on_customer_entrance=beep_on_customer_entrance,
        )

    async def update_async(
        self,
        if_match: Union[str, object] = values.unset,
        reservation_status: Union["ReservationInstance.Status", object] = values.unset,
        worker_activity_sid: Union[str, object] = values.unset,
        instruction: Union[str, object] = values.unset,
        dequeue_post_work_activity_sid: Union[str, object] = values.unset,
        dequeue_from: Union[str, object] = values.unset,
        dequeue_record: Union[str, object] = values.unset,
        dequeue_timeout: Union[int, object] = values.unset,
        dequeue_to: Union[str, object] = values.unset,
        dequeue_status_callback_url: Union[str, object] = values.unset,
        call_from: Union[str, object] = values.unset,
        call_record: Union[str, object] = values.unset,
        call_timeout: Union[int, object] = values.unset,
        call_to: Union[str, object] = values.unset,
        call_url: Union[str, object] = values.unset,
        call_status_callback_url: Union[str, object] = values.unset,
        call_accept: Union[bool, object] = values.unset,
        redirect_call_sid: Union[str, object] = values.unset,
        redirect_accept: Union[bool, object] = values.unset,
        redirect_url: Union[str, object] = values.unset,
        to: Union[str, object] = values.unset,
        from_: Union[str, object] = values.unset,
        status_callback: Union[str, object] = values.unset,
        status_callback_method: Union[str, object] = values.unset,
        status_callback_event: Union[
            List["ReservationInstance.CallStatus"], object
        ] = values.unset,
        timeout: Union[int, object] = values.unset,
        record: Union[bool, object] = values.unset,
        muted: Union[bool, object] = values.unset,
        beep: Union[str, object] = values.unset,
        start_conference_on_enter: Union[bool, object] = values.unset,
        end_conference_on_exit: Union[bool, object] = values.unset,
        wait_url: Union[str, object] = values.unset,
        wait_method: Union[str, object] = values.unset,
        early_media: Union[bool, object] = values.unset,
        max_participants: Union[int, object] = values.unset,
        conference_status_callback: Union[str, object] = values.unset,
        conference_status_callback_method: Union[str, object] = values.unset,
        conference_status_callback_event: Union[
            List["ReservationInstance.ConferenceEvent"], object
        ] = values.unset,
        conference_record: Union[str, object] = values.unset,
        conference_trim: Union[str, object] = values.unset,
        recording_channels: Union[str, object] = values.unset,
        recording_status_callback: Union[str, object] = values.unset,
        recording_status_callback_method: Union[str, object] = values.unset,
        conference_recording_status_callback: Union[str, object] = values.unset,
        conference_recording_status_callback_method: Union[str, object] = values.unset,
        region: Union[str, object] = values.unset,
        sip_auth_username: Union[str, object] = values.unset,
        sip_auth_password: Union[str, object] = values.unset,
        dequeue_status_callback_event: Union[List[str], object] = values.unset,
        post_work_activity_sid: Union[str, object] = values.unset,
        supervisor_mode: Union[
            "ReservationInstance.SupervisorMode", object
        ] = values.unset,
        supervisor: Union[str, object] = values.unset,
        end_conference_on_customer_exit: Union[bool, object] = values.unset,
        beep_on_customer_entrance: Union[bool, object] = values.unset,
    ) -> "ReservationInstance":
        """
        Asynchronous coroutine to update the ReservationInstance

        :param if_match: The If-Match HTTP request header
        :param reservation_status:
        :param worker_activity_sid: The new worker activity SID if rejecting a reservation.
        :param instruction: The assignment instruction for reservation.
        :param dequeue_post_work_activity_sid: The SID of the Activity resource to start after executing a Dequeue instruction.
        :param dequeue_from: The Caller ID of the call to the worker when executing a Dequeue instruction.
        :param dequeue_record: Whether to record both legs of a call when executing a Dequeue instruction or which leg to record.
        :param dequeue_timeout: Timeout for call when executing a Dequeue instruction.
        :param dequeue_to: The Contact URI of the worker when executing a Dequeue instruction. Can be the URI of the Twilio Client, the SIP URI for Programmable SIP, or the [E.164](https://www.twilio.com/docs/glossary/what-e164) formatted phone number, depending on the destination.
        :param dequeue_status_callback_url: The Callback URL for completed call event when executing a Dequeue instruction.
        :param call_from: The Caller ID of the outbound call when executing a Call instruction.
        :param call_record: Whether to record both legs of a call when executing a Call instruction or which leg to record.
        :param call_timeout: Timeout for call when executing a Call instruction.
        :param call_to: The Contact URI of the worker when executing a Call instruction.  Can be the URI of the Twilio Client, the SIP URI for Programmable SIP, or the [E.164](https://www.twilio.com/docs/glossary/what-e164) formatted phone number, depending on the destination.
        :param call_url: TwiML URI executed on answering the worker's leg as a result of the Call instruction.
        :param call_status_callback_url: The URL to call  for the completed call event when executing a Call instruction.
        :param call_accept: Whether to accept a reservation when executing a Call instruction.
        :param redirect_call_sid: The Call SID of the call parked in the queue when executing a Redirect instruction.
        :param redirect_accept: Whether the reservation should be accepted when executing a Redirect instruction.
        :param redirect_url: TwiML URI to redirect the call to when executing the Redirect instruction.
        :param to: The Contact URI of the worker when executing a Conference instruction. Can be the URI of the Twilio Client, the SIP URI for Programmable SIP, or the [E.164](https://www.twilio.com/docs/glossary/what-e164) formatted phone number, depending on the destination.
        :param from_: The Caller ID of the call to the worker when executing a Conference instruction.
        :param status_callback: The URL we should call using the `status_callback_method` to send status information to your application.
        :param status_callback_method: The HTTP method we should use to call `status_callback`. Can be: `POST` or `GET` and the default is `POST`.
        :param status_callback_event: The call progress events that we will send to `status_callback`. Can be: `initiated`, `ringing`, `answered`, or `completed`.
        :param timeout: Timeout for call when executing a Conference instruction.
        :param record: Whether to record the participant and their conferences, including the time between conferences. The default is `false`.
        :param muted: Whether the agent is muted in the conference. The default is `false`.
        :param beep: Whether to play a notification beep when the participant joins or when to play a beep. Can be: `true`, `false`, `onEnter`, or `onExit`. The default value is `true`.
        :param start_conference_on_enter: Whether to start the conference when the participant joins, if it has not already started. The default is `true`. If `false` and the conference has not started, the participant is muted and hears background music until another participant starts the conference.
        :param end_conference_on_exit: Whether to end the conference when the agent leaves.
        :param wait_url: The URL we should call using the `wait_method` for the music to play while participants are waiting for the conference to start. The default value is the URL of our standard hold music. [Learn more about hold music](https://www.twilio.com/labs/twimlets/holdmusic).
        :param wait_method: The HTTP method we should use to call `wait_url`. Can be `GET` or `POST` and the default is `POST`. When using a static audio file, this should be `GET` so that we can cache the file.
        :param early_media: Whether to allow an agent to hear the state of the outbound call, including ringing or disconnect messages. The default is `true`.
        :param max_participants: The maximum number of participants in the conference. Can be a positive integer from `2` to `250`. The default value is `250`.
        :param conference_status_callback: The URL we should call using the `conference_status_callback_method` when the conference events in `conference_status_callback_event` occur. Only the value set by the first participant to join the conference is used. Subsequent `conference_status_callback` values are ignored.
        :param conference_status_callback_method: The HTTP method we should use to call `conference_status_callback`. Can be: `GET` or `POST` and defaults to `POST`.
        :param conference_status_callback_event: The conference status events that we will send to `conference_status_callback`. Can be: `start`, `end`, `join`, `leave`, `mute`, `hold`, `speaker`.
        :param conference_record: Whether to record the conference the participant is joining or when to record the conference. Can be: `true`, `false`, `record-from-start`, and `do-not-record`. The default value is `false`.
        :param conference_trim: How to trim the leading and trailing silence from your recorded conference audio files. Can be: `trim-silence` or `do-not-trim` and defaults to `trim-silence`.
        :param recording_channels: The recording channels for the final recording. Can be: `mono` or `dual` and the default is `mono`.
        :param recording_status_callback: The URL that we should call using the `recording_status_callback_method` when the recording status changes.
        :param recording_status_callback_method: The HTTP method we should use when we call `recording_status_callback`. Can be: `GET` or `POST` and defaults to `POST`.
        :param conference_recording_status_callback: The URL we should call using the `conference_recording_status_callback_method` when the conference recording is available.
        :param conference_recording_status_callback_method: The HTTP method we should use to call `conference_recording_status_callback`. Can be: `GET` or `POST` and defaults to `POST`.
        :param region: The [region](https://support.twilio.com/hc/en-us/articles/*********-How-global-low-latency-routing-and-region-selection-work-for-conferences-and-Client-calls) where we should mix the recorded audio. Can be:`us1`, `ie1`, `de1`, `sg1`, `br1`, `au1`, or `jp1`.
        :param sip_auth_username: The SIP username used for authentication.
        :param sip_auth_password: The SIP password for authentication.
        :param dequeue_status_callback_event: The Call progress events sent via webhooks as a result of a Dequeue instruction.
        :param post_work_activity_sid: The new worker activity SID after executing a Conference instruction.
        :param supervisor_mode:
        :param supervisor: The Supervisor SID/URI when executing the Supervise instruction.
        :param end_conference_on_customer_exit: Whether to end the conference when the customer leaves.
        :param beep_on_customer_entrance: Whether to play a notification beep when the customer joins.

        :returns: The updated ReservationInstance
        """
        return await self._proxy.update_async(
            if_match=if_match,
            reservation_status=reservation_status,
            worker_activity_sid=worker_activity_sid,
            instruction=instruction,
            dequeue_post_work_activity_sid=dequeue_post_work_activity_sid,
            dequeue_from=dequeue_from,
            dequeue_record=dequeue_record,
            dequeue_timeout=dequeue_timeout,
            dequeue_to=dequeue_to,
            dequeue_status_callback_url=dequeue_status_callback_url,
            call_from=call_from,
            call_record=call_record,
            call_timeout=call_timeout,
            call_to=call_to,
            call_url=call_url,
            call_status_callback_url=call_status_callback_url,
            call_accept=call_accept,
            redirect_call_sid=redirect_call_sid,
            redirect_accept=redirect_accept,
            redirect_url=redirect_url,
            to=to,
            from_=from_,
            status_callback=status_callback,
            status_callback_method=status_callback_method,
            status_callback_event=status_callback_event,
            timeout=timeout,
            record=record,
            muted=muted,
            beep=beep,
            start_conference_on_enter=start_conference_on_enter,
            end_conference_on_exit=end_conference_on_exit,
            wait_url=wait_url,
            wait_method=wait_method,
            early_media=early_media,
            max_participants=max_participants,
            conference_status_callback=conference_status_callback,
            conference_status_callback_method=conference_status_callback_method,
            conference_status_callback_event=conference_status_callback_event,
            conference_record=conference_record,
            conference_trim=conference_trim,
            recording_channels=recording_channels,
            recording_status_callback=recording_status_callback,
            recording_status_callback_method=recording_status_callback_method,
            conference_recording_status_callback=conference_recording_status_callback,
            conference_recording_status_callback_method=conference_recording_status_callback_method,
            region=region,
            sip_auth_username=sip_auth_username,
            sip_auth_password=sip_auth_password,
            dequeue_status_callback_event=dequeue_status_callback_event,
            post_work_activity_sid=post_work_activity_sid,
            supervisor_mode=supervisor_mode,
            supervisor=supervisor,
            end_conference_on_customer_exit=end_conference_on_customer_exit,
            beep_on_customer_entrance=beep_on_customer_entrance,
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Taskrouter.V1.ReservationInstance {}>".format(context)


class ReservationContext(InstanceContext):
    def __init__(self, version: Version, workspace_sid: str, task_sid: str, sid: str):
        """
        Initialize the ReservationContext

        :param version: Version that contains the resource
        :param workspace_sid: The SID of the Workspace with the TaskReservation resources to update.
        :param task_sid: The SID of the reserved Task resource with the TaskReservation resources to update.
        :param sid: The SID of the TaskReservation resource to update.
        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "workspace_sid": workspace_sid,
            "task_sid": task_sid,
            "sid": sid,
        }
        self._uri = (
            "/Workspaces/{workspace_sid}/Tasks/{task_sid}/Reservations/{sid}".format(
                **self._solution
            )
        )

    def fetch(self) -> ReservationInstance:
        """
        Fetch the ReservationInstance


        :returns: The fetched ReservationInstance
        """

        payload = self._version.fetch(
            method="GET",
            uri=self._uri,
        )

        return ReservationInstance(
            self._version,
            payload,
            workspace_sid=self._solution["workspace_sid"],
            task_sid=self._solution["task_sid"],
            sid=self._solution["sid"],
        )

    async def fetch_async(self) -> ReservationInstance:
        """
        Asynchronous coroutine to fetch the ReservationInstance


        :returns: The fetched ReservationInstance
        """

        payload = await self._version.fetch_async(
            method="GET",
            uri=self._uri,
        )

        return ReservationInstance(
            self._version,
            payload,
            workspace_sid=self._solution["workspace_sid"],
            task_sid=self._solution["task_sid"],
            sid=self._solution["sid"],
        )

    def update(
        self,
        if_match: Union[str, object] = values.unset,
        reservation_status: Union["ReservationInstance.Status", object] = values.unset,
        worker_activity_sid: Union[str, object] = values.unset,
        instruction: Union[str, object] = values.unset,
        dequeue_post_work_activity_sid: Union[str, object] = values.unset,
        dequeue_from: Union[str, object] = values.unset,
        dequeue_record: Union[str, object] = values.unset,
        dequeue_timeout: Union[int, object] = values.unset,
        dequeue_to: Union[str, object] = values.unset,
        dequeue_status_callback_url: Union[str, object] = values.unset,
        call_from: Union[str, object] = values.unset,
        call_record: Union[str, object] = values.unset,
        call_timeout: Union[int, object] = values.unset,
        call_to: Union[str, object] = values.unset,
        call_url: Union[str, object] = values.unset,
        call_status_callback_url: Union[str, object] = values.unset,
        call_accept: Union[bool, object] = values.unset,
        redirect_call_sid: Union[str, object] = values.unset,
        redirect_accept: Union[bool, object] = values.unset,
        redirect_url: Union[str, object] = values.unset,
        to: Union[str, object] = values.unset,
        from_: Union[str, object] = values.unset,
        status_callback: Union[str, object] = values.unset,
        status_callback_method: Union[str, object] = values.unset,
        status_callback_event: Union[
            List["ReservationInstance.CallStatus"], object
        ] = values.unset,
        timeout: Union[int, object] = values.unset,
        record: Union[bool, object] = values.unset,
        muted: Union[bool, object] = values.unset,
        beep: Union[str, object] = values.unset,
        start_conference_on_enter: Union[bool, object] = values.unset,
        end_conference_on_exit: Union[bool, object] = values.unset,
        wait_url: Union[str, object] = values.unset,
        wait_method: Union[str, object] = values.unset,
        early_media: Union[bool, object] = values.unset,
        max_participants: Union[int, object] = values.unset,
        conference_status_callback: Union[str, object] = values.unset,
        conference_status_callback_method: Union[str, object] = values.unset,
        conference_status_callback_event: Union[
            List["ReservationInstance.ConferenceEvent"], object
        ] = values.unset,
        conference_record: Union[str, object] = values.unset,
        conference_trim: Union[str, object] = values.unset,
        recording_channels: Union[str, object] = values.unset,
        recording_status_callback: Union[str, object] = values.unset,
        recording_status_callback_method: Union[str, object] = values.unset,
        conference_recording_status_callback: Union[str, object] = values.unset,
        conference_recording_status_callback_method: Union[str, object] = values.unset,
        region: Union[str, object] = values.unset,
        sip_auth_username: Union[str, object] = values.unset,
        sip_auth_password: Union[str, object] = values.unset,
        dequeue_status_callback_event: Union[List[str], object] = values.unset,
        post_work_activity_sid: Union[str, object] = values.unset,
        supervisor_mode: Union[
            "ReservationInstance.SupervisorMode", object
        ] = values.unset,
        supervisor: Union[str, object] = values.unset,
        end_conference_on_customer_exit: Union[bool, object] = values.unset,
        beep_on_customer_entrance: Union[bool, object] = values.unset,
    ) -> ReservationInstance:
        """
        Update the ReservationInstance

        :param if_match: The If-Match HTTP request header
        :param reservation_status:
        :param worker_activity_sid: The new worker activity SID if rejecting a reservation.
        :param instruction: The assignment instruction for reservation.
        :param dequeue_post_work_activity_sid: The SID of the Activity resource to start after executing a Dequeue instruction.
        :param dequeue_from: The Caller ID of the call to the worker when executing a Dequeue instruction.
        :param dequeue_record: Whether to record both legs of a call when executing a Dequeue instruction or which leg to record.
        :param dequeue_timeout: Timeout for call when executing a Dequeue instruction.
        :param dequeue_to: The Contact URI of the worker when executing a Dequeue instruction. Can be the URI of the Twilio Client, the SIP URI for Programmable SIP, or the [E.164](https://www.twilio.com/docs/glossary/what-e164) formatted phone number, depending on the destination.
        :param dequeue_status_callback_url: The Callback URL for completed call event when executing a Dequeue instruction.
        :param call_from: The Caller ID of the outbound call when executing a Call instruction.
        :param call_record: Whether to record both legs of a call when executing a Call instruction or which leg to record.
        :param call_timeout: Timeout for call when executing a Call instruction.
        :param call_to: The Contact URI of the worker when executing a Call instruction.  Can be the URI of the Twilio Client, the SIP URI for Programmable SIP, or the [E.164](https://www.twilio.com/docs/glossary/what-e164) formatted phone number, depending on the destination.
        :param call_url: TwiML URI executed on answering the worker's leg as a result of the Call instruction.
        :param call_status_callback_url: The URL to call  for the completed call event when executing a Call instruction.
        :param call_accept: Whether to accept a reservation when executing a Call instruction.
        :param redirect_call_sid: The Call SID of the call parked in the queue when executing a Redirect instruction.
        :param redirect_accept: Whether the reservation should be accepted when executing a Redirect instruction.
        :param redirect_url: TwiML URI to redirect the call to when executing the Redirect instruction.
        :param to: The Contact URI of the worker when executing a Conference instruction. Can be the URI of the Twilio Client, the SIP URI for Programmable SIP, or the [E.164](https://www.twilio.com/docs/glossary/what-e164) formatted phone number, depending on the destination.
        :param from_: The Caller ID of the call to the worker when executing a Conference instruction.
        :param status_callback: The URL we should call using the `status_callback_method` to send status information to your application.
        :param status_callback_method: The HTTP method we should use to call `status_callback`. Can be: `POST` or `GET` and the default is `POST`.
        :param status_callback_event: The call progress events that we will send to `status_callback`. Can be: `initiated`, `ringing`, `answered`, or `completed`.
        :param timeout: Timeout for call when executing a Conference instruction.
        :param record: Whether to record the participant and their conferences, including the time between conferences. The default is `false`.
        :param muted: Whether the agent is muted in the conference. The default is `false`.
        :param beep: Whether to play a notification beep when the participant joins or when to play a beep. Can be: `true`, `false`, `onEnter`, or `onExit`. The default value is `true`.
        :param start_conference_on_enter: Whether to start the conference when the participant joins, if it has not already started. The default is `true`. If `false` and the conference has not started, the participant is muted and hears background music until another participant starts the conference.
        :param end_conference_on_exit: Whether to end the conference when the agent leaves.
        :param wait_url: The URL we should call using the `wait_method` for the music to play while participants are waiting for the conference to start. The default value is the URL of our standard hold music. [Learn more about hold music](https://www.twilio.com/labs/twimlets/holdmusic).
        :param wait_method: The HTTP method we should use to call `wait_url`. Can be `GET` or `POST` and the default is `POST`. When using a static audio file, this should be `GET` so that we can cache the file.
        :param early_media: Whether to allow an agent to hear the state of the outbound call, including ringing or disconnect messages. The default is `true`.
        :param max_participants: The maximum number of participants in the conference. Can be a positive integer from `2` to `250`. The default value is `250`.
        :param conference_status_callback: The URL we should call using the `conference_status_callback_method` when the conference events in `conference_status_callback_event` occur. Only the value set by the first participant to join the conference is used. Subsequent `conference_status_callback` values are ignored.
        :param conference_status_callback_method: The HTTP method we should use to call `conference_status_callback`. Can be: `GET` or `POST` and defaults to `POST`.
        :param conference_status_callback_event: The conference status events that we will send to `conference_status_callback`. Can be: `start`, `end`, `join`, `leave`, `mute`, `hold`, `speaker`.
        :param conference_record: Whether to record the conference the participant is joining or when to record the conference. Can be: `true`, `false`, `record-from-start`, and `do-not-record`. The default value is `false`.
        :param conference_trim: How to trim the leading and trailing silence from your recorded conference audio files. Can be: `trim-silence` or `do-not-trim` and defaults to `trim-silence`.
        :param recording_channels: The recording channels for the final recording. Can be: `mono` or `dual` and the default is `mono`.
        :param recording_status_callback: The URL that we should call using the `recording_status_callback_method` when the recording status changes.
        :param recording_status_callback_method: The HTTP method we should use when we call `recording_status_callback`. Can be: `GET` or `POST` and defaults to `POST`.
        :param conference_recording_status_callback: The URL we should call using the `conference_recording_status_callback_method` when the conference recording is available.
        :param conference_recording_status_callback_method: The HTTP method we should use to call `conference_recording_status_callback`. Can be: `GET` or `POST` and defaults to `POST`.
        :param region: The [region](https://support.twilio.com/hc/en-us/articles/*********-How-global-low-latency-routing-and-region-selection-work-for-conferences-and-Client-calls) where we should mix the recorded audio. Can be:`us1`, `ie1`, `de1`, `sg1`, `br1`, `au1`, or `jp1`.
        :param sip_auth_username: The SIP username used for authentication.
        :param sip_auth_password: The SIP password for authentication.
        :param dequeue_status_callback_event: The Call progress events sent via webhooks as a result of a Dequeue instruction.
        :param post_work_activity_sid: The new worker activity SID after executing a Conference instruction.
        :param supervisor_mode:
        :param supervisor: The Supervisor SID/URI when executing the Supervise instruction.
        :param end_conference_on_customer_exit: Whether to end the conference when the customer leaves.
        :param beep_on_customer_entrance: Whether to play a notification beep when the customer joins.

        :returns: The updated ReservationInstance
        """
        data = values.of(
            {
                "ReservationStatus": reservation_status,
                "WorkerActivitySid": worker_activity_sid,
                "Instruction": instruction,
                "DequeuePostWorkActivitySid": dequeue_post_work_activity_sid,
                "DequeueFrom": dequeue_from,
                "DequeueRecord": dequeue_record,
                "DequeueTimeout": dequeue_timeout,
                "DequeueTo": dequeue_to,
                "DequeueStatusCallbackUrl": dequeue_status_callback_url,
                "CallFrom": call_from,
                "CallRecord": call_record,
                "CallTimeout": call_timeout,
                "CallTo": call_to,
                "CallUrl": call_url,
                "CallStatusCallbackUrl": call_status_callback_url,
                "CallAccept": call_accept,
                "RedirectCallSid": redirect_call_sid,
                "RedirectAccept": redirect_accept,
                "RedirectUrl": redirect_url,
                "To": to,
                "From": from_,
                "StatusCallback": status_callback,
                "StatusCallbackMethod": status_callback_method,
                "StatusCallbackEvent": serialize.map(
                    status_callback_event, lambda e: e
                ),
                "Timeout": timeout,
                "Record": record,
                "Muted": muted,
                "Beep": beep,
                "StartConferenceOnEnter": start_conference_on_enter,
                "EndConferenceOnExit": end_conference_on_exit,
                "WaitUrl": wait_url,
                "WaitMethod": wait_method,
                "EarlyMedia": early_media,
                "MaxParticipants": max_participants,
                "ConferenceStatusCallback": conference_status_callback,
                "ConferenceStatusCallbackMethod": conference_status_callback_method,
                "ConferenceStatusCallbackEvent": serialize.map(
                    conference_status_callback_event, lambda e: e
                ),
                "ConferenceRecord": conference_record,
                "ConferenceTrim": conference_trim,
                "RecordingChannels": recording_channels,
                "RecordingStatusCallback": recording_status_callback,
                "RecordingStatusCallbackMethod": recording_status_callback_method,
                "ConferenceRecordingStatusCallback": conference_recording_status_callback,
                "ConferenceRecordingStatusCallbackMethod": conference_recording_status_callback_method,
                "Region": region,
                "SipAuthUsername": sip_auth_username,
                "SipAuthPassword": sip_auth_password,
                "DequeueStatusCallbackEvent": serialize.map(
                    dequeue_status_callback_event, lambda e: e
                ),
                "PostWorkActivitySid": post_work_activity_sid,
                "SupervisorMode": supervisor_mode,
                "Supervisor": supervisor,
                "EndConferenceOnCustomerExit": end_conference_on_customer_exit,
                "BeepOnCustomerEntrance": beep_on_customer_entrance,
            }
        )
        headers = values.of(
            {
                "If-Match": if_match,
            }
        )

        payload = self._version.update(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return ReservationInstance(
            self._version,
            payload,
            workspace_sid=self._solution["workspace_sid"],
            task_sid=self._solution["task_sid"],
            sid=self._solution["sid"],
        )

    async def update_async(
        self,
        if_match: Union[str, object] = values.unset,
        reservation_status: Union["ReservationInstance.Status", object] = values.unset,
        worker_activity_sid: Union[str, object] = values.unset,
        instruction: Union[str, object] = values.unset,
        dequeue_post_work_activity_sid: Union[str, object] = values.unset,
        dequeue_from: Union[str, object] = values.unset,
        dequeue_record: Union[str, object] = values.unset,
        dequeue_timeout: Union[int, object] = values.unset,
        dequeue_to: Union[str, object] = values.unset,
        dequeue_status_callback_url: Union[str, object] = values.unset,
        call_from: Union[str, object] = values.unset,
        call_record: Union[str, object] = values.unset,
        call_timeout: Union[int, object] = values.unset,
        call_to: Union[str, object] = values.unset,
        call_url: Union[str, object] = values.unset,
        call_status_callback_url: Union[str, object] = values.unset,
        call_accept: Union[bool, object] = values.unset,
        redirect_call_sid: Union[str, object] = values.unset,
        redirect_accept: Union[bool, object] = values.unset,
        redirect_url: Union[str, object] = values.unset,
        to: Union[str, object] = values.unset,
        from_: Union[str, object] = values.unset,
        status_callback: Union[str, object] = values.unset,
        status_callback_method: Union[str, object] = values.unset,
        status_callback_event: Union[
            List["ReservationInstance.CallStatus"], object
        ] = values.unset,
        timeout: Union[int, object] = values.unset,
        record: Union[bool, object] = values.unset,
        muted: Union[bool, object] = values.unset,
        beep: Union[str, object] = values.unset,
        start_conference_on_enter: Union[bool, object] = values.unset,
        end_conference_on_exit: Union[bool, object] = values.unset,
        wait_url: Union[str, object] = values.unset,
        wait_method: Union[str, object] = values.unset,
        early_media: Union[bool, object] = values.unset,
        max_participants: Union[int, object] = values.unset,
        conference_status_callback: Union[str, object] = values.unset,
        conference_status_callback_method: Union[str, object] = values.unset,
        conference_status_callback_event: Union[
            List["ReservationInstance.ConferenceEvent"], object
        ] = values.unset,
        conference_record: Union[str, object] = values.unset,
        conference_trim: Union[str, object] = values.unset,
        recording_channels: Union[str, object] = values.unset,
        recording_status_callback: Union[str, object] = values.unset,
        recording_status_callback_method: Union[str, object] = values.unset,
        conference_recording_status_callback: Union[str, object] = values.unset,
        conference_recording_status_callback_method: Union[str, object] = values.unset,
        region: Union[str, object] = values.unset,
        sip_auth_username: Union[str, object] = values.unset,
        sip_auth_password: Union[str, object] = values.unset,
        dequeue_status_callback_event: Union[List[str], object] = values.unset,
        post_work_activity_sid: Union[str, object] = values.unset,
        supervisor_mode: Union[
            "ReservationInstance.SupervisorMode", object
        ] = values.unset,
        supervisor: Union[str, object] = values.unset,
        end_conference_on_customer_exit: Union[bool, object] = values.unset,
        beep_on_customer_entrance: Union[bool, object] = values.unset,
    ) -> ReservationInstance:
        """
        Asynchronous coroutine to update the ReservationInstance

        :param if_match: The If-Match HTTP request header
        :param reservation_status:
        :param worker_activity_sid: The new worker activity SID if rejecting a reservation.
        :param instruction: The assignment instruction for reservation.
        :param dequeue_post_work_activity_sid: The SID of the Activity resource to start after executing a Dequeue instruction.
        :param dequeue_from: The Caller ID of the call to the worker when executing a Dequeue instruction.
        :param dequeue_record: Whether to record both legs of a call when executing a Dequeue instruction or which leg to record.
        :param dequeue_timeout: Timeout for call when executing a Dequeue instruction.
        :param dequeue_to: The Contact URI of the worker when executing a Dequeue instruction. Can be the URI of the Twilio Client, the SIP URI for Programmable SIP, or the [E.164](https://www.twilio.com/docs/glossary/what-e164) formatted phone number, depending on the destination.
        :param dequeue_status_callback_url: The Callback URL for completed call event when executing a Dequeue instruction.
        :param call_from: The Caller ID of the outbound call when executing a Call instruction.
        :param call_record: Whether to record both legs of a call when executing a Call instruction or which leg to record.
        :param call_timeout: Timeout for call when executing a Call instruction.
        :param call_to: The Contact URI of the worker when executing a Call instruction.  Can be the URI of the Twilio Client, the SIP URI for Programmable SIP, or the [E.164](https://www.twilio.com/docs/glossary/what-e164) formatted phone number, depending on the destination.
        :param call_url: TwiML URI executed on answering the worker's leg as a result of the Call instruction.
        :param call_status_callback_url: The URL to call  for the completed call event when executing a Call instruction.
        :param call_accept: Whether to accept a reservation when executing a Call instruction.
        :param redirect_call_sid: The Call SID of the call parked in the queue when executing a Redirect instruction.
        :param redirect_accept: Whether the reservation should be accepted when executing a Redirect instruction.
        :param redirect_url: TwiML URI to redirect the call to when executing the Redirect instruction.
        :param to: The Contact URI of the worker when executing a Conference instruction. Can be the URI of the Twilio Client, the SIP URI for Programmable SIP, or the [E.164](https://www.twilio.com/docs/glossary/what-e164) formatted phone number, depending on the destination.
        :param from_: The Caller ID of the call to the worker when executing a Conference instruction.
        :param status_callback: The URL we should call using the `status_callback_method` to send status information to your application.
        :param status_callback_method: The HTTP method we should use to call `status_callback`. Can be: `POST` or `GET` and the default is `POST`.
        :param status_callback_event: The call progress events that we will send to `status_callback`. Can be: `initiated`, `ringing`, `answered`, or `completed`.
        :param timeout: Timeout for call when executing a Conference instruction.
        :param record: Whether to record the participant and their conferences, including the time between conferences. The default is `false`.
        :param muted: Whether the agent is muted in the conference. The default is `false`.
        :param beep: Whether to play a notification beep when the participant joins or when to play a beep. Can be: `true`, `false`, `onEnter`, or `onExit`. The default value is `true`.
        :param start_conference_on_enter: Whether to start the conference when the participant joins, if it has not already started. The default is `true`. If `false` and the conference has not started, the participant is muted and hears background music until another participant starts the conference.
        :param end_conference_on_exit: Whether to end the conference when the agent leaves.
        :param wait_url: The URL we should call using the `wait_method` for the music to play while participants are waiting for the conference to start. The default value is the URL of our standard hold music. [Learn more about hold music](https://www.twilio.com/labs/twimlets/holdmusic).
        :param wait_method: The HTTP method we should use to call `wait_url`. Can be `GET` or `POST` and the default is `POST`. When using a static audio file, this should be `GET` so that we can cache the file.
        :param early_media: Whether to allow an agent to hear the state of the outbound call, including ringing or disconnect messages. The default is `true`.
        :param max_participants: The maximum number of participants in the conference. Can be a positive integer from `2` to `250`. The default value is `250`.
        :param conference_status_callback: The URL we should call using the `conference_status_callback_method` when the conference events in `conference_status_callback_event` occur. Only the value set by the first participant to join the conference is used. Subsequent `conference_status_callback` values are ignored.
        :param conference_status_callback_method: The HTTP method we should use to call `conference_status_callback`. Can be: `GET` or `POST` and defaults to `POST`.
        :param conference_status_callback_event: The conference status events that we will send to `conference_status_callback`. Can be: `start`, `end`, `join`, `leave`, `mute`, `hold`, `speaker`.
        :param conference_record: Whether to record the conference the participant is joining or when to record the conference. Can be: `true`, `false`, `record-from-start`, and `do-not-record`. The default value is `false`.
        :param conference_trim: How to trim the leading and trailing silence from your recorded conference audio files. Can be: `trim-silence` or `do-not-trim` and defaults to `trim-silence`.
        :param recording_channels: The recording channels for the final recording. Can be: `mono` or `dual` and the default is `mono`.
        :param recording_status_callback: The URL that we should call using the `recording_status_callback_method` when the recording status changes.
        :param recording_status_callback_method: The HTTP method we should use when we call `recording_status_callback`. Can be: `GET` or `POST` and defaults to `POST`.
        :param conference_recording_status_callback: The URL we should call using the `conference_recording_status_callback_method` when the conference recording is available.
        :param conference_recording_status_callback_method: The HTTP method we should use to call `conference_recording_status_callback`. Can be: `GET` or `POST` and defaults to `POST`.
        :param region: The [region](https://support.twilio.com/hc/en-us/articles/*********-How-global-low-latency-routing-and-region-selection-work-for-conferences-and-Client-calls) where we should mix the recorded audio. Can be:`us1`, `ie1`, `de1`, `sg1`, `br1`, `au1`, or `jp1`.
        :param sip_auth_username: The SIP username used for authentication.
        :param sip_auth_password: The SIP password for authentication.
        :param dequeue_status_callback_event: The Call progress events sent via webhooks as a result of a Dequeue instruction.
        :param post_work_activity_sid: The new worker activity SID after executing a Conference instruction.
        :param supervisor_mode:
        :param supervisor: The Supervisor SID/URI when executing the Supervise instruction.
        :param end_conference_on_customer_exit: Whether to end the conference when the customer leaves.
        :param beep_on_customer_entrance: Whether to play a notification beep when the customer joins.

        :returns: The updated ReservationInstance
        """
        data = values.of(
            {
                "ReservationStatus": reservation_status,
                "WorkerActivitySid": worker_activity_sid,
                "Instruction": instruction,
                "DequeuePostWorkActivitySid": dequeue_post_work_activity_sid,
                "DequeueFrom": dequeue_from,
                "DequeueRecord": dequeue_record,
                "DequeueTimeout": dequeue_timeout,
                "DequeueTo": dequeue_to,
                "DequeueStatusCallbackUrl": dequeue_status_callback_url,
                "CallFrom": call_from,
                "CallRecord": call_record,
                "CallTimeout": call_timeout,
                "CallTo": call_to,
                "CallUrl": call_url,
                "CallStatusCallbackUrl": call_status_callback_url,
                "CallAccept": call_accept,
                "RedirectCallSid": redirect_call_sid,
                "RedirectAccept": redirect_accept,
                "RedirectUrl": redirect_url,
                "To": to,
                "From": from_,
                "StatusCallback": status_callback,
                "StatusCallbackMethod": status_callback_method,
                "StatusCallbackEvent": serialize.map(
                    status_callback_event, lambda e: e
                ),
                "Timeout": timeout,
                "Record": record,
                "Muted": muted,
                "Beep": beep,
                "StartConferenceOnEnter": start_conference_on_enter,
                "EndConferenceOnExit": end_conference_on_exit,
                "WaitUrl": wait_url,
                "WaitMethod": wait_method,
                "EarlyMedia": early_media,
                "MaxParticipants": max_participants,
                "ConferenceStatusCallback": conference_status_callback,
                "ConferenceStatusCallbackMethod": conference_status_callback_method,
                "ConferenceStatusCallbackEvent": serialize.map(
                    conference_status_callback_event, lambda e: e
                ),
                "ConferenceRecord": conference_record,
                "ConferenceTrim": conference_trim,
                "RecordingChannels": recording_channels,
                "RecordingStatusCallback": recording_status_callback,
                "RecordingStatusCallbackMethod": recording_status_callback_method,
                "ConferenceRecordingStatusCallback": conference_recording_status_callback,
                "ConferenceRecordingStatusCallbackMethod": conference_recording_status_callback_method,
                "Region": region,
                "SipAuthUsername": sip_auth_username,
                "SipAuthPassword": sip_auth_password,
                "DequeueStatusCallbackEvent": serialize.map(
                    dequeue_status_callback_event, lambda e: e
                ),
                "PostWorkActivitySid": post_work_activity_sid,
                "SupervisorMode": supervisor_mode,
                "Supervisor": supervisor,
                "EndConferenceOnCustomerExit": end_conference_on_customer_exit,
                "BeepOnCustomerEntrance": beep_on_customer_entrance,
            }
        )
        headers = values.of(
            {
                "If-Match": if_match,
            }
        )

        payload = await self._version.update_async(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return ReservationInstance(
            self._version,
            payload,
            workspace_sid=self._solution["workspace_sid"],
            task_sid=self._solution["task_sid"],
            sid=self._solution["sid"],
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Taskrouter.V1.ReservationContext {}>".format(context)


class ReservationPage(Page):
    def get_instance(self, payload: Dict[str, Any]) -> ReservationInstance:
        """
        Build an instance of ReservationInstance

        :param payload: Payload response from the API
        """
        return ReservationInstance(
            self._version,
            payload,
            workspace_sid=self._solution["workspace_sid"],
            task_sid=self._solution["task_sid"],
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Taskrouter.V1.ReservationPage>"


class ReservationList(ListResource):
    def __init__(self, version: Version, workspace_sid: str, task_sid: str):
        """
        Initialize the ReservationList

        :param version: Version that contains the resource
        :param workspace_sid: The SID of the Workspace with the TaskReservation resources to read.
        :param task_sid: The SID of the reserved Task resource with the TaskReservation resources to read.

        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "workspace_sid": workspace_sid,
            "task_sid": task_sid,
        }
        self._uri = "/Workspaces/{workspace_sid}/Tasks/{task_sid}/Reservations".format(
            **self._solution
        )

    def stream(
        self,
        reservation_status: Union["ReservationInstance.Status", object] = values.unset,
        worker_sid: Union[str, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> Iterator[ReservationInstance]:
        """
        Streams ReservationInstance records from the API as a generator stream.
        This operation lazily loads records as efficiently as possible until the limit
        is reached.
        The results are returned as a generator, so this operation is memory efficient.

        :param &quot;ReservationInstance.Status&quot; reservation_status: Returns the list of reservations for a task with a specified ReservationStatus.  Can be: `pending`, `accepted`, `rejected`, or `timeout`.
        :param str worker_sid: The SID of the reserved Worker resource to read.
        :param limit: Upper limit for the number of records to return. stream()
                      guarantees to never return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, stream() will attempt to read the
                          limit with the most efficient page size, i.e. min(limit, 1000)

        :returns: Generator that will yield up to limit results
        """
        limits = self._version.read_limits(limit, page_size)
        page = self.page(
            reservation_status=reservation_status,
            worker_sid=worker_sid,
            page_size=limits["page_size"],
        )

        return self._version.stream(page, limits["limit"])

    async def stream_async(
        self,
        reservation_status: Union["ReservationInstance.Status", object] = values.unset,
        worker_sid: Union[str, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> AsyncIterator[ReservationInstance]:
        """
        Asynchronously streams ReservationInstance records from the API as a generator stream.
        This operation lazily loads records as efficiently as possible until the limit
        is reached.
        The results are returned as a generator, so this operation is memory efficient.

        :param &quot;ReservationInstance.Status&quot; reservation_status: Returns the list of reservations for a task with a specified ReservationStatus.  Can be: `pending`, `accepted`, `rejected`, or `timeout`.
        :param str worker_sid: The SID of the reserved Worker resource to read.
        :param limit: Upper limit for the number of records to return. stream()
                      guarantees to never return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, stream() will attempt to read the
                          limit with the most efficient page size, i.e. min(limit, 1000)

        :returns: Generator that will yield up to limit results
        """
        limits = self._version.read_limits(limit, page_size)
        page = await self.page_async(
            reservation_status=reservation_status,
            worker_sid=worker_sid,
            page_size=limits["page_size"],
        )

        return self._version.stream_async(page, limits["limit"])

    def list(
        self,
        reservation_status: Union["ReservationInstance.Status", object] = values.unset,
        worker_sid: Union[str, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> List[ReservationInstance]:
        """
        Lists ReservationInstance records from the API as a list.
        Unlike stream(), this operation is eager and will load `limit` records into
        memory before returning.

        :param &quot;ReservationInstance.Status&quot; reservation_status: Returns the list of reservations for a task with a specified ReservationStatus.  Can be: `pending`, `accepted`, `rejected`, or `timeout`.
        :param str worker_sid: The SID of the reserved Worker resource to read.
        :param limit: Upper limit for the number of records to return. list() guarantees
                      never to return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, list() will attempt to read the limit
                          with the most efficient page size, i.e. min(limit, 1000)

        :returns: list that will contain up to limit results
        """
        return list(
            self.stream(
                reservation_status=reservation_status,
                worker_sid=worker_sid,
                limit=limit,
                page_size=page_size,
            )
        )

    async def list_async(
        self,
        reservation_status: Union["ReservationInstance.Status", object] = values.unset,
        worker_sid: Union[str, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> List[ReservationInstance]:
        """
        Asynchronously lists ReservationInstance records from the API as a list.
        Unlike stream(), this operation is eager and will load `limit` records into
        memory before returning.

        :param &quot;ReservationInstance.Status&quot; reservation_status: Returns the list of reservations for a task with a specified ReservationStatus.  Can be: `pending`, `accepted`, `rejected`, or `timeout`.
        :param str worker_sid: The SID of the reserved Worker resource to read.
        :param limit: Upper limit for the number of records to return. list() guarantees
                      never to return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, list() will attempt to read the limit
                          with the most efficient page size, i.e. min(limit, 1000)

        :returns: list that will contain up to limit results
        """
        return [
            record
            async for record in await self.stream_async(
                reservation_status=reservation_status,
                worker_sid=worker_sid,
                limit=limit,
                page_size=page_size,
            )
        ]

    def page(
        self,
        reservation_status: Union["ReservationInstance.Status", object] = values.unset,
        worker_sid: Union[str, object] = values.unset,
        page_token: Union[str, object] = values.unset,
        page_number: Union[int, object] = values.unset,
        page_size: Union[int, object] = values.unset,
    ) -> ReservationPage:
        """
        Retrieve a single page of ReservationInstance records from the API.
        Request is executed immediately

        :param reservation_status: Returns the list of reservations for a task with a specified ReservationStatus.  Can be: `pending`, `accepted`, `rejected`, or `timeout`.
        :param worker_sid: The SID of the reserved Worker resource to read.
        :param page_token: PageToken provided by the API
        :param page_number: Page Number, this value is simply for client state
        :param page_size: Number of records to return, defaults to 50

        :returns: Page of ReservationInstance
        """
        data = values.of(
            {
                "ReservationStatus": reservation_status,
                "WorkerSid": worker_sid,
                "PageToken": page_token,
                "Page": page_number,
                "PageSize": page_size,
            }
        )

        response = self._version.page(method="GET", uri=self._uri, params=data)
        return ReservationPage(self._version, response, self._solution)

    async def page_async(
        self,
        reservation_status: Union["ReservationInstance.Status", object] = values.unset,
        worker_sid: Union[str, object] = values.unset,
        page_token: Union[str, object] = values.unset,
        page_number: Union[int, object] = values.unset,
        page_size: Union[int, object] = values.unset,
    ) -> ReservationPage:
        """
        Asynchronously retrieve a single page of ReservationInstance records from the API.
        Request is executed immediately

        :param reservation_status: Returns the list of reservations for a task with a specified ReservationStatus.  Can be: `pending`, `accepted`, `rejected`, or `timeout`.
        :param worker_sid: The SID of the reserved Worker resource to read.
        :param page_token: PageToken provided by the API
        :param page_number: Page Number, this value is simply for client state
        :param page_size: Number of records to return, defaults to 50

        :returns: Page of ReservationInstance
        """
        data = values.of(
            {
                "ReservationStatus": reservation_status,
                "WorkerSid": worker_sid,
                "PageToken": page_token,
                "Page": page_number,
                "PageSize": page_size,
            }
        )

        response = await self._version.page_async(
            method="GET", uri=self._uri, params=data
        )
        return ReservationPage(self._version, response, self._solution)

    def get_page(self, target_url: str) -> ReservationPage:
        """
        Retrieve a specific page of ReservationInstance records from the API.
        Request is executed immediately

        :param target_url: API-generated URL for the requested results page

        :returns: Page of ReservationInstance
        """
        response = self._version.domain.twilio.request("GET", target_url)
        return ReservationPage(self._version, response, self._solution)

    async def get_page_async(self, target_url: str) -> ReservationPage:
        """
        Asynchronously retrieve a specific page of ReservationInstance records from the API.
        Request is executed immediately

        :param target_url: API-generated URL for the requested results page

        :returns: Page of ReservationInstance
        """
        response = await self._version.domain.twilio.request_async("GET", target_url)
        return ReservationPage(self._version, response, self._solution)

    def get(self, sid: str) -> ReservationContext:
        """
        Constructs a ReservationContext

        :param sid: The SID of the TaskReservation resource to update.
        """
        return ReservationContext(
            self._version,
            workspace_sid=self._solution["workspace_sid"],
            task_sid=self._solution["task_sid"],
            sid=sid,
        )

    def __call__(self, sid: str) -> ReservationContext:
        """
        Constructs a ReservationContext

        :param sid: The SID of the TaskReservation resource to update.
        """
        return ReservationContext(
            self._version,
            workspace_sid=self._solution["workspace_sid"],
            task_sid=self._solution["task_sid"],
            sid=sid,
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Taskrouter.V1.ReservationList>"
