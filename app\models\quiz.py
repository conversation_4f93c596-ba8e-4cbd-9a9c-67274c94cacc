import json
from datetime import datetime
from app import db

class Quiz(db.Model):
    """Quiz modeli"""
    __tablename__ = 'quizzes'
    
    id = db.<PERSON>umn(db.Integer, primary_key=True)
    training_id = db.<PERSON>umn(db.<PERSON><PERSON><PERSON>, db.<PERSON>('trainings.id'), nullable=False)
    ad = db.Column(db.String(200), nullable=False)
    aciklama = db.Column(db.Text)
    questions_json = db.Column(db.Text, nullable=False)  # JSON string olarak sorular
    gecer_notu = db.Column(db.Float, default=60.0, nullable=False)
    aktif_mi = db.Column(db.<PERSON>, default=True, nullable=False)
    olusturulma_tarihi = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Quiz'i oluşturan kullanıcı
    olusturan_id = db.Column(db.<PERSON><PERSON><PERSON>, db.<PERSON>('users.id'), nullable=False)
    olusturan = db.relationship('User', backref='olusturulan_quizler')
    
    # <PERSON>liş<PERSON><PERSON>
    attempts = db.relationship('QuizAttempt', backref='quiz', lazy='dynamic', cascade='all, delete-orphan')
    
    def __init__(self, **kwargs):
        super(Quiz, self).__init__(**kwargs)
    
    @property
    def questions(self):
        """Soruları Python objesi olarak döndür"""
        try:
            return json.loads(self.questions_json) if self.questions_json else []
        except json.JSONDecodeError:
            return []
    
    @questions.setter
    def questions(self, value):
        """Soruları JSON string olarak kaydet"""
        self.questions_json = json.dumps(value, ensure_ascii=False)
    
    @property
    def soru_sayisi(self):
        """Toplam soru sayısı"""
        return len(self.questions)
    
    @property
    def deneme_sayisi(self):
        """Toplam deneme sayısı"""
        return self.attempts.count()
    
    @property
    def basarili_deneme_sayisi(self):
        """Başarılı deneme sayısı"""
        return self.attempts.filter_by(gecme_durumu=True).count()
    
    @property
    def basari_orani(self):
        """Başarı oranı"""
        if self.deneme_sayisi == 0:
            return 0
        return (self.basarili_deneme_sayisi / self.deneme_sayisi) * 100
    
    @property
    def ortalama_skor(self):
        """Ortalama skor"""
        attempts = self.attempts.all()
        if not attempts:
            return 0
        return sum(attempt.skor for attempt in attempts) / len(attempts)
    
    def calculate_score(self, answers):
        """Verilen cevaplara göre skoru hesapla"""
        if not self.questions or not answers:
            return 0
        
        correct_count = 0
        total_questions = len(self.questions)
        
        for i, question in enumerate(self.questions):
            question_id = str(i)
            if question_id in answers:
                user_answer = answers[question_id]
                correct_answer = question.get('correct_answer', '')
                if user_answer == correct_answer:
                    correct_count += 1
        
        if total_questions == 0:
            return 0
        
        return (correct_count / total_questions) * 100
    
    def is_passing_score(self, score):
        """Geçer not kontrolü"""
        return score >= self.gecer_notu
    
    def get_user_attempt(self, user_id):
        """Kullanıcının denemesini getir"""
        return self.attempts.filter_by(user_id=user_id).first()
    
    def has_user_attempted(self, user_id):
        """Kullanıcı daha önce deneme yaptı mı"""
        return self.get_user_attempt(user_id) is not None
    
    def __repr__(self):
        return f'<Quiz {self.ad}>'
    
    def to_dict(self, include_questions=False):
        """Quiz bilgilerini dictionary olarak döndür"""
        data = {
            'id': self.id,
            'training_id': self.training_id,
            'ad': self.ad,
            'aciklama': self.aciklama,
            'gecer_notu': self.gecer_notu,
            'aktif_mi': self.aktif_mi,
            'olusturulma_tarihi': self.olusturulma_tarihi.isoformat() if self.olusturulma_tarihi else None,
            'soru_sayisi': self.soru_sayisi,
            'deneme_sayisi': self.deneme_sayisi,
            'basarili_deneme_sayisi': self.basarili_deneme_sayisi,
            'basari_orani': self.basari_orani,
            'ortalama_skor': self.ortalama_skor
        }
        
        if include_questions:
            data['questions'] = self.questions
            
        return data


class QuizAttempt(db.Model):
    """Quiz deneme modeli"""
    __tablename__ = 'quiz_attempts'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    quiz_id = db.Column(db.Integer, db.ForeignKey('quizzes.id'), nullable=False)
    skor = db.Column(db.Float, nullable=False)
    gecme_durumu = db.Column(db.Boolean, nullable=False)
    cevaplar_json = db.Column(db.Text)  # Kullanıcının cevapları JSON olarak
    deneme_tarihi = db.Column(db.DateTime, default=datetime.utcnow)
    tamamlandi_mi = db.Column(db.Boolean, default=True, nullable=False)
    
    # Benzersiz constraint - her kullanıcı her quiz'e sadece bir kez deneme yapabilir
    __table_args__ = (db.UniqueConstraint('user_id', 'quiz_id', name='unique_user_quiz_attempt'),)
    
    def __init__(self, **kwargs):
        super(QuizAttempt, self).__init__(**kwargs)
    
    @property
    def cevaplar(self):
        """Cevapları Python objesi olarak döndür"""
        try:
            return json.loads(self.cevaplar_json) if self.cevaplar_json else {}
        except json.JSONDecodeError:
            return {}
    
    @cevaplar.setter
    def cevaplar(self, value):
        """Cevapları JSON string olarak kaydet"""
        self.cevaplar_json = json.dumps(value, ensure_ascii=False)
    
    @property
    def gecti_mi(self):
        """Geçti mi"""
        return self.gecme_durumu
    
    @property
    def skor_yuzde(self):
        """Skor yüzde olarak"""
        return f"{self.skor:.1f}%"
    
    def __repr__(self):
        return f'<QuizAttempt {self.user.tam_ad} - {self.quiz.ad} - {self.skor}%>'
    
    def to_dict(self):
        """Deneme bilgilerini dictionary olarak döndür"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'quiz_id': self.quiz_id,
            'skor': self.skor,
            'gecme_durumu': self.gecme_durumu,
            'deneme_tarihi': self.deneme_tarihi.isoformat() if self.deneme_tarihi else None,
            'tamamlandi_mi': self.tamamlandi_mi,
            'user_name': self.user.tam_ad if self.user else None,
            'quiz_name': self.quiz.ad if self.quiz else None,
            'skor_yuzde': self.skor_yuzde
        }
