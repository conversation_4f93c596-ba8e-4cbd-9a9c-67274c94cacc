{% extends "base.html" %}

{% block title %}Quiz Sonuçları - {{ quiz.ad }}{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-chart-bar"></i> Quiz Sonuçları
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('training.detail', training_id=quiz.training_id) }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> Eğitime Dön
            </a>
            <a href="{{ url_for('quiz.edit_quiz', quiz_id=quiz.id) }}" class="btn btn-outline-warning">
                <i class="fas fa-edit"></i> Quiz'i Düzenle
            </a>
        </div>
        <button class="btn btn-outline-success" onclick="exportResults()">
            <i class="fas fa-download"></i> Sonuçları İndir
        </button>
    </div>
</div>

<!-- Quiz Bilgileri -->
<div class="row mb-4">
    <div class="col-lg-8">
        <div class="card shadow">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-info-circle"></i> Quiz Bilgileri</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>{{ quiz.ad }}</h6>
                        {% if quiz.aciklama %}
                        <p class="text-muted">{{ quiz.aciklama }}</p>
                        {% endif %}
                        <p><strong>Eğitim:</strong> {{ quiz.training.ad }}</p>
                        <p><strong>Geçer Not:</strong> {{ quiz.gecer_notu }}%</p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>Soru Sayısı:</strong> {{ quiz.soru_sayisi }}</p>
                        <p><strong>Oluşturulma Tarihi:</strong> {{ quiz.olusturulma_tarihi.strftime('%d.%m.%Y %H:%M') }}</p>
                        <p><strong>Oluşturan:</strong> {{ quiz.olusturan.tam_ad }}</p>
                        <p><strong>Durum:</strong> 
                            <span class="badge {{ 'bg-success' if quiz.aktif_mi else 'bg-secondary' }}">
                                {{ 'Aktif' if quiz.aktif_mi else 'Pasif' }}
                            </span>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card shadow">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-chart-pie"></i> Genel İstatistikler</h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6 mb-3">
                        <div class="h4 mb-0 text-primary">{{ quiz.deneme_sayisi }}</div>
                        <small class="text-muted">Toplam Deneme</small>
                    </div>
                    <div class="col-6 mb-3">
                        <div class="h4 mb-0 text-success">{{ quiz.basarili_deneme_sayisi }}</div>
                        <small class="text-muted">Başarılı</small>
                    </div>
                    <div class="col-6">
                        <div class="h4 mb-0 text-info">{{ "%.1f"|format(quiz.basari_orani) }}%</div>
                        <small class="text-muted">Başarı Oranı</small>
                    </div>
                    <div class="col-6">
                        <div class="h4 mb-0 text-warning">{{ "%.1f"|format(quiz.ortalama_skor) }}%</div>
                        <small class="text-muted">Ortalama Skor</small>
                    </div>
                </div>
                
                <hr>
                
                <!-- Skor Dağılımı -->
                <h6 class="mb-3">Skor Dağılımı</h6>
                {% set score_ranges = [
                    (90, 100, 'Mükemmel', 'success'),
                    (80, 89, 'İyi', 'info'),
                    (70, 79, 'Orta', 'warning'),
                    (60, 69, 'Geçer', 'secondary'),
                    (0, 59, 'Yetersiz', 'danger')
                ] %}
                
                {% for min_score, max_score, label, color in score_ranges %}
                {% set count = attempts|selectattr('skor', '>=', min_score)|selectattr('skor', '<=', max_score)|list|length %}
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span class="badge bg-{{ color }}">{{ label }}</span>
                    <span>{{ count }} kişi</span>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
</div>

<!-- Sonuçlar Tablosu -->
<div class="card shadow">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            <i class="fas fa-list"></i> Katılımcı Sonuçları ({{ attempts|length }})
        </h5>
        <div class="btn-group btn-group-sm">
            <button class="btn btn-outline-primary" onclick="sortTable('name')">
                <i class="fas fa-sort-alpha-down"></i> Ada Göre
            </button>
            <button class="btn btn-outline-success" onclick="sortTable('score')">
                <i class="fas fa-sort-numeric-down"></i> Skora Göre
            </button>
            <button class="btn btn-outline-info" onclick="sortTable('date')">
                <i class="fas fa-sort-numeric-down"></i> Tarihe Göre
            </button>
        </div>
    </div>
    <div class="card-body">
        {% if attempts %}
        <!-- Filtreler -->
        <div class="row mb-3">
            <div class="col-md-4">
                <input type="text" class="form-control" id="searchFilter" placeholder="Katılımcı ara...">
            </div>
            <div class="col-md-3">
                <select class="form-select" id="statusFilter">
                    <option value="">Tüm Sonuçlar</option>
                    <option value="passed">Geçenler</option>
                    <option value="failed">Kalanlar</option>
                </select>
            </div>
            <div class="col-md-3">
                <select class="form-select" id="scoreRangeFilter">
                    <option value="">Tüm Skorlar</option>
                    <option value="90-100">90-100%</option>
                    <option value="80-89">80-89%</option>
                    <option value="70-79">70-79%</option>
                    <option value="60-69">60-69%</option>
                    <option value="0-59">0-59%</option>
                </select>
            </div>
            <div class="col-md-2">
                <button class="btn btn-outline-secondary w-100" onclick="clearFilters()">
                    <i class="fas fa-times"></i> Temizle
                </button>
            </div>
        </div>
        
        <div class="table-responsive">
            <table class="table table-hover" id="resultsTable">
                <thead>
                    <tr>
                        <th>Sıra</th>
                        <th>Katılımcı</th>
                        <th>E-posta</th>
                        <th>Skor</th>
                        <th>Durum</th>
                        <th>Deneme Tarihi</th>
                        <th>İşlemler</th>
                    </tr>
                </thead>
                <tbody>
                    {% for attempt in attempts %}
                    <tr data-name="{{ attempt.user.tam_ad.lower() }}" 
                        data-score="{{ attempt.skor }}" 
                        data-status="{{ 'passed' if attempt.gecme_durumu else 'failed' }}"
                        data-date="{{ attempt.deneme_tarihi.timestamp() }}">
                        <td>{{ loop.index }}</td>
                        <td>
                            <strong>{{ attempt.user.tam_ad }}</strong>
                        </td>
                        <td>{{ attempt.user.email }}</td>
                        <td>
                            <span class="badge {{ 'bg-success' if attempt.gecme_durumu else 'bg-danger' }} fs-6">
                                {{ "%.1f"|format(attempt.skor) }}%
                            </span>
                        </td>
                        <td>
                            {% if attempt.gecme_durumu %}
                            <i class="fas fa-check-circle text-success"></i> Geçti
                            {% else %}
                            <i class="fas fa-times-circle text-danger"></i> Kaldı
                            {% endif %}
                        </td>
                        <td>
                            <small class="text-muted">
                                {{ attempt.deneme_tarihi.strftime('%d.%m.%Y %H:%M') }}
                            </small>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="{{ url_for('quiz.result', attempt_id=attempt.id) }}" 
                                   class="btn btn-outline-info" target="_blank">
                                    <i class="fas fa-eye"></i> Detay
                                </a>
                                <button class="btn btn-outline-primary" 
                                        onclick="showAttemptDetails({{ attempt.id }})">
                                    <i class="fas fa-chart-line"></i> Analiz
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- Sayfalama -->
        <nav aria-label="Sonuçlar sayfalama" class="mt-4">
            <ul class="pagination justify-content-center" id="pagination">
                <!-- JavaScript ile oluşturulacak -->
            </ul>
        </nav>
        
        {% else %}
        <div class="text-center text-muted py-4">
            <i class="fas fa-chart-bar fa-3x mb-3"></i>
            <h5>Henüz Sonuç Yok</h5>
            <p>Bu quiz'i henüz kimse çözmemiş.</p>
            <a href="{{ url_for('training.detail', training_id=quiz.training_id) }}" class="btn btn-primary">
                <i class="fas fa-arrow-left"></i> Eğitime Dön
            </a>
        </div>
        {% endif %}
    </div>
</div>

<!-- Detay Modalı -->
<div class="modal fade" id="attemptDetailModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-chart-line"></i> Deneme Detayları
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="attemptDetailContent">
                <!-- İçerik AJAX ile yüklenecek -->
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let currentSort = 'score';
let currentOrder = 'desc';
let currentPage = 1;
let itemsPerPage = 10;

$(document).ready(function() {
    // Filtreleri uygula
    $('#searchFilter, #statusFilter, #scoreRangeFilter').on('change input', function() {
        applyFilters();
    });
    
    // Varsayılan sıralama
    sortTable('score');
});

function sortTable(column) {
    let tbody = $('#resultsTable tbody');
    let rows = tbody.find('tr').toArray();
    
    // Sıralama yönünü belirle
    if (currentSort === column) {
        currentOrder = currentOrder === 'asc' ? 'desc' : 'asc';
    } else {
        currentOrder = 'desc';
        currentSort = column;
    }
    
    rows.sort(function(a, b) {
        let aVal, bVal;
        
        switch(column) {
            case 'name':
                aVal = $(a).data('name');
                bVal = $(b).data('name');
                break;
            case 'score':
                aVal = parseFloat($(a).data('score'));
                bVal = parseFloat($(b).data('score'));
                break;
            case 'date':
                aVal = parseFloat($(a).data('date'));
                bVal = parseFloat($(b).data('date'));
                break;
        }
        
        if (currentOrder === 'asc') {
            return aVal > bVal ? 1 : -1;
        } else {
            return aVal < bVal ? 1 : -1;
        }
    });
    
    // Sıralanmış satırları tabloya ekle
    tbody.empty().append(rows);
    
    // Sıra numaralarını güncelle
    updateRowNumbers();
    
    // Sayfalama güncelle
    updatePagination();
}

function applyFilters() {
    let searchTerm = $('#searchFilter').val().toLowerCase();
    let statusFilter = $('#statusFilter').val();
    let scoreRangeFilter = $('#scoreRangeFilter').val();
    
    $('#resultsTable tbody tr').each(function() {
        let row = $(this);
        let name = row.data('name');
        let score = parseFloat(row.data('score'));
        let status = row.data('status');
        
        let showRow = true;
        
        // Arama filtresi
        if (searchTerm && !name.includes(searchTerm)) {
            showRow = false;
        }
        
        // Durum filtresi
        if (statusFilter && status !== statusFilter) {
            showRow = false;
        }
        
        // Skor aralığı filtresi
        if (scoreRangeFilter) {
            let [min, max] = scoreRangeFilter.split('-').map(Number);
            if (score < min || score > max) {
                showRow = false;
            }
        }
        
        if (showRow) {
            row.show();
        } else {
            row.hide();
        }
    });
    
    updateRowNumbers();
    updatePagination();
}

function updateRowNumbers() {
    let visibleRows = $('#resultsTable tbody tr:visible');
    visibleRows.each(function(index) {
        $(this).find('td:first').text(index + 1);
    });
}

function updatePagination() {
    // Basit sayfalama implementasyonu
    let visibleRows = $('#resultsTable tbody tr:visible');
    let totalItems = visibleRows.length;
    let totalPages = Math.ceil(totalItems / itemsPerPage);
    
    // Sayfalama HTML'i oluştur
    let paginationHtml = '';
    
    if (totalPages > 1) {
        for (let i = 1; i <= totalPages; i++) {
            paginationHtml += `
                <li class="page-item ${i === currentPage ? 'active' : ''}">
                    <a class="page-link" href="#" onclick="goToPage(${i})">${i}</a>
                </li>
            `;
        }
    }
    
    $('#pagination').html(paginationHtml);
    
    // Sayfa öğelerini göster/gizle
    showPageItems();
}

function goToPage(page) {
    currentPage = page;
    updatePagination();
}

function showPageItems() {
    let visibleRows = $('#resultsTable tbody tr:visible');
    let startIndex = (currentPage - 1) * itemsPerPage;
    let endIndex = startIndex + itemsPerPage;
    
    visibleRows.each(function(index) {
        if (index >= startIndex && index < endIndex) {
            $(this).show();
        } else {
            $(this).hide();
        }
    });
}

function clearFilters() {
    $('#searchFilter').val('');
    $('#statusFilter').val('');
    $('#scoreRangeFilter').val('');
    applyFilters();
}

function exportResults() {
    // CSV formatında sonuçları indir
    let csvContent = "data:text/csv;charset=utf-8,";
    csvContent += "Sıra,Ad Soyad,E-posta,Skor,Durum,Deneme Tarihi\n";
    
    $('#resultsTable tbody tr:visible').each(function(index) {
        let row = [];
        $(this).find('td').slice(0, 6).each(function() {
            let text = $(this).text().trim().replace(/\n/g, ' ').replace(/,/g, ';');
            row.push(text);
        });
        csvContent += row.join(',') + '\n';
    });
    
    let encodedUri = encodeURI(csvContent);
    let link = document.createElement("a");
    link.setAttribute("href", encodedUri);
    link.setAttribute("download", "{{ quiz.ad }}_sonuclari.csv");
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

function showAttemptDetails(attemptId) {
    $('#attemptDetailModal').modal('show');
    $('#attemptDetailContent').html('<div class="text-center"><i class="fas fa-spinner fa-spin"></i> Yükleniyor...</div>');
    
    // AJAX ile detayları yükle (şimdilik basit bir implementasyon)
    setTimeout(function() {
        $('#attemptDetailContent').html(`
            <div class="alert alert-info">
                <h6>Deneme Analizi</h6>
                <p>Bu özellik geliştirilme aşamasındadır. Şimdilik detaylı sonuç sayfasını kullanabilirsiniz.</p>
            </div>
        `);
    }, 1000);
}
</script>
{% endblock %}
