{% extends "base.html" %}

{% block title %}{{ training.ad }} - Eğitim Yönetim Sistemi{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-book"></i> {{ training.ad }}
        <span class="badge 
            {% if training.durum == 'planlanan' %}bg-info
            {% elif training.durum == 'devam_eden' %}bg-success
            {% else %}bg-secondary{% endif %} ms-2">
            {% if training.durum == 'planlanan' %}Planlanan
            {% elif training.durum == 'devam_eden' %}Devam Eden
            {% else %}Tamamlanan{% endif %}
        </span>
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('training.list_trainings') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> <PERSON><PERSON>
            </a>
            {% if current_user.can_manage_trainings() %}
            <a href="{{ url_for('training.edit_training', training_id=training.id) }}" class="btn btn-outline-warning">
                <i class="fas fa-edit"></i> Düzenle
            </a>
            {% endif %}
        </div>
    </div>
</div>

<!-- Eğitim Bilgileri -->
<div class="row mb-4">
    <div class="col-lg-8">
        <div class="card shadow">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-info-circle"></i> Eğitim Bilgileri</h5>
            </div>
            <div class="card-body">
                {% if training.aciklama %}
                <p class="mb-3">{{ training.aciklama }}</p>
                {% endif %}
                
                <div class="row">
                    <div class="col-md-6">
                        <strong>Başlangıç Tarihi:</strong><br>
                        <i class="fas fa-calendar-alt text-muted"></i> 
                        {{ training.baslangic_tarihi.strftime('%d.%m.%Y %H:%M') }}
                    </div>
                    <div class="col-md-6">
                        <strong>Bitiş Tarihi:</strong><br>
                        <i class="fas fa-calendar-alt text-muted"></i> 
                        {{ training.bitis_tarihi.strftime('%d.%m.%Y %H:%M') }}
                    </div>
                </div>
                
                <hr>

                <!-- Eğitmen Bilgileri -->
                <div class="row mb-3">
                    <div class="col-md-6">
                        <strong>Eğitmen:</strong><br>
                        <i class="fas fa-chalkboard-teacher text-muted"></i>
                        {{ training.egitmen_bilgisi.ad }}
                        {% if training.egitmen_bilgisi.tip == 'sistem_kullanicisi' %}
                        <span class="badge bg-primary ms-1">Sistem Eğitmeni</span>
                        {% else %}
                        <span class="badge bg-secondary ms-1">Dış Eğitmen</span>
                        {% endif %}

                        {% if training.egitmen_bilgisi.telefon %}
                        <br><small class="text-muted">
                            <i class="fas fa-phone"></i> {{ training.egitmen_bilgisi.telefon }}
                        </small>
                        {% endif %}

                        {% if training.egitmen_bilgisi.email %}
                        <br><small class="text-muted">
                            <i class="fas fa-envelope"></i> {{ training.egitmen_bilgisi.email }}
                        </small>
                        {% endif %}
                    </div>
                    <div class="col-md-6">
                        <strong>Eğitim Onay Durumu:</strong><br>
                        {% if training.egitim_verildi_mi %}
                        <span class="text-success">
                            <i class="fas fa-check-circle"></i> Eğitim Onaylandı
                        </span>
                        {% if training.egitmen_onay_tarihi %}
                        <br><small class="text-muted">
                            {{ training.egitmen_onay_tarihi.strftime('%d.%m.%Y %H:%M') }}
                        </small>
                        {% endif %}
                        {% else %}
                        <span class="text-warning">
                            <i class="fas fa-clock"></i> Eğitmen Onayı Bekleniyor
                        </span>
                        {% endif %}
                    </div>
                </div>

                <hr>

                <div class="row">
                    <div class="col-md-6">
                        <strong>Şirket:</strong><br>
                        {% if training.company %}
                        <i class="fas fa-building text-muted"></i>
                        {{ training.company.tam_ad }}
                        {% else %}
                        <span class="text-muted">Şirket atanmamış</span>
                        {% endif %}
                    </div>
                    <div class="col-md-6">
                        <strong>Oluşturan:</strong><br>
                        <i class="fas fa-user text-muted"></i>
                        {{ training.olusturan.tam_ad }}
                    </div>
                </div>

                <div class="row mt-2">
                    <div class="col-md-6">
                        <strong>Oluşturulma Tarihi:</strong><br>
                        <i class="fas fa-clock text-muted"></i>
                        {{ training.olusturulma_tarihi.strftime('%d.%m.%Y %H:%M') }}
                    </div>
                    <div class="col-md-6">
                        <strong>Durum:</strong><br>
                        <span class="badge
                            {% if training.durum == 'planlanan' %}bg-info
                            {% elif training.durum == 'devam_eden' %}bg-success
                            {% else %}bg-secondary{% endif %}">
                            {% if training.durum == 'planlanan' %}Planlanan
                            {% elif training.durum == 'devam_eden' %}Devam Eden
                            {% else %}Tamamlanan{% endif %}
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card shadow">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-chart-bar"></i> İstatistikler</h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6 mb-3">
                        <div class="h4 mb-0 text-primary">{{ training.katilimci_sayisi }}</div>
                        <small class="text-muted">Toplam Katılımcı</small>
                    </div>
                    <div class="col-6 mb-3">
                        <div class="h4 mb-0 text-success">{{ training.teyit_eden_sayisi }}</div>
                        <small class="text-muted">Teyit Eden</small>
                    </div>
                    <div class="col-12">
                        <div class="h4 mb-0 text-info">{{ "%.1f"|format(training.teyit_orani) }}%</div>
                        <small class="text-muted">Teyit Oranı</small>
                    </div>
                </div>
                
                {% if current_user.can_manage_trainings() %}
                <hr>
                <div class="d-grid gap-2">
                    <a href="{{ url_for('training.manage_participants', training_id=training.id) }}" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-users"></i> Katılımcı Yönet
                    </a>
                    <a href="{{ url_for('quiz.create_quiz', training_id=training.id) }}" class="btn btn-outline-success btn-sm">
                        <i class="fas fa-plus"></i> Quiz Oluştur
                    </a>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Bildirim Gönderme -->
        {% if current_user.can_manage_trainings() and enrollments %}
        <div class="card shadow mt-3">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-bell"></i> Bildirim Gönder</h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <!-- Eğitmen Onay SMS'i -->
                    {% if not training.egitim_verildi_mi and training.egitmen_iletisim_telefon %}
                    <button class="btn btn-outline-warning btn-sm" onclick="sendInstructorConfirmationSMS()">
                        <i class="fas fa-chalkboard-teacher"></i> Eğitmen Onay SMS
                        <small class="d-block">Eğitmene eğitim teslim onayı için SMS gönder</small>
                    </button>
                    {% endif %}

                    <!-- Manuel Eğitmen Onayı (Sadece Admin) -->
                    {% if current_user.is_admin %}
                    <button class="btn btn-outline-{{ 'danger' if training.egitim_verildi_mi else 'success' }} btn-sm"
                            onclick="toggleInstructorConfirmation()">
                        <i class="fas fa-{{ 'times' if training.egitim_verildi_mi else 'check' }}"></i>
                        {{ 'Eğitmen Onayını İptal Et' if training.egitim_verildi_mi else 'Manuel Eğitmen Onayı Ver' }}
                        <small class="d-block">Admin yetkisiyle manuel onay</small>
                    </button>
                    {% endif %}

                    <button class="btn btn-outline-info btn-sm" onclick="sendParticipationSMS()">
                        <i class="fas fa-sms"></i> Katılım Teyidi SMS
                        <small class="d-block">Katılımcılara katılım teyidi için SMS gönder</small>
                    </button>

                    {% if quizzes %}
                    <div class="dropdown">
                        <button class="btn btn-outline-warning btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-link"></i> Quiz Linki SMS
                        </button>
                        <ul class="dropdown-menu">
                            {% for quiz in quizzes %}
                            <li>
                                <a class="dropdown-item" href="#" onclick="sendQuizSMS({{ quiz.id }}, '{{ quiz.ad }}')">
                                    {{ quiz.ad }}
                                </a>
                            </li>
                            {% endfor %}
                        </ul>
                    </div>
                    {% endif %}

                    <button class="btn btn-outline-success btn-sm" onclick="sendEvaluationEmail()">
                        <i class="fas fa-envelope"></i> Değerlendirme Anketi Email
                        <small class="d-block">Yöneticilere değerlendirme anketi gönder</small>
                    </button>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>

<!-- Katılımcılar -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-users"></i> Katılımcılar ({{ enrollments|length }})</h5>
                {% if current_user.can_manage_trainings() %}
                <a href="{{ url_for('training.manage_participants', training_id=training.id) }}" class="btn btn-outline-primary btn-sm">
                    <i class="fas fa-user-plus"></i> Katılımcı Ekle
                </a>
                {% endif %}
            </div>
            <div class="card-body">
                {% if enrollments %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Ad Soyad</th>
                                <th>E-posta</th>
                                <th>Telefon</th>
                                <th>Durum</th>
                                <th>Kayıt Tarihi</th>
                                {% if current_user.can_manage_trainings() %}
                                <th>İşlemler</th>
                                {% endif %}
                            </tr>
                        </thead>
                        <tbody>
                            {% for enrollment in enrollments %}
                            <tr>
                                <td>{{ enrollment.user.tam_ad }}</td>
                                <td>{{ enrollment.user.email }}</td>
                                <td>{{ enrollment.user.telefon_numarasi }}</td>
                                <td>
                                    <span class="badge 
                                        {% if enrollment.katilim_teyidi_durumu == 'teyit_edildi' %}bg-success
                                        {% elif enrollment.katilim_teyidi_durumu == 'reddedildi' %}bg-danger
                                        {% else %}bg-warning{% endif %}">
                                        {% if enrollment.katilim_teyidi_durumu == 'teyit_edildi' %}Teyit Edildi
                                        {% elif enrollment.katilim_teyidi_durumu == 'reddedildi' %}Reddedildi
                                        {% else %}Beklemede{% endif %}
                                    </span>
                                </td>
                                <td>{{ enrollment.kayit_tarihi.strftime('%d.%m.%Y') }}</td>
                                {% if current_user.can_manage_trainings() %}
                                <td>
                                    <button class="btn btn-outline-danger btn-sm" 
                                            onclick="removeParticipant({{ training.id }}, {{ enrollment.user.id }}, '{{ enrollment.user.tam_ad }}')">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                                {% endif %}
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center text-muted">
                    <i class="fas fa-users fa-3x mb-3"></i>
                    <p>Henüz katılımcı eklenmemiş.</p>
                    {% if current_user.can_manage_trainings() %}
                    <a href="{{ url_for('training.manage_participants', training_id=training.id) }}" class="btn btn-primary">
                        <i class="fas fa-user-plus"></i> İlk Katılımcıyı Ekle
                    </a>
                    {% endif %}
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Quizler -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-question-circle"></i> Quizler ({{ quizzes|length }})</h5>
                {% if current_user.can_create_quizzes() %}
                <a href="{{ url_for('quiz.create_quiz', training_id=training.id) }}" class="btn btn-outline-success btn-sm">
                    <i class="fas fa-plus"></i> Quiz Oluştur
                </a>
                {% endif %}
            </div>
            <div class="card-body">
                {% if quizzes %}
                <div class="row">
                    {% for quiz in quizzes %}
                    <div class="col-md-6 mb-3">
                        <div class="card border-left-primary">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <h6 class="mb-1">{{ quiz.ad }}</h6>
                                        <p class="text-muted small mb-2">{{ quiz.aciklama or 'Açıklama yok' }}</p>
                                        <small class="text-muted">
                                            {{ quiz.soru_sayisi }} soru • Geçer not: {{ quiz.gecer_notu }}%
                                        </small>
                                    </div>
                                    <span class="badge {{ 'bg-success' if quiz.aktif_mi else 'bg-secondary' }}">
                                        {{ 'Aktif' if quiz.aktif_mi else 'Pasif' }}
                                    </span>
                                </div>
                                <div class="mt-3">
                                    {% if current_user.is_participant and quiz.aktif_mi %}
                                        {% if quiz.has_user_attempted(current_user.id) %}
                                        <a href="{{ url_for('quiz.result', attempt_id=quiz.get_user_attempt(current_user.id).id) }}" class="btn btn-outline-info btn-sm">
                                            <i class="fas fa-eye"></i> Sonucu Gör
                                        </a>
                                        {% else %}
                                        <a href="{{ url_for('quiz.take_quiz', quiz_id=quiz.id) }}" class="btn btn-primary btn-sm">
                                            <i class="fas fa-play"></i> Quiz'i Çöz
                                        </a>
                                        {% endif %}
                                    {% endif %}
                                    
                                    {% if current_user.can_manage_trainings() %}
                                    <a href="{{ url_for('quiz.edit_quiz', quiz_id=quiz.id) }}" class="btn btn-outline-warning btn-sm">
                                        <i class="fas fa-edit"></i> Düzenle
                                    </a>
                                    <a href="{{ url_for('quiz.quiz_results', quiz_id=quiz.id) }}" class="btn btn-outline-info btn-sm">
                                        <i class="fas fa-chart-bar"></i> Sonuçlar
                                    </a>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="text-center text-muted">
                    <i class="fas fa-question-circle fa-3x mb-3"></i>
                    <p>Henüz quiz oluşturulmamış.</p>
                    {% if current_user.can_create_quizzes() %}
                    <a href="{{ url_for('quiz.create_quiz', training_id=training.id) }}" class="btn btn-success">
                        <i class="fas fa-plus"></i> İlk Quiz'i Oluştur
                    </a>
                    {% endif %}
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Eğitim Materyalleri -->
    <div class="col-lg-6">
        <div class="card shadow">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-folder-open"></i> Eğitim Materyalleri
                    <span class="badge bg-primary ms-2">{{ training.materyal_sayisi }}</span>
                </h5>
                {% if current_user.can_manage_trainings() or (current_user.is_instructor and training.egitmen_user_id == current_user.id) %}
                <a href="{{ url_for('material.upload_material', training_id=training.id) }}" class="btn btn-sm btn-outline-primary">
                    <i class="fas fa-upload"></i> Yükle
                </a>
                {% endif %}
            </div>
            <div class="card-body">
                {% if training.materyal_sayisi > 0 %}
                <div id="materialsList">
                    <!-- Materyaller AJAX ile yüklenecek -->
                    <div class="text-center">
                        <div class="spinner-border spinner-border-sm" role="status">
                            <span class="visually-hidden">Yükleniyor...</span>
                        </div>
                        <small class="text-muted ms-2">Materyaller yükleniyor...</small>
                    </div>
                </div>
                <div class="text-center mt-3">
                    <a href="{{ url_for('material.list_materials', training_id=training.id) }}" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-eye"></i> Tüm Materyalleri Görüntüle
                    </a>
                </div>
                {% else %}
                <div class="text-center text-muted py-4">
                    <i class="fas fa-folder-open fa-2x mb-2"></i>
                    <p class="mb-2">Henüz materyal yüklenmemiş</p>
                    {% if current_user.can_manage_trainings() or (current_user.is_instructor and training.egitmen_user_id == current_user.id) %}
                    <a href="{{ url_for('material.upload_material', training_id=training.id) }}" class="btn btn-sm btn-primary">
                        <i class="fas fa-upload"></i> İlk Materyali Yükle
                    </a>
                    {% endif %}
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Sayfa yüklendiğinde materyalleri getir
$(document).ready(function() {
    {% if training.materyal_sayisi > 0 %}
    loadMaterials();
    {% endif %}
});

// Materyalleri AJAX ile yükle
function loadMaterials() {
    fetch(`/material/api/training/{{ training.id }}/materials`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayMaterials(data.materials);
            } else {
                $('#materialsList').html('<div class="text-danger">Materyaller yüklenemedi.</div>');
            }
        })
        .catch(error => {
            $('#materialsList').html('<div class="text-danger">Bir hata oluştu.</div>');
        });
}

// Materyalleri göster (sadece ilk 3 tanesi)
function displayMaterials(materials) {
    let html = '';
    const displayMaterials = materials.slice(0, 3);

    displayMaterials.forEach(material => {
        html += `
            <div class="d-flex align-items-center mb-2 p-2 border rounded">
                <i class="${material.file_icon} ${material.file_color} me-2"></i>
                <div class="flex-grow-1">
                    <div class="fw-bold small text-truncate" style="max-width: 200px;" title="${material.file_name}">
                        ${material.file_name}
                    </div>
                    <small class="text-muted">
                        ${material.file_size_formatted} • ${material.uploaded_by}
                    </small>
                </div>
                <div class="btn-group btn-group-sm">
                    ${material.is_viewable_online ?
                        `<a href="/material/${material.id}/view" class="btn btn-outline-primary btn-sm" target="_blank" title="Görüntüle">
                            <i class="fas fa-eye"></i>
                        </a>` : ''}
                    ${material.is_video ?
                        `<button class="btn btn-outline-info btn-sm" onclick="playVideoInline(${material.id}, '${material.file_name}')" title="Oynat">
                            <i class="fas fa-play"></i>
                        </button>` : ''}
                    <a href="/material/${material.id}/download" class="btn btn-outline-success btn-sm" title="İndir">
                        <i class="fas fa-download"></i>
                    </a>
                </div>
            </div>
        `;
    });

    if (materials.length > 3) {
        html += `<div class="text-center mt-2">
            <small class="text-muted">ve ${materials.length - 3} materyal daha...</small>
        </div>`;
    }

    $('#materialsList').html(html);
}

// Video oynatma fonksiyonu
function playVideoInline(materialId, fileName) {
    // Video modal'ı aç (eğer varsa)
    if ($('#videoModal').length) {
        $('#videoModalTitle').text(fileName);
        $('#videoSource').attr('src', `/material/${materialId}/view`);
        $('#videoPlayer')[0].load();
        $('#videoModal').modal('show');
    } else {
        // Yeni pencerede aç
        window.open(`/material/${materialId}/view`, '_blank');
    }
}
</script>

{% if current_user.can_manage_trainings() %}
<script>
function removeParticipant(trainingId, userId, userName) {
    if (confirm(`${userName} kullanıcısını bu eğitimden çıkarmak istediğinizden emin misiniz?`)) {
        fetch(`/training/${trainingId}/remove-participant/${userId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(data.message);
                location.reload();
            } else {
                alert('Hata: ' + data.message);
            }
        })
        .catch(error => {
            alert('Bir hata oluştu: ' + error);
        });
    }
}

function sendParticipationSMS() {
    if (confirm('Tüm katılımcılara katılım teyidi SMS\'i göndermek istediğinizden emin misiniz?')) {
        const button = event.target;
        const originalText = button.innerHTML;
        button.disabled = true;
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Gönderiliyor...';

        fetch(`/training/{{ training.id }}/send-participation-sms`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('✅ ' + data.message);
                if (data.details && data.details.errors.length > 0) {
                    console.log('Hatalar:', data.details.errors);
                }
            } else {
                alert('❌ Hata: ' + data.message);
            }
        })
        .catch(error => {
            alert('❌ Bir hata oluştu: ' + error);
        })
        .finally(() => {
            button.disabled = false;
            button.innerHTML = originalText;
        });
    }
}

function sendQuizSMS(quizId, quizName) {
    if (confirm(`"${quizName}" quiz linki SMS'ini tüm katılımcılara göndermek istediğinizden emin misiniz?`)) {
        fetch(`/training/{{ training.id }}/send-quiz-sms/${quizId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('✅ ' + data.message);
                if (data.details && data.details.errors.length > 0) {
                    console.log('Hatalar:', data.details.errors);
                }
            } else {
                alert('❌ Hata: ' + data.message);
            }
        })
        .catch(error => {
            alert('❌ Bir hata oluştu: ' + error);
        });
    }
}

function sendEvaluationEmail() {
    if (confirm('Katılımcıların yöneticilerine değerlendirme anketi email\'i göndermek istediğinizden emin misiniz?')) {
        const button = event.target;
        const originalText = button.innerHTML;
        button.disabled = true;
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Gönderiliyor...';

        fetch(`/training/{{ training.id }}/send-evaluation-email`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('✅ ' + data.message);
                if (data.details && data.details.errors.length > 0) {
                    console.log('Hatalar:', data.details.errors);
                }
            } else {
                alert('❌ Hata: ' + data.message);
            }
        })
        .catch(error => {
            alert('❌ Bir hata oluştu: ' + error);
        })
        .finally(() => {
            button.disabled = false;
            button.innerHTML = originalText;
        });
    }
}

function sendInstructorConfirmationSMS() {
    if (confirm('Eğitmene eğitim teslim onayı SMS\'i göndermek istediğinizden emin misiniz?')) {
        const button = event.target;
        const originalText = button.innerHTML;
        button.disabled = true;
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Gönderiliyor...';

        fetch(`/training/{{ training.id }}/instructor-confirmation`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('✅ ' + data.message);
            } else {
                alert('❌ Hata: ' + data.message);
            }
        })
        .catch(error => {
            alert('❌ Bir hata oluştu: ' + error);
        })
        .finally(() => {
            button.disabled = false;
            button.innerHTML = originalText;
        });
    }
}

function toggleInstructorConfirmation() {
    const currentStatus = {{ 'true' if training.egitim_verildi_mi else 'false' }};
    const action = currentStatus ? 'iptal etmek' : 'vermek';

    if (confirm(`Eğitmen onayını ${action} istediğinizden emin misiniz?`)) {
        const button = event.target;
        const originalText = button.innerHTML;
        button.disabled = true;
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> İşleniyor...';

        fetch(`/training/{{ training.id }}/instructor-status`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('✅ ' + data.message);
                location.reload(); // Sayfayı yenile
            } else {
                alert('❌ Hata: ' + data.message);
            }
        })
        .catch(error => {
            alert('❌ Bir hata oluştu: ' + error);
        })
        .finally(() => {
            button.disabled = false;
            button.innerHTML = originalText;
        });
    }
}
</script>
{% endif %}
{% endblock %}
