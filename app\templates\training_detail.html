{% extends "base.html" %}

{% block title %}{{ training.ad }} - Eğitim Yönetim Sistemi{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-book"></i> {{ training.ad }}
        <span class="badge 
            {% if training.durum == 'planlanan' %}bg-info
            {% elif training.durum == 'devam_eden' %}bg-success
            {% else %}bg-secondary{% endif %} ms-2">
            {% if training.durum == 'planlanan' %}Planlanan
            {% elif training.durum == 'devam_eden' %}Devam Eden
            {% else %}Tamamlanan{% endif %}
        </span>
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('training.list_trainings') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> <PERSON><PERSON>
            </a>
            {% if current_user.can_manage_trainings() %}
            <a href="{{ url_for('training.edit_training', training_id=training.id) }}" class="btn btn-outline-warning">
                <i class="fas fa-edit"></i> Düzenle
            </a>
            {% endif %}
        </div>
    </div>
</div>

<!-- Eğitim Bilgileri -->
<div class="row mb-4">
    <div class="col-lg-8">
        <div class="card shadow">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-info-circle"></i> Eğitim Bilgileri</h5>
            </div>
            <div class="card-body">
                {% if training.aciklama %}
                <p class="mb-3">{{ training.aciklama }}</p>
                {% endif %}
                
                <div class="row">
                    <div class="col-md-6">
                        <strong>Başlangıç Tarihi:</strong><br>
                        <i class="fas fa-calendar-alt text-muted"></i> 
                        {{ training.baslangic_tarihi.strftime('%d.%m.%Y %H:%M') }}
                    </div>
                    <div class="col-md-6">
                        <strong>Bitiş Tarihi:</strong><br>
                        <i class="fas fa-calendar-alt text-muted"></i> 
                        {{ training.bitis_tarihi.strftime('%d.%m.%Y %H:%M') }}
                    </div>
                </div>
                
                <hr>
                
                <div class="row">
                    <div class="col-md-6">
                        <strong>Oluşturan:</strong><br>
                        <i class="fas fa-user text-muted"></i> 
                        {{ training.olusturan.tam_ad }}
                    </div>
                    <div class="col-md-6">
                        <strong>Oluşturulma Tarihi:</strong><br>
                        <i class="fas fa-clock text-muted"></i> 
                        {{ training.olusturulma_tarihi.strftime('%d.%m.%Y %H:%M') }}
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card shadow">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-chart-bar"></i> İstatistikler</h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6 mb-3">
                        <div class="h4 mb-0 text-primary">{{ training.katilimci_sayisi }}</div>
                        <small class="text-muted">Toplam Katılımcı</small>
                    </div>
                    <div class="col-6 mb-3">
                        <div class="h4 mb-0 text-success">{{ training.teyit_eden_sayisi }}</div>
                        <small class="text-muted">Teyit Eden</small>
                    </div>
                    <div class="col-12">
                        <div class="h4 mb-0 text-info">{{ "%.1f"|format(training.teyit_orani) }}%</div>
                        <small class="text-muted">Teyit Oranı</small>
                    </div>
                </div>
                
                {% if current_user.can_manage_trainings() %}
                <hr>
                <div class="d-grid gap-2">
                    <a href="{{ url_for('training.manage_participants', training_id=training.id) }}" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-users"></i> Katılımcı Yönet
                    </a>
                    <a href="{{ url_for('quiz.create_quiz', training_id=training.id) }}" class="btn btn-outline-success btn-sm">
                        <i class="fas fa-plus"></i> Quiz Oluştur
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Katılımcılar -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-users"></i> Katılımcılar ({{ enrollments|length }})</h5>
                {% if current_user.can_manage_trainings() %}
                <a href="{{ url_for('training.manage_participants', training_id=training.id) }}" class="btn btn-outline-primary btn-sm">
                    <i class="fas fa-user-plus"></i> Katılımcı Ekle
                </a>
                {% endif %}
            </div>
            <div class="card-body">
                {% if enrollments %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Ad Soyad</th>
                                <th>E-posta</th>
                                <th>Telefon</th>
                                <th>Durum</th>
                                <th>Kayıt Tarihi</th>
                                {% if current_user.can_manage_trainings() %}
                                <th>İşlemler</th>
                                {% endif %}
                            </tr>
                        </thead>
                        <tbody>
                            {% for enrollment in enrollments %}
                            <tr>
                                <td>{{ enrollment.user.tam_ad }}</td>
                                <td>{{ enrollment.user.email }}</td>
                                <td>{{ enrollment.user.telefon_numarasi }}</td>
                                <td>
                                    <span class="badge 
                                        {% if enrollment.katilim_teyidi_durumu == 'teyit_edildi' %}bg-success
                                        {% elif enrollment.katilim_teyidi_durumu == 'reddedildi' %}bg-danger
                                        {% else %}bg-warning{% endif %}">
                                        {% if enrollment.katilim_teyidi_durumu == 'teyit_edildi' %}Teyit Edildi
                                        {% elif enrollment.katilim_teyidi_durumu == 'reddedildi' %}Reddedildi
                                        {% else %}Beklemede{% endif %}
                                    </span>
                                </td>
                                <td>{{ enrollment.kayit_tarihi.strftime('%d.%m.%Y') }}</td>
                                {% if current_user.can_manage_trainings() %}
                                <td>
                                    <button class="btn btn-outline-danger btn-sm" 
                                            onclick="removeParticipant({{ training.id }}, {{ enrollment.user.id }}, '{{ enrollment.user.tam_ad }}')">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                                {% endif %}
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center text-muted">
                    <i class="fas fa-users fa-3x mb-3"></i>
                    <p>Henüz katılımcı eklenmemiş.</p>
                    {% if current_user.can_manage_trainings() %}
                    <a href="{{ url_for('training.manage_participants', training_id=training.id) }}" class="btn btn-primary">
                        <i class="fas fa-user-plus"></i> İlk Katılımcıyı Ekle
                    </a>
                    {% endif %}
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Quizler -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-question-circle"></i> Quizler ({{ quizzes|length }})</h5>
                {% if current_user.can_create_quizzes() %}
                <a href="{{ url_for('quiz.create_quiz', training_id=training.id) }}" class="btn btn-outline-success btn-sm">
                    <i class="fas fa-plus"></i> Quiz Oluştur
                </a>
                {% endif %}
            </div>
            <div class="card-body">
                {% if quizzes %}
                <div class="row">
                    {% for quiz in quizzes %}
                    <div class="col-md-6 mb-3">
                        <div class="card border-left-primary">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <h6 class="mb-1">{{ quiz.ad }}</h6>
                                        <p class="text-muted small mb-2">{{ quiz.aciklama or 'Açıklama yok' }}</p>
                                        <small class="text-muted">
                                            {{ quiz.soru_sayisi }} soru • Geçer not: {{ quiz.gecer_notu }}%
                                        </small>
                                    </div>
                                    <span class="badge {{ 'bg-success' if quiz.aktif_mi else 'bg-secondary' }}">
                                        {{ 'Aktif' if quiz.aktif_mi else 'Pasif' }}
                                    </span>
                                </div>
                                <div class="mt-3">
                                    {% if current_user.is_participant and quiz.aktif_mi %}
                                        {% if quiz.has_user_attempted(current_user.id) %}
                                        <a href="{{ url_for('quiz.result', attempt_id=quiz.get_user_attempt(current_user.id).id) }}" class="btn btn-outline-info btn-sm">
                                            <i class="fas fa-eye"></i> Sonucu Gör
                                        </a>
                                        {% else %}
                                        <a href="{{ url_for('quiz.take_quiz', quiz_id=quiz.id) }}" class="btn btn-primary btn-sm">
                                            <i class="fas fa-play"></i> Quiz'i Çöz
                                        </a>
                                        {% endif %}
                                    {% endif %}
                                    
                                    {% if current_user.can_manage_trainings() %}
                                    <a href="{{ url_for('quiz.edit_quiz', quiz_id=quiz.id) }}" class="btn btn-outline-warning btn-sm">
                                        <i class="fas fa-edit"></i> Düzenle
                                    </a>
                                    <a href="{{ url_for('quiz.quiz_results', quiz_id=quiz.id) }}" class="btn btn-outline-info btn-sm">
                                        <i class="fas fa-chart-bar"></i> Sonuçlar
                                    </a>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="text-center text-muted">
                    <i class="fas fa-question-circle fa-3x mb-3"></i>
                    <p>Henüz quiz oluşturulmamış.</p>
                    {% if current_user.can_create_quizzes() %}
                    <a href="{{ url_for('quiz.create_quiz', training_id=training.id) }}" class="btn btn-success">
                        <i class="fas fa-plus"></i> İlk Quiz'i Oluştur
                    </a>
                    {% endif %}
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
{% if current_user.can_manage_trainings() %}
<script>
function removeParticipant(trainingId, userId, userName) {
    if (confirm(`${userName} kullanıcısını bu eğitimden çıkarmak istediğinizden emin misiniz?`)) {
        fetch(`/training/${trainingId}/remove-participant/${userId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(data.message);
                location.reload();
            } else {
                alert('Hata: ' + data.message);
            }
        })
        .catch(error => {
            alert('Bir hata oluştu: ' + error);
        });
    }
}
</script>
{% endif %}
{% endblock %}
