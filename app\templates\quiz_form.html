{% extends "base.html" %}

{% block title %}
{% if quiz %}Quiz Düzenle{% else %}Ye<PERSON> Quiz{% endif %} - Eğitim Yönetim <PERSON>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        {% if quiz %}
        <i class="fas fa-edit"></i> Quiz Düzenle: {{ quiz.ad }}
        {% else %}
        <i class="fas fa-plus"></i> Yeni Quiz Oluştur
        {% endif %}
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{{ url_for('training.detail', training_id=training.id) }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left"></i> <PERSON><PERSON>
        </a>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card shadow">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle"></i> Quiz Bilgileri
                </h5>
            </div>
            <div class="card-body">
                <form id="quizForm" method="POST">
                    <!-- Temel Bilgiler -->
                    <div class="row mb-3">
                        <div class="col-md-8">
                            <label for="ad" class="form-label">Quiz Adı *</label>
                            <input type="text" class="form-control" id="ad" name="ad" 
                                   value="{{ quiz.ad if quiz else '' }}" required>
                        </div>
                        <div class="col-md-4">
                            <label for="gecer_notu" class="form-label">Geçer Not (%) *</label>
                            <input type="number" class="form-control" id="gecer_notu" name="gecer_notu" 
                                   min="0" max="100" step="0.1" 
                                   value="{{ quiz.gecer_notu if quiz else 60 }}" required>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="aciklama" class="form-label">Açıklama</label>
                        <textarea class="form-control" id="aciklama" name="aciklama" rows="3">{{ quiz.aciklama if quiz else '' }}</textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Eğitim</label>
                        <input type="text" class="form-control" value="{{ training.ad }}" readonly>
                    </div>
                    
                    <!-- Sorular Bölümü -->
                    <div class="mb-4">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h5><i class="fas fa-question-circle"></i> Quiz Soruları</h5>
                            <button type="button" class="btn btn-success add-btn" onclick="addQuestion()">
                                <i class="fas fa-plus"></i> Soru Ekle
                            </button>
                        </div>
                        
                        <div id="questionsContainer">
                            <!-- Sorular buraya dinamik olarak eklenecek -->
                        </div>
                        
                        <div id="noQuestionsMessage" class="alert alert-info">
                            <i class="fas fa-info-circle"></i> Henüz soru eklenmedi. "Soru Ekle" butonuna tıklayarak sorular ekleyin.
                        </div>
                    </div>
                    
                    <!-- Hidden field for questions JSON -->
                    <input type="hidden" id="questions_json" name="questions_json">
                    
                    <!-- Submit Buttons -->
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('training.detail', training_id=training.id) }}" class="btn btn-secondary">
                            <i class="fas fa-times"></i> İptal
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> 
                            {% if quiz %}Güncelle{% else %}Oluştur{% endif %}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card shadow">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-lightbulb"></i> Yardım</h6>
            </div>
            <div class="card-body">
                <h6>Quiz Oluşturma İpuçları:</h6>
                <ul class="small">
                    <li>Her soru için en az 2 seçenek ekleyin</li>
                    <li>Doğru cevabı işaretlemeyi unutmayın</li>
                    <li>Soru metinlerini açık ve anlaşılır yazın</li>
                    <li>Geçer notu genellikle %60-70 arası olmalıdır</li>
                </ul>
                
                <hr>
                
                <h6>Kısayollar:</h6>
                <ul class="small">
                    <li><kbd>Ctrl + S</kbd>: Kaydet</li>
                    <li><kbd>Esc</kbd>: İptal</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let questionCounter = 0;
let questions = [];

// Mevcut quiz varsa sorularını yükle
{% if quiz and quiz.questions %}
questions = {{ quiz.questions | tojsonfilter }};
{% endif %}

$(document).ready(function() {
    // Mevcut soruları yükle
    if (questions.length > 0) {
        questions.forEach(function(question, index) {
            addQuestion(question);
        });
    }
    
    updateQuestionsVisibility();
    
    // Form submit
    $('#quizForm').on('submit', function(e) {
        e.preventDefault();
        
        if (validateForm()) {
            collectQuestionsData();
            this.submit();
        }
    });
    
    // Keyboard shortcuts
    $(document).on('keydown', function(e) {
        if (e.ctrlKey && e.key === 's') {
            e.preventDefault();
            $('#quizForm').submit();
        } else if (e.key === 'Escape') {
            window.location.href = "{{ url_for('training.detail', training_id=training.id) }}";
        }
    });
});

function addQuestion(existingQuestion = null) {
    questionCounter++;
    const questionId = 'question_' + questionCounter;
    
    const questionHtml = `
        <div class="dynamic-form-section" id="${questionId}">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h6><i class="fas fa-question"></i> Soru ${questionCounter}</h6>
                <button type="button" class="btn btn-sm remove-btn" onclick="removeQuestion('${questionId}')">
                    <i class="fas fa-trash"></i> Sil
                </button>
            </div>
            
            <div class="mb-3">
                <label class="form-label">Soru Metni *</label>
                <textarea class="form-control question-text" rows="3" placeholder="Sorunuzu buraya yazın..." required>${existingQuestion ? existingQuestion.question : ''}</textarea>
            </div>
            
            <div class="mb-3">
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <label class="form-label">Seçenekler</label>
                    <button type="button" class="btn btn-sm add-btn" onclick="addOption('${questionId}')">
                        <i class="fas fa-plus"></i> Seçenek Ekle
                    </button>
                </div>
                <div class="options-container" id="${questionId}_options">
                    <!-- Seçenekler buraya eklenecek -->
                </div>
            </div>
        </div>
    `;
    
    $('#questionsContainer').append(questionHtml);
    
    // Mevcut seçenekleri ekle
    if (existingQuestion && existingQuestion.options) {
        existingQuestion.options.forEach(function(option) {
            addOption(questionId, option, option === existingQuestion.correct_answer);
        });
    } else {
        // Varsayılan olarak 2 boş seçenek ekle
        addOption(questionId);
        addOption(questionId);
    }
    
    updateQuestionsVisibility();
}

function removeQuestion(questionId) {
    if (confirm('Bu soruyu silmek istediğinizden emin misiniz?')) {
        $('#' + questionId).remove();
        updateQuestionsVisibility();
    }
}

function addOption(questionId, optionText = '', isCorrect = false) {
    const optionId = questionId + '_option_' + Date.now();
    const radioName = questionId + '_correct';
    
    const optionHtml = `
        <div class="quiz-option" id="${optionId}">
            <div class="input-group mb-2">
                <div class="input-group-text">
                    <input type="radio" name="${radioName}" value="${optionId}" ${isCorrect ? 'checked' : ''}>
                </div>
                <input type="text" class="form-control option-text" placeholder="Seçenek metni..." value="${optionText}" required>
                <button type="button" class="btn btn-outline-danger" onclick="removeOption('${optionId}')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
    `;
    
    $('#' + questionId + '_options').append(optionHtml);
}

function removeOption(optionId) {
    const optionsContainer = $('#' + optionId).parent();
    $('#' + optionId).remove();
    
    // En az 2 seçenek kalmalı
    if (optionsContainer.children().length < 2) {
        alert('Her soru için en az 2 seçenek olmalıdır.');
        addOption(optionsContainer.attr('id').replace('_options', ''));
    }
}

function updateQuestionsVisibility() {
    const hasQuestions = $('#questionsContainer .dynamic-form-section').length > 0;
    
    if (hasQuestions) {
        $('#noQuestionsMessage').hide();
        $('#questionsContainer').show();
    } else {
        $('#noQuestionsMessage').show();
        $('#questionsContainer').hide();
    }
}

function validateForm() {
    const questions = $('#questionsContainer .dynamic-form-section');
    
    if (questions.length === 0) {
        alert('En az bir soru eklemelisiniz.');
        return false;
    }
    
    let isValid = true;
    
    questions.each(function() {
        const questionText = $(this).find('.question-text').val().trim();
        const options = $(this).find('.option-text');
        const correctAnswer = $(this).find('input[type="radio"]:checked');
        
        if (!questionText) {
            alert('Tüm sorular için soru metni girmelisiniz.');
            isValid = false;
            return false;
        }
        
        if (options.length < 2) {
            alert('Her soru için en az 2 seçenek olmalıdır.');
            isValid = false;
            return false;
        }
        
        let hasEmptyOption = false;
        options.each(function() {
            if (!$(this).val().trim()) {
                hasEmptyOption = true;
                return false;
            }
        });
        
        if (hasEmptyOption) {
            alert('Tüm seçenekler için metin girmelisiniz.');
            isValid = false;
            return false;
        }
        
        if (correctAnswer.length === 0) {
            alert('Her soru için doğru cevabı işaretlemelisiniz.');
            isValid = false;
            return false;
        }
    });
    
    return isValid;
}

function collectQuestionsData() {
    const questionsData = [];
    
    $('#questionsContainer .dynamic-form-section').each(function() {
        const questionText = $(this).find('.question-text').val().trim();
        const options = [];
        let correctAnswer = '';
        
        // Seçenekleri topla
        $(this).find('.quiz-option').each(function() {
            const optionText = $(this).find('.option-text').val().trim();
            const isCorrect = $(this).find('input[type="radio"]').is(':checked');
            
            options.push(optionText);
            
            if (isCorrect) {
                correctAnswer = optionText;
            }
        });
        
        questionsData.push({
            question: questionText,
            options: options,
            correct_answer: correctAnswer
        });
    });
    
    $('#questions_json').val(JSON.stringify(questionsData));
}
</script>
{% endblock %}
