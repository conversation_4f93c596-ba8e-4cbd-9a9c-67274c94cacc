{% extends "base.html" %}

{% block title %}Kullanıcı Düzenle{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-user-edit"></i> Kullanıcı Düzenle
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('auth.users') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> Kullanıcı Listesi
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card shadow">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-edit"></i> {{ user.tam_ad }} - <PERSON>il<PERSON><PERSON><PERSON>
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" id="editUserForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="ad" class="form-label">Ad <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="ad" name="ad" 
                                       value="{{ user.ad }}" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="soyad" class="form-label">Soyad <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="soyad" name="soyad" 
                                       value="{{ user.soyad }}" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="email" class="form-label">E-posta <span class="text-danger">*</span></label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       value="{{ user.email }}" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="telefon_numarasi" class="form-label">Telefon Numarası <span class="text-danger">*</span></label>
                                <input type="tel" class="form-control" id="telefon_numarasi" name="telefon_numarasi" 
                                       value="{{ user.telefon_numarasi }}" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="rol" class="form-label">Rol <span class="text-danger">*</span></label>
                                <select class="form-select" id="rol" name="rol" required>
                                    <option value="participant" {{ 'selected' if user.rol == 'participant' else '' }}>Katılımcı</option>
                                    <option value="manager" {{ 'selected' if user.rol == 'manager' else '' }}>Yönetici</option>
                                    <option value="admin" {{ 'selected' if user.rol == 'admin' else '' }}>Admin</option>
                                </select>
                                <div class="form-text">Kullanıcının sistem içindeki rolü</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3" id="manager_selection" style="{{ 'display: block;' if user.rol == 'participant' else 'display: none;' }}">
                                <label for="manager_id" class="form-label">Yönetici Seçin</label>
                                <select class="form-select" id="manager_id" name="manager_id">
                                    <option value="">Yönetici seçin...</option>
                                    {% for manager in managers %}
                                    <option value="{{ manager.id }}" {{ 'selected' if user.manager_id == manager.id else '' }}>
                                        {{ manager.tam_ad }} ({{ manager.email }})
                                    </option>
                                    {% endfor %}
                                </select>
                                <div class="form-text">Katılımcılar için yönetici ataması yapılabilir</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="aktif_mi" name="aktif_mi" 
                                   {{ 'checked' if user.aktif_mi else '' }}>
                            <label class="form-check-label" for="aktif_mi">
                                Kullanıcı aktif olsun
                            </label>
                            <div class="form-text">Pasif kullanıcılar sisteme giriş yapamaz</div>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('auth.users') }}" class="btn btn-secondary me-md-2">
                            <i class="fas fa-times"></i> İptal
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Değişiklikleri Kaydet
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <!-- Mevcut Bilgiler -->
        <div class="card shadow">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle"></i> Mevcut Bilgiler
                </h6>
            </div>
            <div class="card-body">
                <table class="table table-sm">
                    <tr>
                        <td><strong>ID:</strong></td>
                        <td>{{ user.id }}</td>
                    </tr>
                    <tr>
                        <td><strong>Kayıt Tarihi:</strong></td>
                        <td>{{ user.olusturulma_tarihi.strftime('%d.%m.%Y %H:%M') if user.olusturulma_tarihi else 'Bilinmiyor' }}</td>
                    </tr>
                    <tr>
                        <td><strong>Son Giriş:</strong></td>
                        <td>{{ user.son_giris_tarihi.strftime('%d.%m.%Y %H:%M') if user.son_giris_tarihi else 'Hiç giriş yapmamış' }}</td>
                    </tr>
                    <tr>
                        <td><strong>Durum:</strong></td>
                        <td>
                            <span class="badge {{ 'bg-success' if user.aktif_mi else 'bg-secondary' }}">
                                {{ 'Aktif' if user.aktif_mi else 'Pasif' }}
                            </span>
                        </td>
                    </tr>
                    {% if user.manager %}
                    <tr>
                        <td><strong>Mevcut Yönetici:</strong></td>
                        <td>{{ user.manager.tam_ad }}</td>
                    </tr>
                    {% endif %}
                </table>
            </div>
        </div>
        
        <!-- İstatistikler -->
        {% if user.rol == 'participant' %}
        <div class="card shadow mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-chart-bar"></i> Katılım İstatistikleri
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="h5 mb-0 text-primary">{{ user.enrollments.filter_by(aktif_mi=True).count() }}</div>
                        <small class="text-muted">Eğitim</small>
                    </div>
                    <div class="col-6">
                        <div class="h5 mb-0 text-success">{{ user.quiz_attempts.filter_by(tamamlandi_mi=True).count() }}</div>
                        <small class="text-muted">Quiz</small>
                    </div>
                </div>
            </div>
        </div>
        {% elif user.rol == 'manager' %}
        <div class="card shadow mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-users"></i> Yönetici İstatistikleri
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="h5 mb-0 text-info">{{ user.team_members.filter_by(aktif_mi=True).count() }}</div>
                        <small class="text-muted">Ekip Üyesi</small>
                    </div>
                    <div class="col-6">
                        <div class="h5 mb-0 text-warning">{{ user.olusturulan_egitimler.filter_by(aktif_mi=True).count() }}</div>
                        <small class="text-muted">Eğitim</small>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}
        
        <!-- Uyarılar -->
        <div class="card shadow mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-exclamation-triangle"></i> Önemli Notlar
                </h6>
            </div>
            <div class="card-body">
                <div class="alert alert-warning small">
                    <i class="fas fa-info-circle"></i>
                    <strong>Dikkat:</strong> Kullanıcı rolünü değiştirirken dikkatli olun. 
                    Rol değişikliği kullanıcının sistem yetkilerini etkiler.
                </div>
                
                {% if user.id == current_user.id %}
                <div class="alert alert-danger small">
                    <i class="fas fa-ban"></i>
                    <strong>Uyarı:</strong> Kendi hesabınızı düzenliyorsunuz. 
                    Rolünüzü değiştirirseniz yetki kaybedebilirsiniz.
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Rol seçimi değiştiğinde manager seçimini göster/gizle
$('#rol').on('change', function() {
    let rol = $(this).val();
    
    if (rol === 'participant') {
        $('#manager_selection').show();
    } else {
        $('#manager_selection').hide();
        $('#manager_id').val(''); // Seçimi temizle
    }
});

// Form submit öncesi onay
$('#editUserForm').on('submit', function(e) {
    let userName = $('#ad').val() + ' ' + $('#soyad').val();
    
    if (!confirm(userName + ' kullanıcısının bilgilerini güncellemek istediğinizden emin misiniz?')) {
        e.preventDefault();
        return false;
    }
});

// Telefon numarası formatı
$('#telefon_numarasi').on('input', function() {
    let value = $(this).val().replace(/\D/g, ''); // Sadece rakamlar
    if (value.length > 0 && !value.startsWith('0')) {
        value = '0' + value;
    }
    $(this).val(value);
});
</script>
{% endblock %}
