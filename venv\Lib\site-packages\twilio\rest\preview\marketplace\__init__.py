r"""
    This code was generated by
   ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
    |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
    |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

    Twilio - Preview
    This is the public Twilio REST API.

    NOTE: This class is auto generated by OpenAPI Generator.
    https://openapi-generator.tech
    Do not edit the class manually.
"""

from typing import Optional
from twilio.base.version import Version
from twilio.base.domain import Domain
from twilio.rest.preview.marketplace.available_add_on import AvailableAddOnList
from twilio.rest.preview.marketplace.installed_add_on import InstalledAddOnList


class Marketplace(Version):
    def __init__(self, domain: Domain):
        """
        Initialize the Marketplace version of Preview

        :param domain: The Twilio.preview domain
        """
        super().__init__(domain, "marketplace")
        self._available_add_ons: Optional[AvailableAddOnList] = None
        self._installed_add_ons: Optional[InstalledAddOnList] = None

    @property
    def available_add_ons(self) -> AvailableAddOnList:
        if self._available_add_ons is None:
            self._available_add_ons = AvailableAddOnList(self)
        return self._available_add_ons

    @property
    def installed_add_ons(self) -> InstalledAddOnList:
        if self._installed_add_ons is None:
            self._installed_add_ons = InstalledAddOnList(self)
        return self._installed_add_ons

    def __repr__(self) -> str:
        """
        Provide a friendly representation
        :returns: Machine friendly representation
        """
        return "<Twilio.Preview.Marketplace>"
