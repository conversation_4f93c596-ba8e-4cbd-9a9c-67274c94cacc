{% extends "base.html" %}

{% block title %}Eğitim Materyalleri - {{ training.ad }}{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-folder-open"></i> Eğitim Materyalleri
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        {% if can_upload %}
        <a href="{{ url_for('material.upload_material', training_id=training.id) }}" class="btn btn-primary me-2">
            <i class="fas fa-upload"></i> <PERSON><PERSON>al <PERSON>
        </a>
        {% endif %}
        <a href="{{ url_for('training.detail', training_id=training.id) }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left"></i> Eğitime Dön
        </a>
    </div>
</div>

<!-- Eğitim Bilgisi -->
<div class="alert alert-info">
    <div class="row align-items-center">
        <div class="col-md-8">
            <h6 class="mb-1"><i class="fas fa-graduation-cap"></i> {{ training.ad }}</h6>
            <small class="text-muted">
                {{ training.baslangic_tarihi.strftime('%d.%m.%Y') }} - {{ training.bitis_tarihi.strftime('%d.%m.%Y') }}
                • {{ training.katilimci_sayisi }} katılımcı
            </small>
        </div>
        <div class="col-md-4 text-end">
            <span class="badge bg-primary fs-6">{{ materials|length }} Materyal</span>
        </div>
    </div>
</div>

{% if materials %}
<!-- Materyal Listesi -->
<div class="row">
    {% for material in materials %}
    <div class="col-lg-6 col-xl-4 mb-4">
        <div class="card h-100 shadow-sm material-card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center">
                    <i class="{{ material.file_icon }} {{ material.file_color }} me-2"></i>
                    <span class="badge bg-light text-dark">{{ material.file_type.title() }}</span>
                </div>
                {% if can_upload %}
                <div class="dropdown">
                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="fas fa-ellipsis-v"></i>
                    </button>
                    <ul class="dropdown-menu">
                        <li>
                            <a class="dropdown-item" href="#" onclick="toggleMaterialStatus({{ material.id }}, '{{ material.file_name }}', {{ material.is_active|lower }})">
                                <i class="fas fa-{{ 'eye-slash' if material.is_active else 'eye' }}"></i>
                                {{ 'Gizle' if material.is_active else 'Göster' }}
                            </a>
                        </li>
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <a class="dropdown-item text-danger" href="#" onclick="deleteMaterial({{ material.id }}, '{{ material.file_name }}')">
                                <i class="fas fa-trash"></i> Sil
                            </a>
                        </li>
                    </ul>
                </div>
                {% endif %}
            </div>
            
            <div class="card-body">
                <h6 class="card-title text-truncate" title="{{ material.file_name }}">
                    {{ material.file_name }}
                </h6>
                
                {% if material.description %}
                <p class="card-text text-muted small">
                    {{ material.description[:100] }}{% if material.description|length > 100 %}...{% endif %}
                </p>
                {% endif %}
                
                <div class="row text-center mb-3">
                    <div class="col-4">
                        <small class="text-muted">Boyut</small>
                        <div class="fw-bold small">{{ material.file_size_formatted }}</div>
                    </div>
                    <div class="col-4">
                        <small class="text-muted">İndirme</small>
                        <div class="fw-bold small">{{ material.download_count }}</div>
                    </div>
                    <div class="col-4">
                        <small class="text-muted">Görüntülenme</small>
                        <div class="fw-bold small">{{ material.view_count }}</div>
                    </div>
                </div>
                
                <div class="mb-2">
                    <small class="text-muted">
                        <i class="fas fa-user"></i> {{ material.uploaded_by.tam_ad }}
                        <br>
                        <i class="fas fa-clock"></i> {{ material.upload_date.strftime('%d.%m.%Y %H:%M') }}
                    </small>
                </div>
            </div>
            
            <div class="card-footer">
                <div class="d-flex gap-2">
                    <!-- Görüntüle Butonu -->
                    {% if material.is_viewable_online %}
                    <a href="{{ url_for('material.view_material', material_id=material.id) }}" 
                       class="btn btn-outline-primary btn-sm flex-fill" target="_blank">
                        <i class="fas fa-eye"></i> Görüntüle
                    </a>
                    {% endif %}
                    
                    <!-- Video Oynat Butonu -->
                    {% if material.is_video %}
                    <button class="btn btn-outline-info btn-sm flex-fill" onclick="playVideo({{ material.id }}, '{{ material.file_name }}', '{{ material.mime_type }}')">
                        <i class="fas fa-play"></i> Oynat
                    </button>
                    {% endif %}
                    
                    <!-- İndir Butonu -->
                    <a href="{{ url_for('material.download_material', material_id=material.id) }}" 
                       class="btn btn-success btn-sm flex-fill">
                        <i class="fas fa-download"></i> İndir
                    </a>
                </div>
            </div>
        </div>
    </div>
    {% endfor %}
</div>

{% else %}
<!-- Boş Durum -->
<div class="text-center py-5">
    <i class="fas fa-folder-open fa-4x text-muted mb-3"></i>
    <h4 class="text-muted">Henüz materyal yüklenmemiş</h4>
    <p class="text-muted">Bu eğitim için henüz hiç materyal yüklenmemiş.</p>
    {% if can_upload %}
    <a href="{{ url_for('material.upload_material', training_id=training.id) }}" class="btn btn-primary">
        <i class="fas fa-upload"></i> İlk Materyali Yükle
    </a>
    {% endif %}
</div>
{% endif %}

<!-- Video Modal -->
<div class="modal fade" id="videoModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="videoModalTitle">Video Oynatıcı</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <video id="videoPlayer" class="w-100" controls preload="metadata" controlsList="nodownload">
                    <source id="videoSource" src="" type="video/mp4">
                    Tarayıcınız video oynatmayı desteklemiyor.
                </video>
                <div class="mt-2 text-muted small">
                    <i class="fas fa-info-circle"></i> Video yüklenirken lütfen bekleyin...
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Önizleme Modal -->
<div class="modal fade" id="previewModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="previewModalTitle">Dosya Önizleme</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="previewContent" class="text-center">
                    <iframe id="previewFrame" src="" style="width: 100%; height: 600px; border: none;"></iframe>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.material-card {
    transition: transform 0.2s ease-in-out;
}

.material-card:hover {
    transform: translateY(-2px);
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

.badge {
    font-size: 0.75em;
}

.btn-group-sm > .btn, .btn-sm {
    font-size: 0.8rem;
}

#videoPlayer {
    max-height: 500px;
}

.text-truncate {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
function playVideo(materialId, fileName, mimeType) {
    $('#videoModalTitle').text(fileName);
    $('#videoSource').attr('src', `/material/${materialId}/stream`);
    $('#videoSource').attr('type', mimeType || 'video/mp4');
    $('#videoPlayer')[0].load();
    $('#videoModal').modal('show');
}

function previewFile(materialId, fileName) {
    $('#previewModalTitle').text(fileName);
    $('#previewFrame').attr('src', `/material/${materialId}/view`);
    $('#previewModal').modal('show');
}

function deleteMaterial(materialId, fileName) {
    if (confirm(`"${fileName}" dosyasını silmek istediğinizden emin misiniz?\n\nBu işlem geri alınamaz!`)) {
        fetch(`/material/${materialId}/delete`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('✅ ' + data.message);
                location.reload();
            } else {
                alert('❌ Hata: ' + data.message);
            }
        })
        .catch(error => {
            alert('❌ Bir hata oluştu: ' + error);
        });
    }
}

function toggleMaterialStatus(materialId, fileName, isActive) {
    const action = isActive ? 'gizlemek' : 'göstermek';
    
    if (confirm(`"${fileName}" dosyasını ${action} istediğinizden emin misiniz?`)) {
        fetch(`/material/${materialId}/toggle-status`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('✅ ' + data.message);
                location.reload();
            } else {
                alert('❌ Hata: ' + data.message);
            }
        })
        .catch(error => {
            alert('❌ Bir hata oluştu: ' + error);
        });
    }
}

// Modal kapatıldığında video durdur
$('#videoModal').on('hidden.bs.modal', function () {
    const video = $('#videoPlayer')[0];
    video.pause();
    video.currentTime = 0;
});

// Video yükleme durumu takibi
$('#videoPlayer').on('loadstart', function() {
    $('.modal-body .text-muted').text('Video yükleniyor...').show();
});

$('#videoPlayer').on('canplay', function() {
    $('.modal-body .text-muted').text('Video hazır!').fadeOut(2000);
});

$('#videoPlayer').on('error', function() {
    $('.modal-body .text-muted').text('Video yüklenirken hata oluştu.').removeClass('text-muted').addClass('text-danger');
});
</script>
{% endblock %}
