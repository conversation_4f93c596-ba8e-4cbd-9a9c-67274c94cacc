{% extends "base.html" %}

{% block title %}Quiz Sonucu - {{ quiz.ad }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <!-- <PERSON><PERSON><PERSON> -->
            <div class="card shadow mb-4">
                <div class="card-header {{ 'bg-success' if attempt.gecme_durumu else 'bg-danger' }} text-white">
                    <div class="text-center">
                        <h3 class="mb-2">
                            <i class="fas {{ 'fa-check-circle' if attempt.gecme_durumu else 'fa-times-circle' }}"></i>
                            {{ 'Tebrikler!' if attempt.gecme_durumu else 'Maalesef' }}
                        </h3>
                        <h4 class="mb-0">
                            {{ 'Geçtiniz!' if attempt.gecme_durumu else 'Geçemediniz' }}
                        </h4>
                    </div>
                </div>
                <div class="card-body text-center">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="h2 mb-0 {{ 'text-success' if attempt.gecme_durumu else 'text-danger' }}">
                                {{ "%.1f"|format(attempt.skor) }}%
                            </div>
                            <small class="text-muted">Skorunuz</small>
                        </div>
                        <div class="col-md-4">
                            <div class="h2 mb-0 text-info">
                                {{ quiz.gecer_notu }}%
                            </div>
                            <small class="text-muted">Geçer Not</small>
                        </div>
                        <div class="col-md-4">
                            <div class="h2 mb-0 text-primary">
                                {{ quiz.soru_sayisi }}
                            </div>
                            <small class="text-muted">Toplam Soru</small>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <strong>Eğitim:</strong> {{ training.ad }}
                        </div>
                        <div class="col-md-6">
                            <strong>Tamamlanma Tarihi:</strong> {{ attempt.deneme_tarihi.strftime('%d.%m.%Y %H:%M') }}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quiz Detayları -->
            <div class="card shadow mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle"></i> Quiz Detayları
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>{{ quiz.ad }}</h6>
                            {% if quiz.aciklama %}
                            <p class="text-muted">{{ quiz.aciklama }}</p>
                            {% endif %}
                        </div>
                        <div class="col-md-6">
                            <div class="progress mb-2">
                                <div class="progress-bar {{ 'bg-success' if attempt.gecme_durumu else 'bg-danger' }}" 
                                     role="progressbar" 
                                     style="width: {{ attempt.skor }}%">
                                    {{ "%.1f"|format(attempt.skor) }}%
                                </div>
                            </div>
                            <small class="text-muted">
                                Başarı oranınız: {{ "%.1f"|format(attempt.skor) }}% / {{ quiz.gecer_notu }}%
                            </small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Cevap Detayları (Sadece yöneticiler için) -->
            {% if current_user.can_manage_trainings() %}
            <div class="card shadow mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list-alt"></i> Cevap Detayları
                    </h5>
                </div>
                <div class="card-body">
                    {% for question in quiz.questions %}
                    <div class="mb-4 p-3 border rounded">
                        <h6 class="mb-3">
                            <span class="badge bg-secondary me-2">{{ loop.index }}</span>
                            {{ question.question }}
                        </h6>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <strong>Seçenekler:</strong>
                                <ul class="list-unstyled mt-2">
                                    {% for option in question.options %}
                                    <li class="mb-1">
                                        <span class="badge 
                                            {% if option == question.correct_answer %}bg-success
                                            {% elif option == attempt.cevaplar.get(loop.index0|string) %}bg-danger
                                            {% else %}bg-light text-dark{% endif %} me-2">
                                            {% if option == question.correct_answer %}✓
                                            {% elif option == attempt.cevaplar.get(loop.index0|string) %}✗
                                            {% else %}○{% endif %}
                                        </span>
                                        {{ option }}
                                    </li>
                                    {% endfor %}
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <strong>Doğru Cevap:</strong> 
                                <span class="text-success">{{ question.correct_answer }}</span><br>
                                
                                <strong>Verilen Cevap:</strong> 
                                {% set user_answer = attempt.cevaplar.get(loop.index0|string) %}
                                {% if user_answer %}
                                    <span class="{{ 'text-success' if user_answer == question.correct_answer else 'text-danger' }}">
                                        {{ user_answer }}
                                    </span>
                                {% else %}
                                    <span class="text-muted">Yanıtlanmamış</span>
                                {% endif %}
                                
                                <br>
                                <strong>Sonuç:</strong>
                                {% if user_answer == question.correct_answer %}
                                    <span class="badge bg-success">Doğru</span>
                                {% else %}
                                    <span class="badge bg-danger">Yanlış</span>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endif %}

            <!-- İstatistikler -->
            {% if current_user.can_manage_trainings() %}
            <div class="card shadow mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-bar"></i> Quiz İstatistikleri
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-md-3">
                            <div class="h4 mb-0 text-primary">{{ quiz.deneme_sayisi }}</div>
                            <small class="text-muted">Toplam Deneme</small>
                        </div>
                        <div class="col-md-3">
                            <div class="h4 mb-0 text-success">{{ quiz.basarili_deneme_sayisi }}</div>
                            <small class="text-muted">Başarılı Deneme</small>
                        </div>
                        <div class="col-md-3">
                            <div class="h4 mb-0 text-info">{{ "%.1f"|format(quiz.basari_orani) }}%</div>
                            <small class="text-muted">Başarı Oranı</small>
                        </div>
                        <div class="col-md-3">
                            <div class="h4 mb-0 text-warning">{{ "%.1f"|format(quiz.ortalama_skor) }}%</div>
                            <small class="text-muted">Ortalama Skor</small>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- Aksiyon Butonları -->
            <div class="card shadow">
                <div class="card-body text-center">
                    <div class="btn-group" role="group">
                        <a href="{{ url_for('training.detail', training_id=training.id) }}" class="btn btn-outline-primary">
                            <i class="fas fa-arrow-left"></i> Eğitime Dön
                        </a>
                        
                        {% if current_user.can_manage_trainings() %}
                        <a href="{{ url_for('quiz.quiz_results', quiz_id=quiz.id) }}" class="btn btn-outline-info">
                            <i class="fas fa-chart-bar"></i> Tüm Sonuçlar
                        </a>
                        <a href="{{ url_for('quiz.edit_quiz', quiz_id=quiz.id) }}" class="btn btn-outline-warning">
                            <i class="fas fa-edit"></i> Quiz'i Düzenle
                        </a>
                        {% endif %}
                        
                        <button onclick="window.print()" class="btn btn-outline-secondary">
                            <i class="fas fa-print"></i> Yazdır
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
@media print {
    .btn, .card-header, nav, .sidebar {
        display: none !important;
    }
    
    .card {
        border: none !important;
        box-shadow: none !important;
    }
    
    .main-content {
        margin: 0 !important;
        padding: 0 !important;
    }
}
</style>
{% endblock %}
