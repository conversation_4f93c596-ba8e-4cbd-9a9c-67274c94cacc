{% extends "base.html" %}

{% block title %}Toplu Kullanıcı Yükleme{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-upload"></i> Toplu Kullanıcı Yükleme
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('auth.users') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> Kullanı<PERSON>ı Listesi
            </a>
            <a href="{{ url_for('auth.download_sample_excel') }}" class="btn btn-outline-success">
                <i class="fas fa-download"></i> Örnek Dosya İndir
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <!-- Upload Formu -->
        <div class="card shadow">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-file-excel"></i> Excel Dosyası Yükle
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" enctype="multipart/form-data" id="uploadForm">
                    <div class="mb-4">
                        <label for="excel_file" class="form-label">Excel Dosyası Seçin</label>
                        <input type="file" 
                               class="form-control" 
                               id="excel_file" 
                               name="excel_file" 
                               accept=".xlsx,.xls"
                               required>
                        <div class="form-text">
                            Sadece .xlsx ve .xls dosyaları kabul edilir. Maksimum dosya boyutu: 16MB
                        </div>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <button type="submit" class="btn btn-primary" id="uploadBtn">
                            <i class="fas fa-upload"></i> Dosyayı Yükle ve İşle
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <!-- Yönergeler -->
        <div class="card shadow">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle"></i> Yönergeler
                </h6>
            </div>
            <div class="card-body">
                <h6>Excel Dosyası Formatı:</h6>
                <p class="small">Excel dosyanızda aşağıdaki sütunlar bulunmalıdır:</p>
                
                <table class="table table-sm table-bordered">
                    <thead class="table-light">
                        <tr>
                            <th>Sütun Adı</th>
                            <th>Zorunlu</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><code>ad</code></td>
                            <td><span class="badge bg-danger">Evet</span></td>
                        </tr>
                        <tr>
                            <td><code>soyad</code></td>
                            <td><span class="badge bg-danger">Evet</span></td>
                        </tr>
                        <tr>
                            <td><code>email</code></td>
                            <td><span class="badge bg-danger">Evet</span></td>
                        </tr>
                        <tr>
                            <td><code>telefon_numarasi</code></td>
                            <td><span class="badge bg-danger">Evet</span></td>
                        </tr>
                        <tr>
                            <td><code>rol</code></td>
                            <td><span class="badge bg-danger">Evet</span></td>
                        </tr>
                        <tr>
                            <td><code>sifre</code></td>
                            <td><span class="badge bg-secondary">Hayır</span></td>
                        </tr>
                        <tr>
                            <td><code>manager_email</code></td>
                            <td><span class="badge bg-secondary">Hayır</span></td>
                        </tr>
                    </tbody>
                </table>
                
                <h6 class="mt-3">Geçerli Roller:</h6>
                <ul class="small">
                    <li><code>participant</code> - Katılımcı</li>
                    <li><code>manager</code> - Yönetici</li>
                    <li><code>admin</code> - Admin</li>
                </ul>
                
                <div class="alert alert-info small mt-3">
                    <i class="fas fa-lightbulb"></i>
                    <strong>İpuçları:</strong><br>
                    • Şifre sütunu boş bırakılırsa varsayılan şifre "123456" olarak atanır.<br>
                    • manager_email sütunu sadece participant rolündeki kullanıcılar için kullanılır.<br>
                    • Yönetici email'i sistemde kayıtlı bir manager veya admin olmalıdır.
                </div>
            </div>
        </div>
        
        <!-- Örnek Veri -->
        <div class="card shadow mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-table"></i> Örnek Veri
                </h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm table-bordered">
                        <thead class="table-light">
                            <tr>
                                <th>ad</th>
                                <th>soyad</th>
                                <th>email</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Ahmet</td>
                                <td>Yılmaz</td>
                                <td><EMAIL></td>
                            </tr>
                            <tr>
                                <td>Ayşe</td>
                                <td>Kaya</td>
                                <td><EMAIL></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <div class="table-responsive mt-2">
                    <table class="table table-sm table-bordered">
                        <thead class="table-light">
                            <tr>
                                <th>telefon_numarasi</th>
                                <th>rol</th>
                                <th>manager_email</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>05551234567</td>
                                <td>manager</td>
                                <td>-</td>
                            </tr>
                            <tr>
                                <td>05551234568</td>
                                <td>participant</td>
                                <td><EMAIL></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Loading Modal -->
<div class="modal fade" id="loadingModal" tabindex="-1" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body text-center py-4">
                <div class="spinner-border text-primary mb-3" role="status">
                    <span class="visually-hidden">Yükleniyor...</span>
                </div>
                <h5>Excel Dosyası İşleniyor...</h5>
                <p class="text-muted mb-0">Lütfen bekleyin, kullanıcılar oluşturuluyor.</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.getElementById('uploadForm').addEventListener('submit', function(e) {
    // Loading modal'ını göster
    var loadingModal = new bootstrap.Modal(document.getElementById('loadingModal'));
    loadingModal.show();
    
    // Upload butonunu deaktif et
    document.getElementById('uploadBtn').disabled = true;
});

// Dosya seçildiğinde bilgi göster
document.getElementById('excel_file').addEventListener('change', function(e) {
    var file = e.target.files[0];
    if (file) {
        var fileSize = (file.size / 1024 / 1024).toFixed(2);
        console.log('Seçilen dosya:', file.name, '(' + fileSize + ' MB)');
    }
});
</script>
{% endblock %}
