{% extends "base.html" %}

{% block title %}Eğitmen Onayı - {{ training.ad }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header text-center">
                    <h4 class="mb-0">
                        <i class="fas fa-chalkboard-teacher"></i> Eğitmen Onayı
                    </h4>
                </div>
                <div class="card-body">
                    <!-- Eğitim Bilgileri -->
                    <div class="alert alert-info">
                        <h5><i class="fas fa-info-circle"></i> Eğitim Bilgileri</h5>
                        <hr>
                        <div class="row">
                            <div class="col-md-6">
                                <strong>Eğitim Adı:</strong><br>
                                {{ training.ad }}
                            </div>
                            <div class="col-md-6">
                                <strong>Tarih:</strong><br>
                                {{ training.baslangic_tarihi.strftime('%d.%m.%Y %H:%M') }} - 
                                {{ training.bitis_tarihi.strftime('%d.%m.%Y %H:%M') }}
                            </div>
                        </div>
                        {% if training.aciklama %}
                        <div class="mt-2">
                            <strong>Açıklama:</strong><br>
                            {{ training.aciklama }}
                        </div>
                        {% endif %}
                        <div class="mt-2">
                            <strong>Katılımcı Sayısı:</strong> {{ training.katilimci_sayisi }} kişi
                        </div>
                    </div>

                    {% if show_form %}
                    <!-- Onay Formu -->
                    <div class="text-center">
                        <h5 class="mb-4">
                            <i class="fas fa-question-circle text-warning"></i>
                            Bu eğitimi verdiğinizi onaylıyor musunuz?
                        </h5>
                        
                        <form method="POST" class="d-inline">
                            <input type="hidden" name="confirmation" value="confirm">
                            <button type="submit" class="btn btn-success btn-lg me-3">
                                <i class="fas fa-check"></i> Evet, Eğitimi Verdim
                            </button>
                        </form>
                        
                        <form method="POST" class="d-inline">
                            <input type="hidden" name="confirmation" value="cancel">
                            <button type="submit" class="btn btn-danger btn-lg">
                                <i class="fas fa-times"></i> Hayır, Eğitimi Vermedim
                            </button>
                        </form>
                    </div>
                    
                    {% elif success %}
                    <!-- Başarı Mesajı -->
                    <div class="text-center">
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                            <h4>Teşekkürler!</h4>
                            <p class="mb-0">{{ message }}</p>
                        </div>
                        
                        <div class="mt-4">
                            <p class="text-muted">
                                Onayınız sistem tarafından kaydedilmiştir. 
                                Eğitim yöneticileri bilgilendirilecektir.
                            </p>
                        </div>
                    </div>
                    
                    {% else %}
                    <!-- Hata Mesajı -->
                    <div class="text-center">
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle fa-3x text-danger mb-3"></i>
                            <h4>Bir Sorun Oluştu</h4>
                            <p class="mb-0">{{ message }}</p>
                        </div>
                        
                        <div class="mt-4">
                            <p class="text-muted">
                                Lütfen eğitim yöneticileri ile iletişime geçin.
                            </p>
                        </div>
                    </div>
                    {% endif %}
                </div>
                
                <div class="card-footer text-center text-muted">
                    <small>
                        <i class="fas fa-shield-alt"></i>
                        Bu onay sistemi güvenli bir şekilde şifrelenmiştir.
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Bilgilendirme -->
<div class="row justify-content-center mt-4">
    <div class="col-lg-8">
        <div class="card border-info">
            <div class="card-header bg-info text-white">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle"></i> Bilgilendirme
                </h6>
            </div>
            <div class="card-body">
                <ul class="mb-0">
                    <li>Bu onay sistemi eğitimin gerçekten verildiğini teyit etmek için kullanılır.</li>
                    <li>Onayınız sistem tarafından kaydedilir ve değiştirilemez.</li>
                    <li>Eğitim yöneticileri onayınızı görebilir.</li>
                    <li>Herhangi bir sorun durumunda eğitim yöneticileri ile iletişime geçebilirsiniz.</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Form submit öncesi onay
$('form').on('submit', function(e) {
    let confirmation = $(this).find('input[name="confirmation"]').val();
    let message = '';
    
    if (confirmation === 'confirm') {
        message = 'Eğitimi verdiğinizi onaylamak istediğinizden emin misiniz?\n\nBu işlem geri alınamaz.';
    } else {
        message = 'Eğitimi vermediğinizi belirtmek istediğinizden emin misiniz?';
    }
    
    if (!confirm(message)) {
        e.preventDefault();
        return false;
    }
});
</script>
{% endblock %}
