{% extends "base.html" %}

{% block title %}Eğitim Değerlendirme Anketi - {{ training.ad }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-poll"></i> Eğitim Değerlendirme Anketi
                    </h4>
                </div>
                <div class="card-body">
                    <!-- Anket Bilgileri -->
                    <div class="alert alert-info">
                        <div class="row">
                            <div class="col-md-6">
                                <strong>Eğitim:</strong> {{ training.ad }}<br>
                                <strong>Yönetici:</strong> {{ manager.tam_ad }}
                            </div>
                            <div class="col-md-6">
                                <strong>Eğitim Tarihi:</strong> 
                                {{ training.baslangic_tarihi.strftime('%d.%m.%Y') }} - 
                                {{ training.bitis_tarihi.strftime('%d.%m.%Y') }}<br>
                                <strong>Anket Tarihi:</strong> {{ moment().format('DD.MM.YYYY') }}
                            </div>
                        </div>
                    </div>

                    <!-- Anket Formu -->
                    <form id="surveyForm" method="POST">
                        {% for question in questions %}
                        <div class="mb-4 p-3 border rounded">
                            <h6 class="mb-3">
                                <span class="badge bg-secondary me-2">{{ loop.index }}</span>
                                {{ question.question }}
                            </h6>
                            
                            {% if question.type == 'rating' %}
                            <!-- Rating Soruları -->
                            <div class="row">
                                {% for option in question.options %}
                                <div class="col-md-6 mb-2">
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" 
                                               name="question_{{ loop.index0 }}" 
                                               id="q{{ loop.index0 }}_opt{{ loop.index0 }}" 
                                               value="{{ option }}" required>
                                        <label class="form-check-label" for="q{{ loop.index0 }}_opt{{ loop.index0 }}">
                                            {{ option }}
                                        </label>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                            
                            {% elif question.type == 'text' %}
                            <!-- Metin Soruları -->
                            <textarea class="form-control" 
                                      name="question_{{ loop.index0 }}" 
                                      rows="4" 
                                      placeholder="Lütfen detaylı olarak açıklayın..."
                                      required></textarea>
                            
                            {% elif question.type == 'select' %}
                            <!-- Seçim Soruları -->
                            <select class="form-select" name="question_{{ loop.index0 }}" required>
                                <option value="">Seçiniz...</option>
                                {% for option in question.options %}
                                <option value="{{ option }}">{{ option }}</option>
                                {% endfor %}
                            </select>
                            {% endif %}
                        </div>
                        {% endfor %}
                        
                        <!-- Ek Yorumlar -->
                        <div class="mb-4 p-3 border rounded">
                            <h6 class="mb-3">
                                <i class="fas fa-comment"></i> Ek Yorumlar ve Öneriler
                            </h6>
                            <textarea class="form-control" 
                                      name="additional_comments" 
                                      rows="4" 
                                      placeholder="Eğitim hakkında eklemek istediğiniz başka yorumlarınız varsa lütfen belirtin..."></textarea>
                        </div>
                        
                        <!-- Submit Buttons -->
                        <div class="d-flex justify-content-between">
                            <button type="button" class="btn btn-outline-secondary" onclick="window.close()">
                                <i class="fas fa-times"></i> İptal
                            </button>
                            <button type="submit" class="btn btn-primary" id="submitBtn">
                                <i class="fas fa-paper-plane"></i> Anketi Gönder
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Başarı Modalı -->
<div class="modal fade" id="successModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title">
                    <i class="fas fa-check-circle"></i> Anket Gönderildi
                </h5>
            </div>
            <div class="modal-body text-center">
                <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                <h5>Teşekkür Ederiz!</h5>
                <p>Anketiniz başarıyla gönderildi. Geri bildiriminiz bizim için çok değerli.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" onclick="window.close()">
                    <i class="fas fa-times"></i> Kapat
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Form submit
    $('#surveyForm').on('submit', function(e) {
        e.preventDefault();
        submitSurvey();
    });
    
    // Progress tracking
    $('input, textarea, select').on('change input', function() {
        updateProgress();
    });
    
    updateProgress();
});

function updateProgress() {
    let totalQuestions = {{ questions|length }};
    let answeredQuestions = 0;
    
    // Rating ve select soruları
    $('input[type="radio"]:checked, select').each(function() {
        if ($(this).val()) {
            answeredQuestions++;
        }
    });
    
    // Text soruları
    $('textarea[required]').each(function() {
        if ($(this).val().trim()) {
            answeredQuestions++;
        }
    });
    
    let percentage = (answeredQuestions / totalQuestions) * 100;
    
    // Progress bar güncelle (eğer varsa)
    if ($('#progressBar').length) {
        $('#progressBar').css('width', percentage + '%');
        $('#progressText').text(answeredQuestions + ' / ' + totalQuestions + ' soru yanıtlandı');
    }
    
    // Submit butonunu aktif/pasif yap
    if (answeredQuestions === totalQuestions) {
        $('#submitBtn').prop('disabled', false);
    } else {
        $('#submitBtn').prop('disabled', true);
    }
}

function submitSurvey() {
    // Tüm gerekli alanlar dolduruldu mu kontrol et
    let isValid = true;
    let emptyFields = [];
    
    $('input[required], textarea[required], select[required]').each(function() {
        if (!$(this).val() || ($(this).is('textarea') && !$(this).val().trim())) {
            isValid = false;
            let questionNumber = $(this).closest('.border').find('.badge').text();
            emptyFields.push(questionNumber);
        }
    });
    
    if (!isValid) {
        alert(`Lütfen şu soruları yanıtlayın: ${emptyFields.join(', ')}`);
        return;
    }
    
    // Submit butonunu deaktive et
    $('#submitBtn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Gönderiliyor...');
    
    // Form verilerini topla
    let formData = new FormData($('#surveyForm')[0]);
    
    // AJAX ile gönder
    fetch(window.location.href, {
        method: 'POST',
        body: formData
    })
    .then(response => {
        if (response.ok) {
            return response.text();
        } else {
            throw new Error('Sunucu hatası');
        }
    })
    .then(data => {
        // Başarı modalını göster
        $('#successModal').modal('show');
        
        // 3 saniye sonra sayfayı kapat
        setTimeout(function() {
            window.close();
        }, 3000);
    })
    .catch(error => {
        alert('Anket gönderilirken bir hata oluştu: ' + error.message);
        $('#submitBtn').prop('disabled', false).html('<i class="fas fa-paper-plane"></i> Anketi Gönder');
    });
}

// Sayfa kapatılmaya çalışıldığında uyar (anket doldurulmuşsa)
window.addEventListener('beforeunload', function(e) {
    let hasAnswers = false;
    
    $('input:checked, textarea, select').each(function() {
        if ($(this).val()) {
            hasAnswers = true;
            return false;
        }
    });
    
    if (hasAnswers) {
        e.preventDefault();
        e.returnValue = 'Anket doldurulmaya başlanmış. Sayfayı kapatmak istediğinizden emin misiniz?';
    }
});
</script>
{% endblock %}
