from datetime import datetime, timed<PERSON>ta
from flask import Blueprint, request, jsonify, current_app
from flask_login import login_required, current_user
from app import db
from app.models.training import Training, Enrollment
from app.models.communication import CommunicationLog
from app.utils.sms_service import SMSService
from app.utils.email_service import EmailService
from itsdangerous import URLSafeTimedSerializer, BadSignature

api_bp = Blueprint('api', __name__)

def generate_token(data, salt='confirmation'):
    """Güvenli token oluştur"""
    serializer = URLSafeTimedSerializer(current_app.config['SECRET_KEY'])
    return serializer.dumps(data, salt=salt)

def verify_token(token, salt='confirmation', max_age=7*24*3600):  # 7 gün
    """Token'ı doğrula"""
    serializer = URLSafeTimedSerializer(current_app.config['SECRET_KEY'])
    try:
        data = serializer.loads(token, salt=salt, max_age=max_age)
        return data
    except BadSignature:
        return None

@api_bp.route('/send-sms-confirmations', methods=['POST'])
@login_required
def send_sms_confirmations():
    """Katılım teyidi SMS'lerini gönder"""
    if not current_user.can_manage_trainings():
        return jsonify({'success': False, 'message': 'Yetkiniz yok'}), 403
    
    # Yaklaşan eğitimleri bul (SMS_SEND_DAYS_BEFORE gün öncesinden)
    days_before = current_app.config.get('SMS_SEND_DAYS_BEFORE', 3)
    target_date = datetime.utcnow() + timedelta(days=days_before)
    
    # Bugünden itibaren hedef tarihe kadar olan eğitimler
    upcoming_trainings = Training.query.filter(
        Training.baslangic_tarihi >= datetime.utcnow(),
        Training.baslangic_tarihi <= target_date,
        Training.aktif_mi == True
    ).all()
    
    sms_service = SMSService()
    sent_count = 0
    failed_count = 0
    
    for training in upcoming_trainings:
        # Bu eğitim için henüz SMS gönderilmemiş katılımcıları bul
        enrollments = training.enrollments.filter_by(
            aktif_mi=True,
            katilim_teyidi_durumu='beklemede'
        ).filter(
            Enrollment.teyit_sms_id.is_(None)
        ).all()
        
        for enrollment in enrollments:
            user = enrollment.user
            
            # Token oluştur
            token_data = {
                'enrollment_id': enrollment.id,
                'user_id': user.id,
                'training_id': training.id
            }
            token = generate_token(token_data, salt='sms_confirmation')
            
            # SMS içeriği
            confirmation_url = f"{current_app.config['BASE_URL']}/participant/confirm/{token}"
            message = f"Merhaba {user.ad}, {training.ad} eğitimine katılımınızı teyit etmek için lütfen bu linke tıklayın: {confirmation_url}"
            
            # SMS gönder
            result = sms_service.send_sms(user.telefon_numarasi, message)
            
            # Log kaydı oluştur
            log = CommunicationLog(
                type='sms',
                recipient_id=user.id,
                content=message,
                status='sent' if result['success'] else 'failed',
                external_id=result.get('sid'),
                error_message=result.get('error'),
                training_id=training.id,
                enrollment_id=enrollment.id
            )
            db.session.add(log)
            
            if result['success']:
                enrollment.teyit_sms_id = result.get('sid')
                sent_count += 1
            else:
                failed_count += 1
    
    try:
        db.session.commit()
        return jsonify({
            'success': True,
            'message': f'{sent_count} SMS gönderildi, {failed_count} başarısız',
            'sent_count': sent_count,
            'failed_count': failed_count
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': 'SMS kayıtları güncellenirken hata oluştu'}), 500

@api_bp.route('/send-survey-emails', methods=['POST'])
@login_required
def send_survey_emails():
    """Anket e-postalarını gönder"""
    if not current_user.can_manage_trainings():
        return jsonify({'success': False, 'message': 'Yetkiniz yok'}), 403
    
    # Tamamlanan eğitimleri bul (SURVEY_SEND_DAYS_AFTER gün sonrasından)
    days_after = current_app.config.get('SURVEY_SEND_DAYS_AFTER', 1)
    target_date = datetime.utcnow() - timedelta(days=days_after)
    
    completed_trainings = Training.query.filter(
        Training.bitis_tarihi <= target_date,
        Training.aktif_mi == True
    ).all()
    
    email_service = EmailService()
    sent_count = 0
    failed_count = 0
    
    for training in completed_trainings:
        # Bu eğitimin katılımcılarının yöneticilerine anket gönder
        confirmed_enrollments = training.enrollments.filter_by(
            aktif_mi=True,
            katilim_teyidi_durumu='teyit_edildi'
        ).all()
        
        # Yöneticileri topla (tekrar etmesin)
        managers = set()
        for enrollment in confirmed_enrollments:
            participant = enrollment.user
            # Burada yönetici-çalışan ilişkisini nasıl belirleyeceğinizi düşünmeniz gerekiyor
            # Şimdilik manager rolündeki kullanıcıları yönetici olarak kabul edelim
            if participant.rol == 'participant':
                # Tüm manager'lara gönder (gerçek uygulamada ilişki tablosu olmalı)
                from app.models.user import User
                training_managers = User.query.filter_by(rol='manager', aktif_mi=True).all()
                managers.update(training_managers)
        
        for manager in managers:
            # Token oluştur
            token_data = {
                'manager_id': manager.id,
                'training_id': training.id
            }
            token = generate_token(token_data, salt='survey_email')
            
            # E-posta içeriği
            survey_url = f"{current_app.config['BASE_URL']}/manager/survey/{token}"
            subject = f"{training.ad} Eğitimi Değerlendirme Anketi"
            
            html_content = f"""
            <h2>Eğitim Değerlendirme Anketi</h2>
            <p>Merhaba {manager.tam_ad},</p>
            <p><strong>{training.ad}</strong> eğitimi tamamlanmıştır.</p>
            <p>Eğitimin etkinliği hakkında geri bildiriminizi almak için lütfen aşağıdaki linke tıklayarak anketi doldurun:</p>
            <p><a href="{survey_url}" style="background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Anketi Doldur</a></p>
            <p>Bu anket linki 7 gün boyunca geçerlidir.</p>
            <p>Teşekkürler,<br>{current_app.config['APP_NAME']}</p>
            """
            
            # E-posta gönder
            result = email_service.send_email(
                to_email=manager.email,
                subject=subject,
                html_content=html_content
            )
            
            # Log kaydı oluştur
            log = CommunicationLog(
                type='email',
                recipient_id=manager.id,
                content=html_content,
                status='sent' if result['success'] else 'failed',
                external_id=result.get('message_id'),
                error_message=result.get('error'),
                training_id=training.id
            )
            db.session.add(log)
            
            if result['success']:
                sent_count += 1
            else:
                failed_count += 1
    
    try:
        db.session.commit()
        return jsonify({
            'success': True,
            'message': f'{sent_count} e-posta gönderildi, {failed_count} başarısız',
            'sent_count': sent_count,
            'failed_count': failed_count
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': 'E-posta kayıtları güncellenirken hata oluştu'}), 500

@api_bp.route('/participant/confirm/<token>')
def confirm_participation(token):
    """Katılım teyidi (SMS linki)"""
    from flask import render_template

    # Token'ı doğrula
    data = verify_token(token, salt='sms_confirmation')
    if not data:
        return render_template('error.html',
                             message='Geçersiz veya süresi dolmuş teyit linki'), 400
    
    enrollment_id = data.get('enrollment_id')
    enrollment = Enrollment.query.get(enrollment_id)
    
    if not enrollment:
        return render_template('error.html', message='Kayıt bulunamadı'), 404
    
    if enrollment.katilim_teyidi_durumu == 'teyit_edildi':
        return render_template('confirmation_result.html', 
                             message='Katılımınız daha önce teyit edilmiştir',
                             training=enrollment.training,
                             already_confirmed=True)
    
    # Katılımı teyit et
    enrollment.confirm_participation()
    
    return render_template('confirmation_result.html',
                         message='Katılımınız başarıyla teyit edildi',
                         training=enrollment.training,
                         user=enrollment.user,
                         already_confirmed=False)

@api_bp.route('/manager/survey/<token>')
def manager_survey(token):
    """Yönetici anketi (E-posta linki)"""
    # Token'ı doğrula
    data = verify_token(token, salt='survey_email')
    if not data:
        return render_template('error.html', 
                             message='Geçersiz veya süresi dolmuş anket linki'), 400
    
    manager_id = data.get('manager_id')
    training_id = data.get('training_id')
    
    from app.models.user import User
    manager = User.query.get(manager_id)
    training = Training.query.get(training_id)
    
    if not manager or not training:
        return render_template('error.html', message='Geçersiz anket linki'), 404
    
    # Anket sorularını hazırla (şimdilik sabit sorular)
    survey_questions = [
        {
            'question': 'Eğitimin genel kalitesini nasıl değerlendiriyorsunuz?',
            'type': 'rating',
            'options': ['1 - Çok Kötü', '2 - Kötü', '3 - Orta', '4 - İyi', '5 - Çok İyi']
        },
        {
            'question': 'Eğitim içeriği iş süreçlerinize uygun muydu?',
            'type': 'rating',
            'options': ['1 - Hiç Uygun Değil', '2 - Uygun Değil', '3 - Orta', '4 - Uygun', '5 - Çok Uygun']
        },
        {
            'question': 'Çalışanınızın eğitim sonrası performansında gözlemlediğiniz değişiklikler:',
            'type': 'text'
        }
    ]
    
    return render_template('manager_survey.html',
                         manager=manager,
                         training=training,
                         questions=survey_questions,
                         token=token)

@api_bp.route('/twilio/webhook', methods=['POST'])
def twilio_webhook():
    """Twilio SMS webhook'u"""
    # Twilio'dan gelen SMS yanıtlarını işle
    from_number = request.form.get('From')
    body = request.form.get('Body', '').lower().strip()
    sms_sid = request.form.get('MessageSid')
    
    # SMS yanıtına göre işlem yap
    if 'evet' in body or 'yes' in body or 'e' in body:
        # Katılımı teyit et
        # Telefon numarasından kullanıcıyı bul
        from app.models.user import User
        user = User.query.filter_by(telefon_numarasi=from_number).first()
        if user:
            # Beklemedeki kayıtları teyit et
            pending_enrollments = user.enrollments.filter_by(
                katilim_teyidi_durumu='beklemede'
            ).all()
            
            for enrollment in pending_enrollments:
                enrollment.confirm_participation()
    
    return '', 200  # Twilio için boş yanıt
