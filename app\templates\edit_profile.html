{% extends "base.html" %}

{% block title %}Profil <PERSON> - {{ user.tam_ad }}{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-edit"></i> Profil <PERSON>le
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{{ url_for('auth.profile') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left"></i> Profil
        </a>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card shadow">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-user-edit"></i> <PERSON><PERSON><PERSON><PERSON>
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" id="editProfileForm">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="ad" class="form-label">Ad *</label>
                            <input type="text" class="form-control" id="ad" name="ad" 
                                   value="{{ user.ad }}" required>
                        </div>
                        <div class="col-md-6">
                            <label for="soyad" class="form-label">Soyad *</label>
                            <input type="text" class="form-control" id="soyad" name="soyad" 
                                   value="{{ user.soyad }}" required>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="telefon_numarasi" class="form-label">Telefon Numarası *</label>
                        <input type="tel" class="form-control" id="telefon_numarasi" name="telefon_numarasi" 
                               value="{{ user.telefon_numarasi }}" placeholder="05551234567" required>
                        <div class="form-text">SMS bildirimleri için kullanılacak</div>
                    </div>
                    
                    <!-- Salt okunur bilgiler -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label">E-posta Adresi</label>
                            <input type="email" class="form-control" value="{{ user.email }}" readonly>
                            <div class="form-text">E-posta adresi değiştirilemez</div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Rol</label>
                            <input type="text" class="form-control" 
                                   value="{% if user.rol == 'admin' %}Admin{% elif user.rol == 'manager' %}Manager{% else %}Participant{% endif %}" 
                                   readonly>
                            <div class="form-text">Rol değiştirilemez</div>
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label">Kayıt Tarihi</label>
                            <input type="text" class="form-control" 
                                   value="{{ user.olusturulma_tarihi.strftime('%d.%m.%Y %H:%M') if user.olusturulma_tarihi }}" 
                                   readonly>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Son Giriş</label>
                            <input type="text" class="form-control" 
                                   value="{% if user.son_giris_tarihi %}{{ user.son_giris_tarihi.strftime('%d.%m.%Y %H:%M') }}{% else %}Hiç giriş yapmamış{% endif %}" 
                                   readonly>
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('auth.profile') }}" class="btn btn-secondary">
                            <i class="fas fa-times"></i> İptal
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Değişiklikleri Kaydet
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card shadow">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-info-circle"></i> Bilgi</h6>
            </div>
            <div class="card-body">
                <h6>Düzenlenebilir Alanlar:</h6>
                <ul class="small">
                    <li><strong>Ad:</strong> Adınızı güncelleyebilirsiniz</li>
                    <li><strong>Soyad:</strong> Soyadınızı güncelleyebilirsiniz</li>
                    <li><strong>Telefon:</strong> SMS bildirimleri için telefon numaranızı güncelleyebilirsiniz</li>
                </ul>
                
                <hr>
                
                <h6>Değiştirilemeyen Alanlar:</h6>
                <ul class="small">
                    <li><strong>E-posta:</strong> Giriş kimliğiniz olduğu için değiştirilemez</li>
                    <li><strong>Rol:</strong> Güvenlik nedeniyle sadece admin değiştirebilir</li>
                    <li><strong>Sistem Bilgileri:</strong> Otomatik olarak güncellenir</li>
                </ul>
                
                <hr>
                
                <div class="d-grid">
                    <a href="{{ url_for('auth.change_password') }}" class="btn btn-outline-warning">
                        <i class="fas fa-key"></i> Şifre Değiştir
                    </a>
                </div>
            </div>
        </div>
        
        <div class="card shadow mt-4">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-shield-alt"></i> Güvenlik</h6>
            </div>
            <div class="card-body">
                <div class="small">
                    <p><strong>Güvenlik İpuçları:</strong></p>
                    <ul>
                        <li>Telefon numaranızı güncel tutun</li>
                        <li>Düzenli olarak şifrenizi değiştirin</li>
                        <li>Şüpheli aktivite fark ederseniz bildirin</li>
                    </ul>
                    
                    <p class="mt-3"><strong>Veri Güvenliği:</strong></p>
                    <ul>
                        <li>Tüm değişiklikler loglanır</li>
                        <li>Kişisel verileriniz şifrelenir</li>
                        <li>Sadece siz profilinizi düzenleyebilirsiniz</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Form validasyonu
    $('#editProfileForm').on('submit', function(e) {
        if (!validateForm()) {
            e.preventDefault();
        }
    });
    
    // Telefon numarası formatı
    $('#telefon_numarasi').on('input', function() {
        formatPhoneNumber(this);
    });
    
    // Değişiklik takibi
    let originalData = {
        ad: $('#ad').val(),
        soyad: $('#soyad').val(),
        telefon_numarasi: $('#telefon_numarasi').val()
    };
    
    $('input[type="text"], input[type="tel"]').on('input', function() {
        checkForChanges();
    });
    
    function checkForChanges() {
        let hasChanges = false;
        
        if ($('#ad').val() !== originalData.ad ||
            $('#soyad').val() !== originalData.soyad ||
            $('#telefon_numarasi').val() !== originalData.telefon_numarasi) {
            hasChanges = true;
        }
        
        if (hasChanges) {
            $('button[type="submit"]').removeClass('btn-primary').addClass('btn-warning');
            $('button[type="submit"]').html('<i class="fas fa-save"></i> Değişiklikleri Kaydet *');
        } else {
            $('button[type="submit"]').removeClass('btn-warning').addClass('btn-primary');
            $('button[type="submit"]').html('<i class="fas fa-save"></i> Değişiklikleri Kaydet');
        }
    }
});

function validateForm() {
    let isValid = true;
    
    // Ad kontrolü
    if ($('#ad').val().trim().length < 2) {
        alert('Ad en az 2 karakter olmalıdır!');
        isValid = false;
    }
    
    // Soyad kontrolü
    if ($('#soyad').val().trim().length < 2) {
        alert('Soyad en az 2 karakter olmalıdır!');
        isValid = false;
    }
    
    // Telefon numarası kontrolü
    let phone = $('#telefon_numarasi').val().replace(/\D/g, '');
    if (phone.length !== 11 || !phone.startsWith('05')) {
        alert('Telefon numarası 05xxxxxxxxx formatında olmalıdır!');
        isValid = false;
    }
    
    return isValid;
}

function formatPhoneNumber(input) {
    // Sadece rakamları al
    let value = input.value.replace(/\D/g, '');
    
    // 05 ile başlamazsa ekle
    if (value.length > 0 && !value.startsWith('05')) {
        if (value.startsWith('5')) {
            value = '0' + value;
        } else if (value.startsWith('0') && !value.startsWith('05')) {
            value = '05' + value.substring(1);
        }
    }
    
    // Maksimum 11 karakter
    if (value.length > 11) {
        value = value.substring(0, 11);
    }
    
    input.value = value;
    
    // Validasyon göstergesi
    if (value.length === 11 && value.startsWith('05')) {
        $(input).removeClass('is-invalid').addClass('is-valid');
    } else if (value.length > 0) {
        $(input).removeClass('is-valid').addClass('is-invalid');
    } else {
        $(input).removeClass('is-valid is-invalid');
    }
}

// Sayfa kapatılmaya çalışıldığında uyar (değişiklik varsa)
window.addEventListener('beforeunload', function(e) {
    let hasChanges = false;
    
    let originalData = {
        ad: '{{ user.ad }}',
        soyad: '{{ user.soyad }}',
        telefon_numarasi: '{{ user.telefon_numarasi }}'
    };
    
    if ($('#ad').val() !== originalData.ad ||
        $('#soyad').val() !== originalData.soyad ||
        $('#telefon_numarasi').val() !== originalData.telefon_numarasi) {
        hasChanges = true;
    }
    
    if (hasChanges) {
        e.preventDefault();
        e.returnValue = 'Kaydedilmemiş değişiklikler var. Sayfayı kapatmak istediğinizden emin misiniz?';
    }
});
</script>
{% endblock %}
