{% extends "base.html" %}

{% block title %}Sistem Bilgileri - Eğitim Yönetim Sistemi{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-info-circle"></i> Sistem Bilgileri
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('settings.index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> Ayarlara Dön
            </a>
            <button type="button" class="btn btn-outline-primary" onclick="refreshPage()">
                <i class="fas fa-sync-alt"></i> Yenile
            </button>
        </div>
    </div>
</div>

<div class="row">
    <!-- Sistem Bilgileri -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-server"></i> Sistem Bilgileri
                </h5>
            </div>
            <div class="card-body">
                <table class="table table-sm">
                    <tr>
                        <td><strong>Platform:</strong></td>
                        <td>{{ system_info.platform }}</td>
                    </tr>
                    <tr>
                        <td><strong>İşletim Sistemi:</strong></td>
                        <td>{{ system_info.system }} {{ system_info.release }}</td>
                    </tr>
                    <tr>
                        <td><strong>Mimari:</strong></td>
                        <td>{{ system_info.architecture }}</td>
                    </tr>
                    <tr>
                        <td><strong>İşlemci:</strong></td>
                        <td>{{ system_info.processor }}</td>
                    </tr>
                    <tr>
                        <td><strong>Python Sürümü:</strong></td>
                        <td>{{ system_info.python_version }}</td>
                    </tr>
                    <tr>
                        <td><strong>CPU Sayısı:</strong></td>
                        <td>{{ system_info.cpu_count }}</td>
                    </tr>
                    <tr>
                        <td><strong>Toplam RAM:</strong></td>
                        <td>
                            {% if system_info.memory_total != 'psutil gerekli' %}
                            {{ system_info.memory_total }} GB
                            {% else %}
                            <span class="text-muted">{{ system_info.memory_total }}</span>
                            {% endif %}
                        </td>
                    </tr>
                    <tr>
                        <td><strong>Kullanılabilir RAM:</strong></td>
                        <td>
                            {% if system_info.memory_available != 'psutil gerekli' %}
                            {{ system_info.memory_available }} GB ({{ system_info.memory_percent }}% kullanımda)
                            {% else %}
                            <span class="text-muted">{{ system_info.memory_available }}</span>
                            {% endif %}
                        </td>
                    </tr>
                    <tr>
                        <td><strong>Disk Kullanımı:</strong></td>
                        <td>
                            {% if system_info.disk_usage != 'psutil gerekli' %}
                            %{{ system_info.disk_usage }}
                            {% else %}
                            <span class="text-muted">{{ system_info.disk_usage }}</span>
                            {% endif %}
                        </td>
                    </tr>
                    <tr>
                        <td><strong>Sistem Çalışma Süresi:</strong></td>
                        <td>
                            {% if system_info.uptime != 'psutil gerekli' %}
                            {{ system_info.uptime }}
                            {% else %}
                            <span class="text-muted">{{ system_info.uptime }}</span>
                            {% endif %}
                        </td>
                    </tr>
                </table>
            </div>
        </div>
    </div>

    <!-- Flask Bilgileri -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fab fa-python"></i> Flask Uygulaması
                </h5>
            </div>
            <div class="card-body">
                <table class="table table-sm">
                    <tr>
                        <td><strong>Debug Modu:</strong></td>
                        <td>
                            <span class="badge {{ 'bg-warning' if flask_info.debug_mode else 'bg-success' }}">
                                {{ 'Aktif' if flask_info.debug_mode else 'Pasif' }}
                            </span>
                        </td>
                    </tr>
                    <tr>
                        <td><strong>Test Modu:</strong></td>
                        <td>
                            <span class="badge {{ 'bg-info' if flask_info.testing else 'bg-secondary' }}">
                                {{ 'Aktif' if flask_info.testing else 'Pasif' }}
                            </span>
                        </td>
                    </tr>
                    <tr>
                        <td><strong>Secret Key:</strong></td>
                        <td>
                            <span class="badge {{ 'bg-success' if flask_info.secret_key_set else 'bg-danger' }}">
                                {{ 'Ayarlanmış' if flask_info.secret_key_set else 'Ayarlanmamış' }}
                            </span>
                        </td>
                    </tr>
                    <tr>
                        <td><strong>Veritabanı URI:</strong></td>
                        <td>
                            <small class="font-monospace">{{ flask_info.database_uri }}</small>
                        </td>
                    </tr>
                </table>
            </div>
        </div>
    </div>

    <!-- Veritabanı Bilgileri -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-database"></i> Veritabanı İstatistikleri
                </h5>
            </div>
            <div class="card-body">
                {% if db_info.error %}
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i>
                    {{ db_info.error }}
                </div>
                {% else %}
                <table class="table table-sm">
                    <tr>
                        <td><strong>Kullanıcılar:</strong></td>
                        <td>
                            <span class="badge bg-primary">{{ db_info.users_count }}</span>
                        </td>
                    </tr>
                    <tr>
                        <td><strong>Şirketler:</strong></td>
                        <td>
                            <span class="badge bg-info">{{ db_info.companies_count }}</span>
                        </td>
                    </tr>
                    <tr>
                        <td><strong>Eğitimler:</strong></td>
                        <td>
                            <span class="badge bg-success">{{ db_info.trainings_count }}</span>
                        </td>
                    </tr>
                    <tr>
                        <td><strong>Materyaller:</strong></td>
                        <td>
                            <span class="badge bg-warning">{{ db_info.materials_count }}</span>
                        </td>
                    </tr>
                    <tr>
                        <td><strong>Sistem Ayarları:</strong></td>
                        <td>
                            <span class="badge bg-secondary">{{ db_info.settings_count }}</span>
                        </td>
                    </tr>
                </table>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Sistem Durumu -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-heartbeat"></i> Sistem Durumu
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6 mb-3">
                        <div class="border rounded p-3">
                            <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                            <h6>Web Sunucusu</h6>
                            <span class="badge bg-success">Çalışıyor</span>
                        </div>
                    </div>
                    <div class="col-6 mb-3">
                        <div class="border rounded p-3">
                            <i class="fas fa-database fa-2x text-success mb-2"></i>
                            <h6>Veritabanı</h6>
                            <span class="badge bg-success">Bağlı</span>
                        </div>
                    </div>
                    <div class="col-6 mb-3">
                        <div class="border rounded p-3">
                            <i class="fas fa-cogs fa-2x text-info mb-2"></i>
                            <h6>Ayarlar</h6>
                            <span class="badge bg-info">{{ db_info.settings_count or 0 }} Ayar</span>
                        </div>
                    </div>
                    <div class="col-6 mb-3">
                        <div class="border rounded p-3">
                            <i class="fas fa-users fa-2x text-primary mb-2"></i>
                            <h6>Aktif Kullanıcılar</h6>
                            <span class="badge bg-primary">{{ db_info.users_count or 0 }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Ek Bilgiler -->
<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-tools"></i> Sistem Araçları
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-2">
                        <button class="btn btn-outline-info w-100" onclick="checkDatabaseConnection()">
                            <i class="fas fa-database"></i> DB Bağlantısı Test Et
                        </button>
                    </div>
                    <div class="col-md-3 mb-2">
                        <button class="btn btn-outline-warning w-100" onclick="clearTempFiles()">
                            <i class="fas fa-broom"></i> Geçici Dosyaları Temizle
                        </button>
                    </div>
                    <div class="col-md-3 mb-2">
                        <button class="btn btn-outline-success w-100" onclick="exportSystemInfo()">
                            <i class="fas fa-download"></i> Sistem Raporu İndir
                        </button>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="{{ url_for('settings.view_logs') }}" class="btn btn-outline-secondary w-100">
                            <i class="fas fa-file-alt"></i> Sistem Logları
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% if system_info.memory_total == 'psutil gerekli' %}
<div class="alert alert-info mt-4">
    <i class="fas fa-info-circle"></i>
    <strong>Bilgi:</strong> Daha detaylı sistem bilgileri için <code>pip install psutil</code> komutunu çalıştırın.
</div>
{% endif %}
{% endblock %}

{% block extra_css %}
<style>
.font-monospace {
    font-family: 'Courier New', monospace;
    font-size: 0.85rem;
}

.card {
    border: none;
    border-radius: 10px;
}

.border {
    border-radius: 8px !important;
}

.badge {
    font-size: 0.8rem;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
function refreshPage() {
    location.reload();
}

function checkDatabaseConnection() {
    alert('🔄 Veritabanı bağlantısı kontrol ediliyor...');
    // TODO: Implement database connection test
}

function clearTempFiles() {
    if (confirm('Geçici dosyalar temizlensin mi?')) {
        alert('🔄 Geçici dosyalar temizleniyor...');
        // TODO: Implement temp file cleanup
    }
}

function exportSystemInfo() {
    // Sistem bilgilerini JSON olarak indir
    const systemData = {
        timestamp: new Date().toISOString(),
        system_info: {{ system_info | tojson }},
        flask_info: {{ flask_info | tojson }},
        db_info: {{ db_info | tojson }}
    };
    
    const blob = new Blob([JSON.stringify(systemData, null, 2)], {type: 'application/json'});
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `system_info_${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
}
</script>
{% endblock %}
