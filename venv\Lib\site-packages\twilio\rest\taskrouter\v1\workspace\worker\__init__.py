r"""
    This code was generated by
   ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
    |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
    |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

    Twilio - Taskrouter
    This is the public Twilio REST API.

    NOTE: This class is auto generated by OpenAPI Generator.
    https://openapi-generator.tech
    Do not edit the class manually.
"""


from datetime import datetime
from typing import Any, Dict, List, Optional, Union, Iterator, AsyncIterator
from twilio.base import deserialize, values
from twilio.base.instance_context import InstanceContext
from twilio.base.instance_resource import InstanceResource
from twilio.base.list_resource import ListResource
from twilio.base.version import Version
from twilio.base.page import Page
from twilio.rest.taskrouter.v1.workspace.worker.reservation import ReservationList
from twilio.rest.taskrouter.v1.workspace.worker.worker_channel import WorkerChannelList
from twilio.rest.taskrouter.v1.workspace.worker.worker_statistics import (
    WorkerStatisticsList,
)
from twilio.rest.taskrouter.v1.workspace.worker.workers_cumulative_statistics import (
    WorkersCumulativeStatisticsList,
)
from twilio.rest.taskrouter.v1.workspace.worker.workers_real_time_statistics import (
    WorkersRealTimeStatisticsList,
)
from twilio.rest.taskrouter.v1.workspace.worker.workers_statistics import (
    WorkersStatisticsList,
)


class WorkerInstance(InstanceResource):

    """
    :ivar account_sid: The SID of the [Account](https://www.twilio.com/docs/iam/api/account) that created the Worker resource.
    :ivar activity_name: The `friendly_name` of the Worker's current Activity.
    :ivar activity_sid: The SID of the Worker's current Activity.
    :ivar attributes: The JSON string that describes the Worker. For example: `{ \"email\": \"<EMAIL>\", \"phone\": \"+**********\" }`. **Note** If this property has been assigned a value, it will only be displayed in FETCH actions that return a single resource. Otherwise, this property will be null, even if it has a value. This data is passed to the `assignment_callback_url` when TaskRouter assigns a Task to the Worker.
    :ivar available: Whether the Worker is available to perform tasks.
    :ivar date_created: The date and time in GMT when the resource was created specified in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format.
    :ivar date_status_changed: The date and time in GMT of the last change to the Worker's activity specified in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format. Used to calculate Workflow statistics.
    :ivar date_updated: The date and time in GMT when the resource was last updated specified in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format.
    :ivar friendly_name: The string that you assigned to describe the resource. Friendly names are case insensitive, and unique within the TaskRouter Workspace.
    :ivar sid: The unique string that we created to identify the Worker resource.
    :ivar workspace_sid: The SID of the Workspace that contains the Worker.
    :ivar url: The absolute URL of the Worker resource.
    :ivar links: The URLs of related resources.
    """

    def __init__(
        self,
        version: Version,
        payload: Dict[str, Any],
        workspace_sid: str,
        sid: Optional[str] = None,
    ):
        super().__init__(version)

        self.account_sid: Optional[str] = payload.get("account_sid")
        self.activity_name: Optional[str] = payload.get("activity_name")
        self.activity_sid: Optional[str] = payload.get("activity_sid")
        self.attributes: Optional[str] = payload.get("attributes")
        self.available: Optional[bool] = payload.get("available")
        self.date_created: Optional[datetime] = deserialize.iso8601_datetime(
            payload.get("date_created")
        )
        self.date_status_changed: Optional[datetime] = deserialize.iso8601_datetime(
            payload.get("date_status_changed")
        )
        self.date_updated: Optional[datetime] = deserialize.iso8601_datetime(
            payload.get("date_updated")
        )
        self.friendly_name: Optional[str] = payload.get("friendly_name")
        self.sid: Optional[str] = payload.get("sid")
        self.workspace_sid: Optional[str] = payload.get("workspace_sid")
        self.url: Optional[str] = payload.get("url")
        self.links: Optional[Dict[str, object]] = payload.get("links")

        self._solution = {
            "workspace_sid": workspace_sid,
            "sid": sid or self.sid,
        }
        self._context: Optional[WorkerContext] = None

    @property
    def _proxy(self) -> "WorkerContext":
        """
        Generate an instance context for the instance, the context is capable of
        performing various actions. All instance actions are proxied to the context

        :returns: WorkerContext for this WorkerInstance
        """
        if self._context is None:
            self._context = WorkerContext(
                self._version,
                workspace_sid=self._solution["workspace_sid"],
                sid=self._solution["sid"],
            )
        return self._context

    def delete(self, if_match: Union[str, object] = values.unset) -> bool:
        """
        Deletes the WorkerInstance

        :param if_match: The If-Match HTTP request header

        :returns: True if delete succeeds, False otherwise
        """
        return self._proxy.delete(
            if_match=if_match,
        )

    async def delete_async(self, if_match: Union[str, object] = values.unset) -> bool:
        """
        Asynchronous coroutine that deletes the WorkerInstance

        :param if_match: The If-Match HTTP request header

        :returns: True if delete succeeds, False otherwise
        """
        return await self._proxy.delete_async(
            if_match=if_match,
        )

    def fetch(self) -> "WorkerInstance":
        """
        Fetch the WorkerInstance


        :returns: The fetched WorkerInstance
        """
        return self._proxy.fetch()

    async def fetch_async(self) -> "WorkerInstance":
        """
        Asynchronous coroutine to fetch the WorkerInstance


        :returns: The fetched WorkerInstance
        """
        return await self._proxy.fetch_async()

    def update(
        self,
        if_match: Union[str, object] = values.unset,
        activity_sid: Union[str, object] = values.unset,
        attributes: Union[str, object] = values.unset,
        friendly_name: Union[str, object] = values.unset,
        reject_pending_reservations: Union[bool, object] = values.unset,
    ) -> "WorkerInstance":
        """
        Update the WorkerInstance

        :param if_match: The If-Match HTTP request header
        :param activity_sid: The SID of a valid Activity that will describe the Worker's initial state. See [Activities](https://www.twilio.com/docs/taskrouter/api/activity) for more information.
        :param attributes: The JSON string that describes the Worker. For example: `{ \\\"email\\\": \\\"<EMAIL>\\\", \\\"phone\\\": \\\"+**********\\\" }`. This data is passed to the `assignment_callback_url` when TaskRouter assigns a Task to the Worker. Defaults to {}.
        :param friendly_name: A descriptive string that you create to describe the Worker. It can be up to 64 characters long.
        :param reject_pending_reservations: Whether to reject the Worker's pending reservations. This option is only valid if the Worker's new [Activity](https://www.twilio.com/docs/taskrouter/api/activity) resource has its `availability` property set to `False`.

        :returns: The updated WorkerInstance
        """
        return self._proxy.update(
            if_match=if_match,
            activity_sid=activity_sid,
            attributes=attributes,
            friendly_name=friendly_name,
            reject_pending_reservations=reject_pending_reservations,
        )

    async def update_async(
        self,
        if_match: Union[str, object] = values.unset,
        activity_sid: Union[str, object] = values.unset,
        attributes: Union[str, object] = values.unset,
        friendly_name: Union[str, object] = values.unset,
        reject_pending_reservations: Union[bool, object] = values.unset,
    ) -> "WorkerInstance":
        """
        Asynchronous coroutine to update the WorkerInstance

        :param if_match: The If-Match HTTP request header
        :param activity_sid: The SID of a valid Activity that will describe the Worker's initial state. See [Activities](https://www.twilio.com/docs/taskrouter/api/activity) for more information.
        :param attributes: The JSON string that describes the Worker. For example: `{ \\\"email\\\": \\\"<EMAIL>\\\", \\\"phone\\\": \\\"+**********\\\" }`. This data is passed to the `assignment_callback_url` when TaskRouter assigns a Task to the Worker. Defaults to {}.
        :param friendly_name: A descriptive string that you create to describe the Worker. It can be up to 64 characters long.
        :param reject_pending_reservations: Whether to reject the Worker's pending reservations. This option is only valid if the Worker's new [Activity](https://www.twilio.com/docs/taskrouter/api/activity) resource has its `availability` property set to `False`.

        :returns: The updated WorkerInstance
        """
        return await self._proxy.update_async(
            if_match=if_match,
            activity_sid=activity_sid,
            attributes=attributes,
            friendly_name=friendly_name,
            reject_pending_reservations=reject_pending_reservations,
        )

    @property
    def reservations(self) -> ReservationList:
        """
        Access the reservations
        """
        return self._proxy.reservations

    @property
    def worker_channels(self) -> WorkerChannelList:
        """
        Access the worker_channels
        """
        return self._proxy.worker_channels

    @property
    def statistics(self) -> WorkerStatisticsList:
        """
        Access the statistics
        """
        return self._proxy.statistics

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Taskrouter.V1.WorkerInstance {}>".format(context)


class WorkerContext(InstanceContext):
    def __init__(self, version: Version, workspace_sid: str, sid: str):
        """
        Initialize the WorkerContext

        :param version: Version that contains the resource
        :param workspace_sid: The SID of the Workspace with the Worker to update.
        :param sid: The SID of the Worker resource to update.
        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "workspace_sid": workspace_sid,
            "sid": sid,
        }
        self._uri = "/Workspaces/{workspace_sid}/Workers/{sid}".format(**self._solution)

        self._reservations: Optional[ReservationList] = None
        self._worker_channels: Optional[WorkerChannelList] = None
        self._statistics: Optional[WorkerStatisticsList] = None

    def delete(self, if_match: Union[str, object] = values.unset) -> bool:
        """
        Deletes the WorkerInstance

        :param if_match: The If-Match HTTP request header

        :returns: True if delete succeeds, False otherwise
        """
        headers = values.of(
            {
                "If-Match": if_match,
            }
        )

        return self._version.delete(method="DELETE", uri=self._uri, headers=headers)

    async def delete_async(self, if_match: Union[str, object] = values.unset) -> bool:
        """
        Asynchronous coroutine that deletes the WorkerInstance

        :param if_match: The If-Match HTTP request header

        :returns: True if delete succeeds, False otherwise
        """
        headers = values.of(
            {
                "If-Match": if_match,
            }
        )

        return await self._version.delete_async(
            method="DELETE", uri=self._uri, headers=headers
        )

    def fetch(self) -> WorkerInstance:
        """
        Fetch the WorkerInstance


        :returns: The fetched WorkerInstance
        """

        payload = self._version.fetch(
            method="GET",
            uri=self._uri,
        )

        return WorkerInstance(
            self._version,
            payload,
            workspace_sid=self._solution["workspace_sid"],
            sid=self._solution["sid"],
        )

    async def fetch_async(self) -> WorkerInstance:
        """
        Asynchronous coroutine to fetch the WorkerInstance


        :returns: The fetched WorkerInstance
        """

        payload = await self._version.fetch_async(
            method="GET",
            uri=self._uri,
        )

        return WorkerInstance(
            self._version,
            payload,
            workspace_sid=self._solution["workspace_sid"],
            sid=self._solution["sid"],
        )

    def update(
        self,
        if_match: Union[str, object] = values.unset,
        activity_sid: Union[str, object] = values.unset,
        attributes: Union[str, object] = values.unset,
        friendly_name: Union[str, object] = values.unset,
        reject_pending_reservations: Union[bool, object] = values.unset,
    ) -> WorkerInstance:
        """
        Update the WorkerInstance

        :param if_match: The If-Match HTTP request header
        :param activity_sid: The SID of a valid Activity that will describe the Worker's initial state. See [Activities](https://www.twilio.com/docs/taskrouter/api/activity) for more information.
        :param attributes: The JSON string that describes the Worker. For example: `{ \\\"email\\\": \\\"<EMAIL>\\\", \\\"phone\\\": \\\"+**********\\\" }`. This data is passed to the `assignment_callback_url` when TaskRouter assigns a Task to the Worker. Defaults to {}.
        :param friendly_name: A descriptive string that you create to describe the Worker. It can be up to 64 characters long.
        :param reject_pending_reservations: Whether to reject the Worker's pending reservations. This option is only valid if the Worker's new [Activity](https://www.twilio.com/docs/taskrouter/api/activity) resource has its `availability` property set to `False`.

        :returns: The updated WorkerInstance
        """
        data = values.of(
            {
                "ActivitySid": activity_sid,
                "Attributes": attributes,
                "FriendlyName": friendly_name,
                "RejectPendingReservations": reject_pending_reservations,
            }
        )
        headers = values.of(
            {
                "If-Match": if_match,
            }
        )

        payload = self._version.update(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return WorkerInstance(
            self._version,
            payload,
            workspace_sid=self._solution["workspace_sid"],
            sid=self._solution["sid"],
        )

    async def update_async(
        self,
        if_match: Union[str, object] = values.unset,
        activity_sid: Union[str, object] = values.unset,
        attributes: Union[str, object] = values.unset,
        friendly_name: Union[str, object] = values.unset,
        reject_pending_reservations: Union[bool, object] = values.unset,
    ) -> WorkerInstance:
        """
        Asynchronous coroutine to update the WorkerInstance

        :param if_match: The If-Match HTTP request header
        :param activity_sid: The SID of a valid Activity that will describe the Worker's initial state. See [Activities](https://www.twilio.com/docs/taskrouter/api/activity) for more information.
        :param attributes: The JSON string that describes the Worker. For example: `{ \\\"email\\\": \\\"<EMAIL>\\\", \\\"phone\\\": \\\"+**********\\\" }`. This data is passed to the `assignment_callback_url` when TaskRouter assigns a Task to the Worker. Defaults to {}.
        :param friendly_name: A descriptive string that you create to describe the Worker. It can be up to 64 characters long.
        :param reject_pending_reservations: Whether to reject the Worker's pending reservations. This option is only valid if the Worker's new [Activity](https://www.twilio.com/docs/taskrouter/api/activity) resource has its `availability` property set to `False`.

        :returns: The updated WorkerInstance
        """
        data = values.of(
            {
                "ActivitySid": activity_sid,
                "Attributes": attributes,
                "FriendlyName": friendly_name,
                "RejectPendingReservations": reject_pending_reservations,
            }
        )
        headers = values.of(
            {
                "If-Match": if_match,
            }
        )

        payload = await self._version.update_async(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return WorkerInstance(
            self._version,
            payload,
            workspace_sid=self._solution["workspace_sid"],
            sid=self._solution["sid"],
        )

    @property
    def reservations(self) -> ReservationList:
        """
        Access the reservations
        """
        if self._reservations is None:
            self._reservations = ReservationList(
                self._version,
                self._solution["workspace_sid"],
                self._solution["sid"],
            )
        return self._reservations

    @property
    def worker_channels(self) -> WorkerChannelList:
        """
        Access the worker_channels
        """
        if self._worker_channels is None:
            self._worker_channels = WorkerChannelList(
                self._version,
                self._solution["workspace_sid"],
                self._solution["sid"],
            )
        return self._worker_channels

    @property
    def statistics(self) -> WorkerStatisticsList:
        """
        Access the statistics
        """
        if self._statistics is None:
            self._statistics = WorkerStatisticsList(
                self._version,
                self._solution["workspace_sid"],
                self._solution["sid"],
            )
        return self._statistics

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Taskrouter.V1.WorkerContext {}>".format(context)


class WorkerPage(Page):
    def get_instance(self, payload: Dict[str, Any]) -> WorkerInstance:
        """
        Build an instance of WorkerInstance

        :param payload: Payload response from the API
        """
        return WorkerInstance(
            self._version, payload, workspace_sid=self._solution["workspace_sid"]
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Taskrouter.V1.WorkerPage>"


class WorkerList(ListResource):
    def __init__(self, version: Version, workspace_sid: str):
        """
        Initialize the WorkerList

        :param version: Version that contains the resource
        :param workspace_sid: The SID of the Workspace with the Workers to read.

        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "workspace_sid": workspace_sid,
        }
        self._uri = "/Workspaces/{workspace_sid}/Workers".format(**self._solution)

        self._cumulative_statistics: Optional[WorkersCumulativeStatisticsList] = None
        self._real_time_statistics: Optional[WorkersRealTimeStatisticsList] = None
        self._statistics: Optional[WorkersStatisticsList] = None

    def create(
        self,
        friendly_name: str,
        activity_sid: Union[str, object] = values.unset,
        attributes: Union[str, object] = values.unset,
    ) -> WorkerInstance:
        """
        Create the WorkerInstance

        :param friendly_name: A descriptive string that you create to describe the new Worker. It can be up to 64 characters long.
        :param activity_sid: The SID of a valid Activity that will describe the new Worker's initial state. See [Activities](https://www.twilio.com/docs/taskrouter/api/activity) for more information. If not provided, the new Worker's initial state is the `default_activity_sid` configured on the Workspace.
        :param attributes: A valid JSON string that describes the new Worker. For example: `{ \\\"email\\\": \\\"<EMAIL>\\\", \\\"phone\\\": \\\"+**********\\\" }`. This data is passed to the `assignment_callback_url` when TaskRouter assigns a Task to the Worker. Defaults to {}.

        :returns: The created WorkerInstance
        """
        data = values.of(
            {
                "FriendlyName": friendly_name,
                "ActivitySid": activity_sid,
                "Attributes": attributes,
            }
        )

        payload = self._version.create(
            method="POST",
            uri=self._uri,
            data=data,
        )

        return WorkerInstance(
            self._version, payload, workspace_sid=self._solution["workspace_sid"]
        )

    async def create_async(
        self,
        friendly_name: str,
        activity_sid: Union[str, object] = values.unset,
        attributes: Union[str, object] = values.unset,
    ) -> WorkerInstance:
        """
        Asynchronously create the WorkerInstance

        :param friendly_name: A descriptive string that you create to describe the new Worker. It can be up to 64 characters long.
        :param activity_sid: The SID of a valid Activity that will describe the new Worker's initial state. See [Activities](https://www.twilio.com/docs/taskrouter/api/activity) for more information. If not provided, the new Worker's initial state is the `default_activity_sid` configured on the Workspace.
        :param attributes: A valid JSON string that describes the new Worker. For example: `{ \\\"email\\\": \\\"<EMAIL>\\\", \\\"phone\\\": \\\"+**********\\\" }`. This data is passed to the `assignment_callback_url` when TaskRouter assigns a Task to the Worker. Defaults to {}.

        :returns: The created WorkerInstance
        """
        data = values.of(
            {
                "FriendlyName": friendly_name,
                "ActivitySid": activity_sid,
                "Attributes": attributes,
            }
        )

        payload = await self._version.create_async(
            method="POST",
            uri=self._uri,
            data=data,
        )

        return WorkerInstance(
            self._version, payload, workspace_sid=self._solution["workspace_sid"]
        )

    def stream(
        self,
        activity_name: Union[str, object] = values.unset,
        activity_sid: Union[str, object] = values.unset,
        available: Union[str, object] = values.unset,
        friendly_name: Union[str, object] = values.unset,
        target_workers_expression: Union[str, object] = values.unset,
        task_queue_name: Union[str, object] = values.unset,
        task_queue_sid: Union[str, object] = values.unset,
        ordering: Union[str, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> Iterator[WorkerInstance]:
        """
        Streams WorkerInstance records from the API as a generator stream.
        This operation lazily loads records as efficiently as possible until the limit
        is reached.
        The results are returned as a generator, so this operation is memory efficient.

        :param str activity_name: The `activity_name` of the Worker resources to read.
        :param str activity_sid: The `activity_sid` of the Worker resources to read.
        :param str available: Whether to return only Worker resources that are available or unavailable. Can be `true`, `1`, or `yes` to return Worker resources that are available, and `false`, or any value returns the Worker resources that are not available.
        :param str friendly_name: The `friendly_name` of the Worker resources to read.
        :param str target_workers_expression: Filter by Workers that would match an expression. In addition to fields in the workers' attributes, the expression can include the following worker fields: `sid`, `friendly_name`, `activity_sid`, or `activity_name`
        :param str task_queue_name: The `friendly_name` of the TaskQueue that the Workers to read are eligible for.
        :param str task_queue_sid: The SID of the TaskQueue that the Workers to read are eligible for.
        :param str ordering: Sorting parameter for Workers
        :param limit: Upper limit for the number of records to return. stream()
                      guarantees to never return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, stream() will attempt to read the
                          limit with the most efficient page size, i.e. min(limit, 1000)

        :returns: Generator that will yield up to limit results
        """
        limits = self._version.read_limits(limit, page_size)
        page = self.page(
            activity_name=activity_name,
            activity_sid=activity_sid,
            available=available,
            friendly_name=friendly_name,
            target_workers_expression=target_workers_expression,
            task_queue_name=task_queue_name,
            task_queue_sid=task_queue_sid,
            ordering=ordering,
            page_size=limits["page_size"],
        )

        return self._version.stream(page, limits["limit"])

    async def stream_async(
        self,
        activity_name: Union[str, object] = values.unset,
        activity_sid: Union[str, object] = values.unset,
        available: Union[str, object] = values.unset,
        friendly_name: Union[str, object] = values.unset,
        target_workers_expression: Union[str, object] = values.unset,
        task_queue_name: Union[str, object] = values.unset,
        task_queue_sid: Union[str, object] = values.unset,
        ordering: Union[str, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> AsyncIterator[WorkerInstance]:
        """
        Asynchronously streams WorkerInstance records from the API as a generator stream.
        This operation lazily loads records as efficiently as possible until the limit
        is reached.
        The results are returned as a generator, so this operation is memory efficient.

        :param str activity_name: The `activity_name` of the Worker resources to read.
        :param str activity_sid: The `activity_sid` of the Worker resources to read.
        :param str available: Whether to return only Worker resources that are available or unavailable. Can be `true`, `1`, or `yes` to return Worker resources that are available, and `false`, or any value returns the Worker resources that are not available.
        :param str friendly_name: The `friendly_name` of the Worker resources to read.
        :param str target_workers_expression: Filter by Workers that would match an expression. In addition to fields in the workers' attributes, the expression can include the following worker fields: `sid`, `friendly_name`, `activity_sid`, or `activity_name`
        :param str task_queue_name: The `friendly_name` of the TaskQueue that the Workers to read are eligible for.
        :param str task_queue_sid: The SID of the TaskQueue that the Workers to read are eligible for.
        :param str ordering: Sorting parameter for Workers
        :param limit: Upper limit for the number of records to return. stream()
                      guarantees to never return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, stream() will attempt to read the
                          limit with the most efficient page size, i.e. min(limit, 1000)

        :returns: Generator that will yield up to limit results
        """
        limits = self._version.read_limits(limit, page_size)
        page = await self.page_async(
            activity_name=activity_name,
            activity_sid=activity_sid,
            available=available,
            friendly_name=friendly_name,
            target_workers_expression=target_workers_expression,
            task_queue_name=task_queue_name,
            task_queue_sid=task_queue_sid,
            ordering=ordering,
            page_size=limits["page_size"],
        )

        return self._version.stream_async(page, limits["limit"])

    def list(
        self,
        activity_name: Union[str, object] = values.unset,
        activity_sid: Union[str, object] = values.unset,
        available: Union[str, object] = values.unset,
        friendly_name: Union[str, object] = values.unset,
        target_workers_expression: Union[str, object] = values.unset,
        task_queue_name: Union[str, object] = values.unset,
        task_queue_sid: Union[str, object] = values.unset,
        ordering: Union[str, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> List[WorkerInstance]:
        """
        Lists WorkerInstance records from the API as a list.
        Unlike stream(), this operation is eager and will load `limit` records into
        memory before returning.

        :param str activity_name: The `activity_name` of the Worker resources to read.
        :param str activity_sid: The `activity_sid` of the Worker resources to read.
        :param str available: Whether to return only Worker resources that are available or unavailable. Can be `true`, `1`, or `yes` to return Worker resources that are available, and `false`, or any value returns the Worker resources that are not available.
        :param str friendly_name: The `friendly_name` of the Worker resources to read.
        :param str target_workers_expression: Filter by Workers that would match an expression. In addition to fields in the workers' attributes, the expression can include the following worker fields: `sid`, `friendly_name`, `activity_sid`, or `activity_name`
        :param str task_queue_name: The `friendly_name` of the TaskQueue that the Workers to read are eligible for.
        :param str task_queue_sid: The SID of the TaskQueue that the Workers to read are eligible for.
        :param str ordering: Sorting parameter for Workers
        :param limit: Upper limit for the number of records to return. list() guarantees
                      never to return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, list() will attempt to read the limit
                          with the most efficient page size, i.e. min(limit, 1000)

        :returns: list that will contain up to limit results
        """
        return list(
            self.stream(
                activity_name=activity_name,
                activity_sid=activity_sid,
                available=available,
                friendly_name=friendly_name,
                target_workers_expression=target_workers_expression,
                task_queue_name=task_queue_name,
                task_queue_sid=task_queue_sid,
                ordering=ordering,
                limit=limit,
                page_size=page_size,
            )
        )

    async def list_async(
        self,
        activity_name: Union[str, object] = values.unset,
        activity_sid: Union[str, object] = values.unset,
        available: Union[str, object] = values.unset,
        friendly_name: Union[str, object] = values.unset,
        target_workers_expression: Union[str, object] = values.unset,
        task_queue_name: Union[str, object] = values.unset,
        task_queue_sid: Union[str, object] = values.unset,
        ordering: Union[str, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> List[WorkerInstance]:
        """
        Asynchronously lists WorkerInstance records from the API as a list.
        Unlike stream(), this operation is eager and will load `limit` records into
        memory before returning.

        :param str activity_name: The `activity_name` of the Worker resources to read.
        :param str activity_sid: The `activity_sid` of the Worker resources to read.
        :param str available: Whether to return only Worker resources that are available or unavailable. Can be `true`, `1`, or `yes` to return Worker resources that are available, and `false`, or any value returns the Worker resources that are not available.
        :param str friendly_name: The `friendly_name` of the Worker resources to read.
        :param str target_workers_expression: Filter by Workers that would match an expression. In addition to fields in the workers' attributes, the expression can include the following worker fields: `sid`, `friendly_name`, `activity_sid`, or `activity_name`
        :param str task_queue_name: The `friendly_name` of the TaskQueue that the Workers to read are eligible for.
        :param str task_queue_sid: The SID of the TaskQueue that the Workers to read are eligible for.
        :param str ordering: Sorting parameter for Workers
        :param limit: Upper limit for the number of records to return. list() guarantees
                      never to return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, list() will attempt to read the limit
                          with the most efficient page size, i.e. min(limit, 1000)

        :returns: list that will contain up to limit results
        """
        return [
            record
            async for record in await self.stream_async(
                activity_name=activity_name,
                activity_sid=activity_sid,
                available=available,
                friendly_name=friendly_name,
                target_workers_expression=target_workers_expression,
                task_queue_name=task_queue_name,
                task_queue_sid=task_queue_sid,
                ordering=ordering,
                limit=limit,
                page_size=page_size,
            )
        ]

    def page(
        self,
        activity_name: Union[str, object] = values.unset,
        activity_sid: Union[str, object] = values.unset,
        available: Union[str, object] = values.unset,
        friendly_name: Union[str, object] = values.unset,
        target_workers_expression: Union[str, object] = values.unset,
        task_queue_name: Union[str, object] = values.unset,
        task_queue_sid: Union[str, object] = values.unset,
        ordering: Union[str, object] = values.unset,
        page_token: Union[str, object] = values.unset,
        page_number: Union[int, object] = values.unset,
        page_size: Union[int, object] = values.unset,
    ) -> WorkerPage:
        """
        Retrieve a single page of WorkerInstance records from the API.
        Request is executed immediately

        :param activity_name: The `activity_name` of the Worker resources to read.
        :param activity_sid: The `activity_sid` of the Worker resources to read.
        :param available: Whether to return only Worker resources that are available or unavailable. Can be `true`, `1`, or `yes` to return Worker resources that are available, and `false`, or any value returns the Worker resources that are not available.
        :param friendly_name: The `friendly_name` of the Worker resources to read.
        :param target_workers_expression: Filter by Workers that would match an expression. In addition to fields in the workers' attributes, the expression can include the following worker fields: `sid`, `friendly_name`, `activity_sid`, or `activity_name`
        :param task_queue_name: The `friendly_name` of the TaskQueue that the Workers to read are eligible for.
        :param task_queue_sid: The SID of the TaskQueue that the Workers to read are eligible for.
        :param ordering: Sorting parameter for Workers
        :param page_token: PageToken provided by the API
        :param page_number: Page Number, this value is simply for client state
        :param page_size: Number of records to return, defaults to 50

        :returns: Page of WorkerInstance
        """
        data = values.of(
            {
                "ActivityName": activity_name,
                "ActivitySid": activity_sid,
                "Available": available,
                "FriendlyName": friendly_name,
                "TargetWorkersExpression": target_workers_expression,
                "TaskQueueName": task_queue_name,
                "TaskQueueSid": task_queue_sid,
                "Ordering": ordering,
                "PageToken": page_token,
                "Page": page_number,
                "PageSize": page_size,
            }
        )

        response = self._version.page(method="GET", uri=self._uri, params=data)
        return WorkerPage(self._version, response, self._solution)

    async def page_async(
        self,
        activity_name: Union[str, object] = values.unset,
        activity_sid: Union[str, object] = values.unset,
        available: Union[str, object] = values.unset,
        friendly_name: Union[str, object] = values.unset,
        target_workers_expression: Union[str, object] = values.unset,
        task_queue_name: Union[str, object] = values.unset,
        task_queue_sid: Union[str, object] = values.unset,
        ordering: Union[str, object] = values.unset,
        page_token: Union[str, object] = values.unset,
        page_number: Union[int, object] = values.unset,
        page_size: Union[int, object] = values.unset,
    ) -> WorkerPage:
        """
        Asynchronously retrieve a single page of WorkerInstance records from the API.
        Request is executed immediately

        :param activity_name: The `activity_name` of the Worker resources to read.
        :param activity_sid: The `activity_sid` of the Worker resources to read.
        :param available: Whether to return only Worker resources that are available or unavailable. Can be `true`, `1`, or `yes` to return Worker resources that are available, and `false`, or any value returns the Worker resources that are not available.
        :param friendly_name: The `friendly_name` of the Worker resources to read.
        :param target_workers_expression: Filter by Workers that would match an expression. In addition to fields in the workers' attributes, the expression can include the following worker fields: `sid`, `friendly_name`, `activity_sid`, or `activity_name`
        :param task_queue_name: The `friendly_name` of the TaskQueue that the Workers to read are eligible for.
        :param task_queue_sid: The SID of the TaskQueue that the Workers to read are eligible for.
        :param ordering: Sorting parameter for Workers
        :param page_token: PageToken provided by the API
        :param page_number: Page Number, this value is simply for client state
        :param page_size: Number of records to return, defaults to 50

        :returns: Page of WorkerInstance
        """
        data = values.of(
            {
                "ActivityName": activity_name,
                "ActivitySid": activity_sid,
                "Available": available,
                "FriendlyName": friendly_name,
                "TargetWorkersExpression": target_workers_expression,
                "TaskQueueName": task_queue_name,
                "TaskQueueSid": task_queue_sid,
                "Ordering": ordering,
                "PageToken": page_token,
                "Page": page_number,
                "PageSize": page_size,
            }
        )

        response = await self._version.page_async(
            method="GET", uri=self._uri, params=data
        )
        return WorkerPage(self._version, response, self._solution)

    def get_page(self, target_url: str) -> WorkerPage:
        """
        Retrieve a specific page of WorkerInstance records from the API.
        Request is executed immediately

        :param target_url: API-generated URL for the requested results page

        :returns: Page of WorkerInstance
        """
        response = self._version.domain.twilio.request("GET", target_url)
        return WorkerPage(self._version, response, self._solution)

    async def get_page_async(self, target_url: str) -> WorkerPage:
        """
        Asynchronously retrieve a specific page of WorkerInstance records from the API.
        Request is executed immediately

        :param target_url: API-generated URL for the requested results page

        :returns: Page of WorkerInstance
        """
        response = await self._version.domain.twilio.request_async("GET", target_url)
        return WorkerPage(self._version, response, self._solution)

    @property
    def cumulative_statistics(self) -> WorkersCumulativeStatisticsList:
        """
        Access the cumulative_statistics
        """
        if self._cumulative_statistics is None:
            self._cumulative_statistics = WorkersCumulativeStatisticsList(
                self._version, workspace_sid=self._solution["workspace_sid"]
            )
        return self._cumulative_statistics

    @property
    def real_time_statistics(self) -> WorkersRealTimeStatisticsList:
        """
        Access the real_time_statistics
        """
        if self._real_time_statistics is None:
            self._real_time_statistics = WorkersRealTimeStatisticsList(
                self._version, workspace_sid=self._solution["workspace_sid"]
            )
        return self._real_time_statistics

    @property
    def statistics(self) -> WorkersStatisticsList:
        """
        Access the statistics
        """
        if self._statistics is None:
            self._statistics = WorkersStatisticsList(
                self._version, workspace_sid=self._solution["workspace_sid"]
            )
        return self._statistics

    def get(self, sid: str) -> WorkerContext:
        """
        Constructs a WorkerContext

        :param sid: The SID of the Worker resource to update.
        """
        return WorkerContext(
            self._version, workspace_sid=self._solution["workspace_sid"], sid=sid
        )

    def __call__(self, sid: str) -> WorkerContext:
        """
        Constructs a WorkerContext

        :param sid: The SID of the Worker resource to update.
        """
        return WorkerContext(
            self._version, workspace_sid=self._solution["workspace_sid"], sid=sid
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Taskrouter.V1.WorkerList>"
