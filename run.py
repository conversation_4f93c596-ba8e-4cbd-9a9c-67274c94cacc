import os
from app import create_app, db
from app.models import User, Training, Enrollment, Quiz, QuizAttempt, CommunicationLog, Survey, SurveyResponse
from app.models.communication import Setting
from app.utils.scheduler import SchedulerService

# Konfigürasyon ortamını belirle
config_name = os.environ.get('FLASK_ENV', 'development')

# Flask uygulamasını oluştur
app = create_app(config_name)

# Scheduler servisini başlat
scheduler = SchedulerService(app)

# Flask shell context
@app.shell_context_processor
def make_shell_context():
    """Flask shell için context sağla"""
    return {
        'db': db,
        'User': User,
        'Training': Training,
        'Enrollment': Enrollment,
        'Quiz': Quiz,
        'QuizAttempt': QuizAttempt,
        'CommunicationLog': CommunicationLog,
        'Survey': Survey,
        'SurveyResponse': SurveyResponse,
        'Setting': Setting
    }

@app.cli.command()
def init_db():
    """Veritabanını başlat"""
    db.create_all()
    print("Veritabanı tabloları oluşturuldu.")

@app.cli.command()
def create_admin():
    """Admin kullanıcısı oluştur"""
    admin_email = input("Admin e-posta adresi: ")
    admin_password = input("Admin şifresi: ")
    admin_ad = input("Admin adı: ")
    admin_soyad = input("Admin soyadı: ")
    admin_telefon = input("Admin telefon numarası: ")
    
    # Admin kullanıcısı var mı kontrol et
    existing_admin = User.query.filter_by(email=admin_email).first()
    if existing_admin:
        print("Bu e-posta adresi zaten kullanılıyor.")
        return
    
    # Yeni admin oluştur
    admin = User(
        ad=admin_ad,
        soyad=admin_soyad,
        email=admin_email,
        telefon_numarasi=admin_telefon,
        rol='admin'
    )
    admin.set_password(admin_password)
    
    db.session.add(admin)
    db.session.commit()
    
    print(f"Admin kullanıcısı oluşturuldu: {admin.tam_ad} ({admin.email})")

@app.cli.command()
def init_settings():
    """Varsayılan ayarları oluştur"""
    default_settings = [
        {
            'key': 'sms_send_days_before',
            'value': '3',
            'description': 'Eğitim başlangıcından kaç gün önce SMS gönderilsin',
            'data_type': 'integer'
        },
        {
            'key': 'survey_send_days_after',
            'value': '1',
            'description': 'Eğitim bitiminden kaç gün sonra anket e-postası gönderilsin',
            'data_type': 'integer'
        },
        {
            'key': 'quiz_time_limit',
            'value': '30',
            'description': 'Quiz süresi (dakika)',
            'data_type': 'integer'
        },
        {
            'key': 'max_quiz_attempts',
            'value': '1',
            'description': 'Maksimum quiz deneme sayısı',
            'data_type': 'integer'
        },
        {
            'key': 'auto_send_sms',
            'value': 'true',
            'description': 'Otomatik SMS gönderimi aktif mi',
            'data_type': 'boolean'
        },
        {
            'key': 'auto_send_survey',
            'value': 'true',
            'description': 'Otomatik anket e-postası gönderimi aktif mi',
            'data_type': 'boolean'
        }
    ]
    
    for setting_data in default_settings:
        existing = Setting.query.filter_by(key=setting_data['key']).first()
        if not existing:
            setting = Setting(**setting_data)
            db.session.add(setting)
    
    db.session.commit()
    print("Varsayılan ayarlar oluşturuldu.")

@app.cli.command()
def create_sample_data():
    """Örnek veri oluştur"""
    # Örnek kullanıcılar
    users_data = [
        {
            'ad': 'Ahmet',
            'soyad': 'Yılmaz',
            'email': '<EMAIL>',
            'telefon_numarasi': '05551234567',
            'rol': 'manager'
        },
        {
            'ad': 'Ayşe',
            'soyad': 'Kaya',
            'email': '<EMAIL>',
            'telefon_numarasi': '05551234568',
            'rol': 'participant'
        },
        {
            'ad': 'Mehmet',
            'soyad': 'Demir',
            'email': '<EMAIL>',
            'telefon_numarasi': '05551234569',
            'rol': 'participant'
        }
    ]
    
    created_users = []
    for user_data in users_data:
        existing = User.query.filter_by(email=user_data['email']).first()
        if not existing:
            user = User(**user_data)
            user.set_password('123456')  # Varsayılan şifre
            db.session.add(user)
            created_users.append(user)
    
    db.session.commit()
    
    # Örnek eğitim
    if created_users:
        from datetime import datetime, timedelta
        
        training = Training(
            ad='Python Programlama Temelleri',
            aciklama='Python programlama diline giriş eğitimi',
            baslangic_tarihi=datetime.utcnow() + timedelta(days=7),
            bitis_tarihi=datetime.utcnow() + timedelta(days=9),
            olusturan_id=created_users[0].id  # İlk kullanıcıyı oluşturan olarak ata
        )
        db.session.add(training)
        db.session.commit()
        
        # Katılımcıları eğitime kaydet
        for user in created_users[1:]:  # İlk kullanıcı hariç (o manager)
            enrollment = Enrollment(
                user_id=user.id,
                training_id=training.id
            )
            db.session.add(enrollment)
        
        db.session.commit()
        print("Örnek veriler oluşturuldu.")

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=1500)
