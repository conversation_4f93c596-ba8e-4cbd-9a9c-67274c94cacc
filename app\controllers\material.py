import os
import uuid
from datetime import datetime
from flask import Blueprint, request, render_template, redirect, url_for, flash, jsonify, send_file, current_app
from flask_login import login_required, current_user
from werkzeug.utils import secure_filename
from app import db
from app.models.training import Training
from app.models.training_material import TrainingMaterial

material_bp = Blueprint('material', __name__, url_prefix='/material')

def can_upload_material(training):
    """Kullanıcı bu eğitime materyal yükleyebilir mi?"""
    if current_user.is_admin:
        return True
    if current_user.is_manager:
        return True
    if current_user.is_instructor and training.egitmen_user_id == current_user.id:
        return True
    return False

def can_view_material(training):
    """Kullanıcı bu eğitimin materyallerini görebilir mi?"""
    if current_user.is_admin or current_user.is_manager:
        return True
    if current_user.is_instructor and training.egitmen_user_id == current_user.id:
        return True
    # Katılımcı bu eğitime kayıtlı mı?
    enrollment = training.enrollments.filter_by(user_id=current_user.id, aktif_mi=True).first()
    return enrollment is not None

@material_bp.route('/training/<int:training_id>')
@login_required
def list_materials(training_id):
    """Eğitim materyallerini listele"""
    training = Training.query.get_or_404(training_id)
    
    if not can_view_material(training):
        flash('Bu eğitimin materyallerini görme yetkiniz yok.', 'error')
        return redirect(url_for('training.detail', training_id=training_id))
    
    materials = training.materials.filter_by(is_active=True).order_by(TrainingMaterial.upload_date.desc()).all()
    
    return render_template('material_list.html', 
                         training=training, 
                         materials=materials,
                         can_upload=can_upload_material(training))

@material_bp.route('/training/<int:training_id>/upload', methods=['GET', 'POST'])
@login_required
def upload_material(training_id):
    """Materyal yükleme"""
    training = Training.query.get_or_404(training_id)
    
    if not can_upload_material(training):
        flash('Bu eğitime materyal yükleme yetkiniz yok.', 'error')
        return redirect(url_for('training.detail', training_id=training_id))
    
    if request.method == 'POST':
        # Dosya kontrolü
        if 'file' not in request.files:
            flash('Dosya seçilmedi.', 'error')
            return render_template('material_upload.html', training=training)
        
        file = request.files['file']
        if file.filename == '':
            flash('Dosya seçilmedi.', 'error')
            return render_template('material_upload.html', training=training)
        
        # Dosya türü kontrolü
        if not TrainingMaterial.is_allowed_file(file.filename):
            flash('Bu dosya türü desteklenmiyor.', 'error')
            return render_template('material_upload.html', training=training)
        
        # Dosya boyutu kontrolü
        file.seek(0, os.SEEK_END)
        file_size = file.tell()
        file.seek(0)
        
        file_type = TrainingMaterial.get_file_type_from_extension(file.filename)
        max_size = TrainingMaterial.get_max_file_size(file_type)
        
        if file_size > max_size:
            max_size_mb = max_size / (1024 * 1024)
            flash(f'Dosya boyutu çok büyük. Maksimum {max_size_mb:.0f}MB olabilir.', 'error')
            return render_template('material_upload.html', training=training)
        
        # Açıklama
        description = request.form.get('description', '').strip()
        
        try:
            # Upload klasörünü oluştur
            upload_folder = os.path.join(current_app.config['UPLOAD_FOLDER'], 'training_materials', str(training_id))
            os.makedirs(upload_folder, exist_ok=True)
            
            # Güvenli dosya adı oluştur
            secure_name = TrainingMaterial.generate_secure_filename(file.filename)
            file_path = os.path.join(upload_folder, secure_name)
            
            # Dosyayı kaydet
            file.save(file_path)
            
            # Veritabanına kaydet
            material = TrainingMaterial(
                training_id=training_id,
                uploaded_by_id=current_user.id,
                file_name=file.filename,
                file_path=file_path,
                file_size=file_size,
                file_type=file_type,
                mime_type=file.content_type or 'application/octet-stream',
                description=description
            )
            
            db.session.add(material)
            db.session.commit()
            
            flash(f'"{file.filename}" başarıyla yüklendi.', 'success')
            return redirect(url_for('material.list_materials', training_id=training_id))
            
        except Exception as e:
            db.session.rollback()
            flash('Dosya yüklenirken bir hata oluştu.', 'error')
            # Dosya varsa sil
            if 'file_path' in locals() and os.path.exists(file_path):
                os.remove(file_path)
    
    return render_template('material_upload.html', training=training)

@material_bp.route('/<int:material_id>/download')
@login_required
def download_material(material_id):
    """Materyal indirme"""
    material = TrainingMaterial.query.get_or_404(material_id)
    
    if not can_view_material(material.training):
        flash('Bu materyali indirme yetkiniz yok.', 'error')
        return redirect(url_for('training.detail', training_id=material.training_id))
    
    if not os.path.exists(material.file_path):
        flash('Dosya bulunamadı.', 'error')
        return redirect(url_for('material.list_materials', training_id=material.training_id))
    
    # İndirme sayısını artır
    material.increment_download_count()
    
    return send_file(
        material.file_path,
        as_attachment=True,
        download_name=material.file_name,
        mimetype=material.mime_type
    )

@material_bp.route('/<int:material_id>/view')
@login_required
def view_material(material_id):
    """Materyal görüntüleme (PDF, resim vb.)"""
    material = TrainingMaterial.query.get_or_404(material_id)
    
    if not can_view_material(material.training):
        flash('Bu materyali görüntüleme yetkiniz yok.', 'error')
        return redirect(url_for('training.detail', training_id=material.training_id))
    
    if not material.is_viewable_online:
        flash('Bu dosya online görüntülenemiyor.', 'error')
        return redirect(url_for('material.list_materials', training_id=material.training_id))
    
    if not os.path.exists(material.file_path):
        flash('Dosya bulunamadı.', 'error')
        return redirect(url_for('material.list_materials', training_id=material.training_id))
    
    # Görüntülenme sayısını artır
    material.increment_view_count()
    
    return send_file(
        material.file_path,
        mimetype=material.mime_type
    )

@material_bp.route('/<int:material_id>/delete', methods=['POST'])
@login_required
def delete_material(material_id):
    """Materyal silme"""
    material = TrainingMaterial.query.get_or_404(material_id)
    
    if not can_upload_material(material.training):
        return jsonify({'success': False, 'message': 'Bu materyali silme yetkiniz yok'}), 403
    
    try:
        # Dosyayı sil
        if os.path.exists(material.file_path):
            os.remove(material.file_path)
        
        # Veritabanından sil
        db.session.delete(material)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': f'"{material.file_name}" başarıyla silindi.'
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': 'Materyal silinirken bir hata oluştu.'
        }), 500

@material_bp.route('/<int:material_id>/toggle-status', methods=['POST'])
@login_required
def toggle_material_status(material_id):
    """Materyal durumunu aktif/pasif yap"""
    material = TrainingMaterial.query.get_or_404(material_id)
    
    if not can_upload_material(material.training):
        return jsonify({'success': False, 'message': 'Bu materyali düzenleme yetkiniz yok'}), 403
    
    try:
        material.is_active = not material.is_active
        db.session.commit()
        
        status = 'aktif' if material.is_active else 'pasif'
        return jsonify({
            'success': True,
            'message': f'Materyal {status} duruma getirildi.',
            'new_status': material.is_active
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': 'Durum değiştirilirken bir hata oluştu.'
        }), 500

@material_bp.route('/api/training/<int:training_id>/materials')
@login_required
def api_materials(training_id):
    """API: Eğitim materyalleri (AJAX için)"""
    training = Training.query.get_or_404(training_id)
    
    if not can_view_material(training):
        return jsonify({'success': False, 'message': 'Yetkiniz yok'}), 403
    
    materials = training.materials.filter_by(is_active=True).order_by(TrainingMaterial.upload_date.desc()).all()
    
    return jsonify({
        'success': True,
        'materials': [material.to_dict() for material in materials],
        'can_upload': can_upload_material(training)
    })
