{% extends "base.html" %}

{% block title %}{{ company.tam_ad }} - Şirket Detayı{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-building"></i> {{ company.tam_ad }}
        <span class="badge {{ 'bg-success' if company.aktif_mi else 'bg-secondary' }} ms-2">
            {{ 'Aktif' if company.aktif_mi else 'Pasif' }}
        </span>
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('company.edit_company', company_id=company.id) }}" class="btn btn-primary">
                <i class="fas fa-edit"></i> Düzenle
            </a>
            <a href="{{ url_for('company.companies') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> Şirket Listesi
            </a>
        </div>
    </div>
</div>

<!-- İstatistik Kartları -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">{{ company.kullanici_sayisi }}</h4>
                        <p class="mb-0">Toplam Kullanıcı</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-users fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">{{ company.manager_sayisi }}</h4>
                        <p class="mb-0">Manager</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-user-tie fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">{{ company.participant_sayisi }}</h4>
                        <p class="mb-0">Katılımcı</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-user fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">{{ company.egitim_sayisi }}</h4>
                        <p class="mb-0">Eğitim</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-book fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Şirket Bilgileri -->
    <div class="col-lg-4">
        <div class="card shadow">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle"></i> Şirket Bilgileri
                </h5>
            </div>
            <div class="card-body">
                <table class="table table-sm">
                    <tr>
                        <td><strong>Kod:</strong></td>
                        <td><span class="badge bg-primary">{{ company.kod }}</span></td>
                    </tr>
                    <tr>
                        <td><strong>Ad:</strong></td>
                        <td>{{ company.ad }}</td>
                    </tr>
                    {% if company.aciklama %}
                    <tr>
                        <td><strong>Açıklama:</strong></td>
                        <td>{{ company.aciklama }}</td>
                    </tr>
                    {% endif %}
                    {% if company.adres %}
                    <tr>
                        <td><strong>Adres:</strong></td>
                        <td>{{ company.adres }}</td>
                    </tr>
                    {% endif %}
                    {% if company.telefon %}
                    <tr>
                        <td><strong>Telefon:</strong></td>
                        <td><i class="fas fa-phone"></i> {{ company.telefon }}</td>
                    </tr>
                    {% endif %}
                    {% if company.email %}
                    <tr>
                        <td><strong>E-posta:</strong></td>
                        <td><i class="fas fa-envelope"></i> {{ company.email }}</td>
                    </tr>
                    {% endif %}
                    {% if company.website %}
                    <tr>
                        <td><strong>Website:</strong></td>
                        <td><i class="fas fa-globe"></i> <a href="{{ company.website }}" target="_blank">{{ company.website }}</a></td>
                    </tr>
                    {% endif %}
                    {% if company.vergi_no %}
                    <tr>
                        <td><strong>Vergi No:</strong></td>
                        <td>{{ company.vergi_no }}</td>
                    </tr>
                    {% endif %}
                    {% if company.vergi_dairesi %}
                    <tr>
                        <td><strong>Vergi Dairesi:</strong></td>
                        <td>{{ company.vergi_dairesi }}</td>
                    </tr>
                    {% endif %}
                    <tr>
                        <td><strong>Oluşturulma:</strong></td>
                        <td>{{ company.olusturulma_tarihi.strftime('%d.%m.%Y %H:%M') if company.olusturulma_tarihi else 'Bilinmiyor' }}</td>
                    </tr>
                    <tr>
                        <td><strong>Oluşturan:</strong></td>
                        <td>{{ company.olusturan.tam_ad if company.olusturan else 'Bilinmiyor' }}</td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
    
    <!-- Kullanıcılar -->
    <div class="col-lg-8">
        <div class="card shadow">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-users"></i> Kullanıcılar 
                    <span class="badge bg-primary">{{ users|length }}</span>
                </h5>
            </div>
            <div class="card-body">
                {% if users %}
                <div class="table-responsive">
                    <table class="table table-hover table-sm">
                        <thead class="table-light">
                            <tr>
                                <th>Ad Soyad</th>
                                <th>E-posta</th>
                                <th>Rol</th>
                                <th>Yönetici</th>
                                <th>Durum</th>
                                <th>İşlemler</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for user in users %}
                            <tr>
                                <td>
                                    <strong>{{ user.tam_ad }}</strong>
                                    {% if user.telefon_numarasi %}
                                    <br><small class="text-muted">{{ user.telefon_numarasi }}</small>
                                    {% endif %}
                                </td>
                                <td>{{ user.email }}</td>
                                <td>
                                    <span class="badge {{ 'bg-danger' if user.rol == 'admin' else 'bg-info' if user.rol == 'manager' else 'bg-secondary' }}">
                                        {{ 'Admin' if user.rol == 'admin' else 'Manager' if user.rol == 'manager' else 'Katılımcı' }}
                                    </span>
                                </td>
                                <td>
                                    {% if user.manager %}
                                    {{ user.manager.tam_ad }}
                                    {% else %}
                                    <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="badge {{ 'bg-success' if user.aktif_mi else 'bg-secondary' }}">
                                        {{ 'Aktif' if user.aktif_mi else 'Pasif' }}
                                    </span>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="{{ url_for('auth.edit_user', user_id=user.id) }}" 
                                           class="btn btn-outline-primary" title="Düzenle">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-users fa-3x text-muted mb-3"></i>
                    <h6 class="text-muted">Bu şirkette henüz kullanıcı bulunmuyor</h6>
                    <a href="{{ url_for('auth.register') }}" class="btn btn-primary btn-sm">
                        <i class="fas fa-plus"></i> Kullanıcı Ekle
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
        
        <!-- Eğitimler -->
        <div class="card shadow mt-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-book"></i> Son Eğitimler 
                    <span class="badge bg-success">{{ trainings|length }}</span>
                </h5>
            </div>
            <div class="card-body">
                {% if trainings %}
                <div class="table-responsive">
                    <table class="table table-hover table-sm">
                        <thead class="table-light">
                            <tr>
                                <th>Eğitim Adı</th>
                                <th>Tarih</th>
                                <th>Oluşturan</th>
                                <th>Katılımcı</th>
                                <th>Durum</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for training in trainings %}
                            <tr>
                                <td>
                                    <strong>{{ training.ad }}</strong>
                                    {% if training.aciklama %}
                                    <br><small class="text-muted">{{ training.aciklama[:50] }}{% if training.aciklama|length > 50 %}...{% endif %}</small>
                                    {% endif %}
                                </td>
                                <td>
                                    <small>
                                        {{ training.baslangic_tarihi.strftime('%d.%m.%Y') }} - 
                                        {{ training.bitis_tarihi.strftime('%d.%m.%Y') }}
                                    </small>
                                </td>
                                <td>{{ training.olusturan.tam_ad }}</td>
                                <td>
                                    <span class="badge bg-info">{{ training.katilimci_sayisi }}</span>
                                </td>
                                <td>
                                    <span class="badge {{ 'bg-success' if training.aktif_mi else 'bg-secondary' }}">
                                        {{ 'Aktif' if training.aktif_mi else 'Pasif' }}
                                    </span>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-book fa-3x text-muted mb-3"></i>
                    <h6 class="text-muted">Bu şirkette henüz eğitim bulunmuyor</h6>
                    <a href="{{ url_for('training.create_training') }}" class="btn btn-success btn-sm">
                        <i class="fas fa-plus"></i> Eğitim Ekle
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
