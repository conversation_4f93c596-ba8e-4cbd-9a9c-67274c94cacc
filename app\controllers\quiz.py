import json
from datetime import datetime
from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from app import db
from app.models.training import Training
from app.models.quiz import Quiz, QuizAttempt

quiz_bp = Blueprint('quiz', __name__)

@quiz_bp.route('/create/<int:training_id>', methods=['GET', 'POST'])
@login_required
def create_quiz(training_id):
    """Yeni quiz oluşturma"""
    training = Training.query.get_or_404(training_id)
    
    if not current_user.can_create_quizzes():
        flash('Bu işlem için yetkiniz yok.', 'error')
        return redirect(url_for('training.detail', training_id=training_id))
    
    if request.method == 'POST':
        ad = request.form.get('ad', '').strip()
        aciklama = request.form.get('aciklama', '').strip()
        gecer_notu = request.form.get('gecer_notu', type=float)
        questions_json = request.form.get('questions_json', '')
        
        # Validasyon
        if not ad:
            flash('Quiz adı zorunludur.', 'error')
            return render_template('quiz_form.html', training=training)
        
        if gecer_notu is None or gecer_notu < 0 or gecer_notu > 100:
            flash('Geçer not 0-100 arasında olmalıdır.', 'error')
            return render_template('quiz_form.html', training=training)
        
        if not questions_json:
            flash('En az bir soru eklemelisiniz.', 'error')
            return render_template('quiz_form.html', training=training)
        
        # JSON formatını kontrol et
        try:
            questions = json.loads(questions_json)
            if not isinstance(questions, list) or len(questions) == 0:
                raise ValueError("Sorular listesi boş olamaz")
            
            # Her sorunun gerekli alanlarını kontrol et
            for i, question in enumerate(questions):
                if not isinstance(question, dict):
                    raise ValueError(f"Soru {i+1} geçersiz format")
                if 'question' not in question or not question['question'].strip():
                    raise ValueError(f"Soru {i+1} metni boş olamaz")
                if 'options' not in question or not isinstance(question['options'], list):
                    raise ValueError(f"Soru {i+1} seçenekleri geçersiz")
                if len(question['options']) < 2:
                    raise ValueError(f"Soru {i+1} en az 2 seçenek içermelidir")
                if 'correct_answer' not in question or question['correct_answer'] not in question['options']:
                    raise ValueError(f"Soru {i+1} doğru cevabı geçersiz")
                    
        except (json.JSONDecodeError, ValueError) as e:
            flash(f'Quiz soruları geçersiz: {str(e)}', 'error')
            return render_template('quiz_form.html', training=training)
        
        # Yeni quiz oluştur
        quiz = Quiz(
            training_id=training_id,
            ad=ad,
            aciklama=aciklama,
            questions_json=questions_json,
            gecer_notu=gecer_notu,
            olusturan_id=current_user.id
        )
        
        try:
            db.session.add(quiz)
            db.session.commit()
            flash(f'"{quiz.ad}" quiz\'i başarıyla oluşturuldu.', 'success')
            return redirect(url_for('training.detail', training_id=training_id))
        except Exception as e:
            db.session.rollback()
            flash('Quiz oluşturulurken bir hata oluştu.', 'error')
    
    return render_template('quiz_form.html', training=training)

@quiz_bp.route('/edit/<int:quiz_id>', methods=['GET', 'POST'])
@login_required
def edit_quiz(quiz_id):
    """Quiz düzenleme"""
    quiz = Quiz.query.get_or_404(quiz_id)
    training = quiz.training
    
    if not current_user.can_create_quizzes():
        flash('Bu işlem için yetkiniz yok.', 'error')
        return redirect(url_for('training.detail', training_id=training.id))
    
    if request.method == 'POST':
        ad = request.form.get('ad', '').strip()
        aciklama = request.form.get('aciklama', '').strip()
        gecer_notu = request.form.get('gecer_notu', type=float)
        questions_json = request.form.get('questions_json', '')
        
        # Validasyon (create ile aynı)
        if not ad:
            flash('Quiz adı zorunludur.', 'error')
            return render_template('quiz_form.html', training=training, quiz=quiz)
        
        if gecer_notu is None or gecer_notu < 0 or gecer_notu > 100:
            flash('Geçer not 0-100 arasında olmalıdır.', 'error')
            return render_template('quiz_form.html', training=training, quiz=quiz)
        
        if not questions_json:
            flash('En az bir soru eklemelisiniz.', 'error')
            return render_template('quiz_form.html', training=training, quiz=quiz)
        
        # JSON formatını kontrol et
        try:
            questions = json.loads(questions_json)
            if not isinstance(questions, list) or len(questions) == 0:
                raise ValueError("Sorular listesi boş olamaz")
                
        except (json.JSONDecodeError, ValueError) as e:
            flash(f'Quiz soruları geçersiz: {str(e)}', 'error')
            return render_template('quiz_form.html', training=training, quiz=quiz)
        
        # Quiz'i güncelle
        quiz.ad = ad
        quiz.aciklama = aciklama
        quiz.questions_json = questions_json
        quiz.gecer_notu = gecer_notu
        
        try:
            db.session.commit()
            flash(f'"{quiz.ad}" quiz\'i başarıyla güncellendi.', 'success')
            return redirect(url_for('training.detail', training_id=training.id))
        except Exception as e:
            db.session.rollback()
            flash('Quiz güncellenirken bir hata oluştu.', 'error')
    
    return render_template('quiz_form.html', training=training, quiz=quiz)

@quiz_bp.route('/<int:quiz_id>/take')
@login_required
def take_quiz(quiz_id):
    """Quiz'i çözme sayfası"""
    quiz = Quiz.query.get_or_404(quiz_id)
    training = quiz.training
    
    # Katılımcı kontrolü
    if not training.is_participant(current_user):
        flash('Bu quiz\'e erişim yetkiniz yok.', 'error')
        return redirect(url_for('training.list_trainings'))
    
    # Daha önce deneme yapılmış mı kontrol et
    existing_attempt = quiz.get_user_attempt(current_user.id)
    if existing_attempt:
        flash('Bu quiz\'i daha önce çözdünüz.', 'info')
        return redirect(url_for('quiz.result', attempt_id=existing_attempt.id))
    
    # Quiz aktif mi kontrol et
    if not quiz.aktif_mi:
        flash('Bu quiz şu anda aktif değil.', 'error')
        return redirect(url_for('training.detail', training_id=training.id))
    
    return render_template('participant_quiz.html', quiz=quiz, training=training)

@quiz_bp.route('/<int:quiz_id>/submit', methods=['POST'])
@login_required
def submit_quiz(quiz_id):
    """Quiz cevaplarını gönderme"""
    quiz = Quiz.query.get_or_404(quiz_id)
    training = quiz.training
    
    # Katılımcı kontrolü
    if not training.is_participant(current_user):
        return jsonify({'success': False, 'message': 'Yetkiniz yok'}), 403
    
    # Daha önce deneme yapılmış mı kontrol et
    if quiz.has_user_attempted(current_user.id):
        return jsonify({'success': False, 'message': 'Bu quiz\'i daha önce çözdünüz'}), 400
    
    # Cevapları al
    answers = {}
    for key, value in request.form.items():
        if key.startswith('question_'):
            question_index = key.replace('question_', '')
            answers[question_index] = value
    
    if not answers:
        return jsonify({'success': False, 'message': 'Hiç cevap verilmedi'}), 400
    
    # Skoru hesapla
    score = quiz.calculate_score(answers)
    is_passing = quiz.is_passing_score(score)
    
    # Deneme kaydını oluştur
    attempt = QuizAttempt(
        user_id=current_user.id,
        quiz_id=quiz_id,
        skor=score,
        gecme_durumu=is_passing,
        cevaplar=answers
    )
    
    try:
        db.session.add(attempt)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'Quiz başarıyla tamamlandı',
            'score': score,
            'passing': is_passing,
            'redirect_url': url_for('quiz.result', attempt_id=attempt.id)
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': 'Quiz kaydedilirken hata oluştu'}), 500

@quiz_bp.route('/result/<int:attempt_id>')
@login_required
def result(attempt_id):
    """Quiz sonucu"""
    attempt = QuizAttempt.query.get_or_404(attempt_id)
    
    # Sadece kendi sonucunu görebilir veya yönetici
    if attempt.user_id != current_user.id and not current_user.can_manage_trainings():
        flash('Bu sonuca erişim yetkiniz yok.', 'error')
        return redirect(url_for('training.list_trainings'))
    
    quiz = attempt.quiz
    training = quiz.training
    
    return render_template('quiz_result.html', 
                         attempt=attempt, 
                         quiz=quiz, 
                         training=training)

@quiz_bp.route('/<int:quiz_id>/results')
@login_required
def quiz_results(quiz_id):
    """Quiz sonuçları listesi (yöneticiler için)"""
    quiz = Quiz.query.get_or_404(quiz_id)
    
    if not current_user.can_manage_trainings():
        flash('Bu sayfaya erişim yetkiniz yok.', 'error')
        return redirect(url_for('training.detail', training_id=quiz.training_id))
    
    attempts = quiz.attempts.order_by(QuizAttempt.deneme_tarihi.desc()).all()
    
    return render_template('quiz_results.html', quiz=quiz, attempts=attempts)

@quiz_bp.route('/<int:quiz_id>/toggle-status', methods=['POST'])
@login_required
def toggle_quiz_status(quiz_id):
    """Quiz durumunu aktif/pasif yap"""
    quiz = Quiz.query.get_or_404(quiz_id)
    
    if not current_user.can_create_quizzes():
        return jsonify({'success': False, 'message': 'Yetkiniz yok'}), 403
    
    quiz.aktif_mi = not quiz.aktif_mi
    
    try:
        db.session.commit()
        status = 'aktif' if quiz.aktif_mi else 'pasif'
        return jsonify({
            'success': True,
            'message': f'Quiz {status} duruma getirildi',
            'status': quiz.aktif_mi
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': 'Durum değiştirilirken hata oluştu'}), 500
