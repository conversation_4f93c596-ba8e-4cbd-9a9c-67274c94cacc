from datetime import datetime
from app import db

class Training(db.Model):
    """Eğitim modeli"""
    __tablename__ = 'trainings'
    
    id = db.Column(db.Integer, primary_key=True)
    ad = db.Column(db.String(200), nullable=False)
    aciklama = db.Column(db.Text)
    baslangic_tarihi = db.Column(db.DateTime, nullable=False)
    bitis_tarihi = db.Column(db.DateTime, nullable=False)
    olusturulma_tarihi = db.Column(db.DateTime, default=datetime.utcnow)
    aktif_mi = db.Column(db.<PERSON>, default=True, nullable=False)
    
    # Eğitimi oluşturan kullanıcı
    olusturan_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    olusturan = db.relationship('User', backref='olusturulan_egitimler')
    
    # İlişkiler
    enrollments = db.relationship('Enrollment', backref='training', lazy='dynamic', cascade='all, delete-orphan')
    quizzes = db.relationship('Quiz', backref='training', lazy='dynamic', cascade='all, delete-orphan')
    surveys = db.relationship('Survey', backref='training', lazy='dynamic', cascade='all, delete-orphan')
    
    def __init__(self, **kwargs):
        super(Training, self).__init__(**kwargs)
    
    @property
    def durum(self):
        """Eğitimin durumunu döndür"""
        now = datetime.utcnow()
        if now < self.baslangic_tarihi:
            return 'planlanan'
        elif now <= self.bitis_tarihi:
            return 'devam_eden'
        else:
            return 'tamamlanan'
    
    @property
    def katilimci_sayisi(self):
        """Toplam katılımcı sayısı"""
        return self.enrollments.filter_by(aktif_mi=True).count()
    
    @property
    def teyit_eden_sayisi(self):
        """Katılımını teyit eden sayısı"""
        return self.enrollments.filter_by(katilim_teyidi_durumu='teyit_edildi').count()
    
    @property
    def teyit_orani(self):
        """Katılım teyit oranı"""
        if self.katilimci_sayisi == 0:
            return 0
        return (self.teyit_eden_sayisi / self.katilimci_sayisi) * 100
    
    def get_participants(self):
        """Katılımcıları getir"""
        return [enrollment.user for enrollment in self.enrollments.filter_by(aktif_mi=True).all()]
    
    def get_confirmed_participants(self):
        """Teyit eden katılımcıları getir"""
        return [enrollment.user for enrollment in 
                self.enrollments.filter_by(katilim_teyidi_durumu='teyit_edildi').all()]
    
    def is_participant(self, user):
        """Kullanıcı bu eğitimin katılımcısı mı"""
        return self.enrollments.filter_by(user_id=user.id, aktif_mi=True).first() is not None
    
    def get_active_quizzes(self):
        """Aktif quizleri getir"""
        return self.quizzes.filter_by(aktif_mi=True).all()
    
    def __repr__(self):
        return f'<Training {self.ad}>'
    
    def to_dict(self):
        """Eğitim bilgilerini dictionary olarak döndür"""
        return {
            'id': self.id,
            'ad': self.ad,
            'aciklama': self.aciklama,
            'baslangic_tarihi': self.baslangic_tarihi.isoformat() if self.baslangic_tarihi else None,
            'bitis_tarihi': self.bitis_tarihi.isoformat() if self.bitis_tarihi else None,
            'olusturulma_tarihi': self.olusturulma_tarihi.isoformat() if self.olusturulma_tarihi else None,
            'durum': self.durum,
            'katilimci_sayisi': self.katilimci_sayisi,
            'teyit_eden_sayisi': self.teyit_eden_sayisi,
            'teyit_orani': self.teyit_orani,
            'aktif_mi': self.aktif_mi
        }


class Enrollment(db.Model):
    """Eğitim kayıt modeli"""
    __tablename__ = 'enrollments'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    training_id = db.Column(db.Integer, db.ForeignKey('trainings.id'), nullable=False)
    katilim_teyidi_durumu = db.Column(db.Enum('beklemede', 'teyit_edildi', 'reddedildi', 
                                             name='enrollment_status'), 
                                     default='beklemede', nullable=False)
    teyit_tarihi = db.Column(db.DateTime)
    teyit_sms_id = db.Column(db.String(100))  # Twilio SMS ID
    kayit_tarihi = db.Column(db.DateTime, default=datetime.utcnow)
    aktif_mi = db.Column(db.Boolean, default=True, nullable=False)
    
    # Benzersiz constraint
    __table_args__ = (db.UniqueConstraint('user_id', 'training_id', name='unique_user_training'),)
    
    def __init__(self, **kwargs):
        super(Enrollment, self).__init__(**kwargs)
    
    def confirm_participation(self):
        """Katılımı teyit et"""
        self.katilim_teyidi_durumu = 'teyit_edildi'
        self.teyit_tarihi = datetime.utcnow()
        db.session.commit()
    
    def reject_participation(self):
        """Katılımı reddet"""
        self.katilim_teyidi_durumu = 'reddedildi'
        self.teyit_tarihi = datetime.utcnow()
        db.session.commit()
    
    @property
    def is_confirmed(self):
        """Teyit edildi mi"""
        return self.katilim_teyidi_durumu == 'teyit_edildi'
    
    @property
    def is_pending(self):
        """Beklemede mi"""
        return self.katilim_teyidi_durumu == 'beklemede'
    
    @property
    def is_rejected(self):
        """Reddedildi mi"""
        return self.katilim_teyidi_durumu == 'reddedildi'
    
    def __repr__(self):
        return f'<Enrollment {self.user.tam_ad} - {self.training.ad}>'
    
    def to_dict(self):
        """Kayıt bilgilerini dictionary olarak döndür"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'training_id': self.training_id,
            'katilim_teyidi_durumu': self.katilim_teyidi_durumu,
            'teyit_tarihi': self.teyit_tarihi.isoformat() if self.teyit_tarihi else None,
            'kayit_tarihi': self.kayit_tarihi.isoformat() if self.kayit_tarihi else None,
            'aktif_mi': self.aktif_mi,
            'user_name': self.user.tam_ad if self.user else None,
            'training_name': self.training.ad if self.training else None
        }
