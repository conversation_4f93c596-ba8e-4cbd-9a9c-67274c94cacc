from datetime import datetime
from app import db

class Training(db.Model):
    """Eğitim modeli"""
    __tablename__ = 'trainings'
    
    id = db.Column(db.Integer, primary_key=True)
    ad = db.Column(db.String(200), nullable=False)
    aciklama = db.Column(db.Text)
    baslangic_tarihi = db.Column(db.DateTime, nullable=False)
    bitis_tarihi = db.Column(db.DateTime, nullable=False)
    olusturulma_tarihi = db.Column(db.DateTime, default=datetime.utcnow)
    aktif_mi = db.Column(db.<PERSON>, default=True, nullable=False)
    
    # Eğitimi oluşturan kullanıcı
    olusturan_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    olusturan = db.relationship('User', backref='olusturulan_egitimler')

    # Şirket ilişkisi
    company_id = db.Column(db.Integer, db.ForeignKey('companies.id'), nullable=True)

    # Eğitmen bilgileri (hibrit yaklaşım)
    egitmen_adi = db.Column(db.String(200))  # Eğitmen adı (zorunlu)
    egitmen_telefon = db.Column(db.String(20))  # SMS için gerekli
    egitmen_email = db.Column(db.String(120))  # İletişim için
    egitmen_user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)  # Sistem kullanıcısıysa

    # Eğitim teslim durumu
    egitim_verildi_mi = db.Column(db.Boolean, default=False)  # Eğitmen onayı
    egitmen_onay_tarihi = db.Column(db.DateTime)  # Onay tarihi
    egitmen_onay_kodu = db.Column(db.String(10))  # SMS onay kodu
    
    # İlişkiler
    enrollments = db.relationship('Enrollment', backref='training', lazy='dynamic', cascade='all, delete-orphan')
    quizzes = db.relationship('Quiz', backref='training', lazy='dynamic', cascade='all, delete-orphan')
    egitmen_user = db.relationship('User', foreign_keys=[egitmen_user_id], backref='egitmen_olarak_egitimler')
    surveys = db.relationship('Survey', backref='training', lazy='dynamic', cascade='all, delete-orphan')
    
    def __init__(self, **kwargs):
        super(Training, self).__init__(**kwargs)
    
    @property
    def durum(self):
        """Eğitimin durumunu döndür"""
        now = datetime.utcnow()
        if now < self.baslangic_tarihi:
            return 'planlanan'
        elif now <= self.bitis_tarihi:
            return 'devam_eden'
        else:
            return 'tamamlanan'
    
    @property
    def katilimci_sayisi(self):
        """Toplam katılımcı sayısı"""
        return self.enrollments.filter_by(aktif_mi=True).count()
    
    @property
    def teyit_eden_sayisi(self):
        """Katılımını teyit eden sayısı"""
        return self.enrollments.filter_by(katilim_teyidi_durumu='teyit_edildi').count()
    
    @property
    def teyit_orani(self):
        """Katılım teyit oranı"""
        if self.katilimci_sayisi == 0:
            return 0
        return (self.teyit_eden_sayisi / self.katilimci_sayisi) * 100
    
    def get_participants(self):
        """Katılımcıları getir"""
        return [enrollment.user for enrollment in self.enrollments.filter_by(aktif_mi=True).all()]
    
    def get_confirmed_participants(self):
        """Teyit eden katılımcıları getir"""
        return [enrollment.user for enrollment in 
                self.enrollments.filter_by(katilim_teyidi_durumu='teyit_edildi').all()]
    
    def is_participant(self, user):
        """Kullanıcı bu eğitimin katılımcısı mı"""
        return self.enrollments.filter_by(user_id=user.id, aktif_mi=True).first() is not None
    
    def get_active_quizzes(self):
        """Aktif quizleri getir"""
        return self.quizzes.filter_by(aktif_mi=True).all()

    @property
    def egitmen_bilgisi(self):
        """Eğitmen bilgilerini döndür"""
        if self.egitmen_user_id and self.egitmen_user:
            # Sistem kullanıcısı eğitmen
            return {
                'ad': self.egitmen_user.tam_ad,
                'telefon': self.egitmen_user.telefon_numarasi,
                'email': self.egitmen_user.email,
                'tip': 'sistem_kullanicisi',
                'user_id': self.egitmen_user_id
            }
        else:
            # Dış eğitmen
            return {
                'ad': self.egitmen_adi,
                'telefon': self.egitmen_telefon,
                'email': self.egitmen_email,
                'tip': 'dis_egitmen',
                'user_id': None
            }

    @property
    def egitmen_iletisim_telefon(self):
        """SMS gönderimi için telefon numarası"""
        if self.egitmen_user_id and self.egitmen_user:
            return self.egitmen_user.telefon_numarasi
        return self.egitmen_telefon

    def generate_onay_kodu(self):
        """SMS onay kodu oluştur"""
        import random
        import string
        self.egitmen_onay_kodu = ''.join(random.choices(string.digits, k=6))
        return self.egitmen_onay_kodu

    def egitmen_onayini_ver(self, onay_kodu=None):
        """Eğitmen onayını ver"""
        from datetime import datetime

        # Kod kontrolü (SMS ile geldiyse)
        if onay_kodu and self.egitmen_onay_kodu != onay_kodu:
            return False, "Geçersiz onay kodu"

        self.egitim_verildi_mi = True
        self.egitmen_onay_tarihi = datetime.utcnow()
        self.egitmen_onay_kodu = None  # Kodu temizle

        return True, "Eğitim onaylandı"

    def egitmen_onayini_iptal_et(self):
        """Eğitmen onayını iptal et"""
        self.egitim_verildi_mi = False
        self.egitmen_onay_tarihi = None
        self.egitmen_onay_kodu = None

    def __repr__(self):
        return f'<Training {self.ad}>'
    
    def to_dict(self):
        """Eğitim bilgilerini dictionary olarak döndür"""
        return {
            'id': self.id,
            'ad': self.ad,
            'aciklama': self.aciklama,
            'baslangic_tarihi': self.baslangic_tarihi.isoformat() if self.baslangic_tarihi else None,
            'bitis_tarihi': self.bitis_tarihi.isoformat() if self.bitis_tarihi else None,
            'olusturulma_tarihi': self.olusturulma_tarihi.isoformat() if self.olusturulma_tarihi else None,
            'durum': self.durum,
            'katilimci_sayisi': self.katilimci_sayisi,
            'teyit_eden_sayisi': self.teyit_eden_sayisi,
            'teyit_orani': self.teyit_orani,
            'aktif_mi': self.aktif_mi,
            'egitmen_bilgisi': self.egitmen_bilgisi,
            'egitim_verildi_mi': self.egitim_verildi_mi,
            'egitmen_onay_tarihi': self.egitmen_onay_tarihi.isoformat() if self.egitmen_onay_tarihi else None
        }


class Enrollment(db.Model):
    """Eğitim kayıt modeli"""
    __tablename__ = 'enrollments'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    training_id = db.Column(db.Integer, db.ForeignKey('trainings.id'), nullable=False)
    katilim_teyidi_durumu = db.Column(db.Enum('beklemede', 'teyit_edildi', 'reddedildi', 
                                             name='enrollment_status'), 
                                     default='beklemede', nullable=False)
    teyit_tarihi = db.Column(db.DateTime)
    teyit_sms_id = db.Column(db.String(100))  # Twilio SMS ID
    kayit_tarihi = db.Column(db.DateTime, default=datetime.utcnow)
    aktif_mi = db.Column(db.Boolean, default=True, nullable=False)
    
    # Benzersiz constraint
    __table_args__ = (db.UniqueConstraint('user_id', 'training_id', name='unique_user_training'),)
    
    def __init__(self, **kwargs):
        super(Enrollment, self).__init__(**kwargs)
    
    def confirm_participation(self):
        """Katılımı teyit et"""
        self.katilim_teyidi_durumu = 'teyit_edildi'
        self.teyit_tarihi = datetime.utcnow()
        db.session.commit()
    
    def reject_participation(self):
        """Katılımı reddet"""
        self.katilim_teyidi_durumu = 'reddedildi'
        self.teyit_tarihi = datetime.utcnow()
        db.session.commit()
    
    @property
    def is_confirmed(self):
        """Teyit edildi mi"""
        return self.katilim_teyidi_durumu == 'teyit_edildi'
    
    @property
    def is_pending(self):
        """Beklemede mi"""
        return self.katilim_teyidi_durumu == 'beklemede'
    
    @property
    def is_rejected(self):
        """Reddedildi mi"""
        return self.katilim_teyidi_durumu == 'reddedildi'
    
    def __repr__(self):
        return f'<Enrollment {self.user.tam_ad} - {self.training.ad}>'
    
    def to_dict(self):
        """Kayıt bilgilerini dictionary olarak döndür"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'training_id': self.training_id,
            'katilim_teyidi_durumu': self.katilim_teyidi_durumu,
            'teyit_tarihi': self.teyit_tarihi.isoformat() if self.teyit_tarihi else None,
            'kayit_tarihi': self.kayit_tarihi.isoformat() if self.kayit_tarihi else None,
            'aktif_mi': self.aktif_mi,
            'user_name': self.user.tam_ad if self.user else None,
            'training_name': self.training.ad if self.training else None
        }
