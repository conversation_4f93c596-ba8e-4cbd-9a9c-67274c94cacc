from twilio.rest import Client
from twilio.base.exceptions import TwilioException
from flask import current_app
import logging

class SMSService:
    """Twilio SMS servisi"""
    
    def __init__(self):
        self.account_sid = current_app.config.get('TWILIO_ACCOUNT_SID')
        self.auth_token = current_app.config.get('TWILIO_AUTH_TOKEN')
        self.phone_number = current_app.config.get('TWILIO_PHONE_NUMBER')
        
        if not all([self.account_sid, self.auth_token, self.phone_number]):
            logging.warning("Twilio konfigürasyonu eksik. SMS servisi çalışmayacak.")
            self.client = None
        else:
            self.client = Client(self.account_sid, self.auth_token)
    
    def send_sms(self, to_number, message):
        """
        SMS gönder
        
        Args:
            to_number (str): Alıcı telefon numarası
            message (str): Gönderilecek mesaj
            
        Returns:
            dict: {'success': bool, 'sid': str, 'error': str}
        """
        if not self.client:
            return {
                'success': False,
                'error': 'Twilio konfigürasyonu eksik'
            }
        
        try:
            # Telefon numarasını formatla
            if not to_number.startswith('+'):
                # Türkiye için +90 ekle
                if to_number.startswith('0'):
                    to_number = '+90' + to_number[1:]
                else:
                    to_number = '+90' + to_number
            
            # SMS gönder
            message_obj = self.client.messages.create(
                body=message,
                from_=self.phone_number,
                to=to_number
            )
            
            logging.info(f"SMS gönderildi: {message_obj.sid} -> {to_number}")
            
            return {
                'success': True,
                'sid': message_obj.sid,
                'status': message_obj.status
            }
            
        except TwilioException as e:
            logging.error(f"Twilio SMS hatası: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
        except Exception as e:
            logging.error(f"SMS gönderme hatası: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def get_message_status(self, message_sid):
        """
        SMS durumunu kontrol et
        
        Args:
            message_sid (str): Twilio message SID
            
        Returns:
            dict: {'success': bool, 'status': str, 'error': str}
        """
        if not self.client:
            return {
                'success': False,
                'error': 'Twilio konfigürasyonu eksik'
            }
        
        try:
            message = self.client.messages(message_sid).fetch()
            
            return {
                'success': True,
                'status': message.status,
                'error_code': message.error_code,
                'error_message': message.error_message
            }
            
        except TwilioException as e:
            logging.error(f"Twilio mesaj durumu hatası: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
        except Exception as e:
            logging.error(f"Mesaj durumu kontrol hatası: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def validate_phone_number(self, phone_number):
        """
        Telefon numarası formatını kontrol et
        
        Args:
            phone_number (str): Kontrol edilecek telefon numarası
            
        Returns:
            dict: {'valid': bool, 'formatted': str, 'error': str}
        """
        try:
            # Boşlukları ve özel karakterleri temizle
            cleaned = ''.join(filter(str.isdigit, phone_number))
            
            if not cleaned:
                return {
                    'valid': False,
                    'error': 'Telefon numarası boş olamaz'
                }
            
            # Türkiye telefon numarası kontrolü
            if len(cleaned) == 10 and cleaned.startswith('5'):
                # 5xxxxxxxxx formatı
                formatted = '+90' + cleaned
            elif len(cleaned) == 11 and cleaned.startswith('05'):
                # 05xxxxxxxxx formatı
                formatted = '+90' + cleaned[1:]
            elif len(cleaned) == 13 and cleaned.startswith('905'):
                # 905xxxxxxxxx formatı
                formatted = '+' + cleaned
            else:
                return {
                    'valid': False,
                    'error': 'Geçersiz telefon numarası formatı'
                }
            
            return {
                'valid': True,
                'formatted': formatted
            }
            
        except Exception as e:
            return {
                'valid': False,
                'error': str(e)
            }
    
    def send_bulk_sms(self, recipients, message):
        """
        Toplu SMS gönder
        
        Args:
            recipients (list): Alıcı telefon numaraları listesi
            message (str): Gönderilecek mesaj
            
        Returns:
            dict: {'success_count': int, 'failed_count': int, 'results': list}
        """
        results = []
        success_count = 0
        failed_count = 0
        
        for phone_number in recipients:
            result = self.send_sms(phone_number, message)
            result['phone_number'] = phone_number
            results.append(result)
            
            if result['success']:
                success_count += 1
            else:
                failed_count += 1
        
        return {
            'success_count': success_count,
            'failed_count': failed_count,
            'results': results
        }
    
    def create_confirmation_message(self, user_name, training_name, confirmation_url):
        """
        Katılım teyidi SMS mesajı oluştur
        
        Args:
            user_name (str): Kullanıcı adı
            training_name (str): Eğitim adı
            confirmation_url (str): Teyit linki
            
        Returns:
            str: SMS mesajı
        """
        message = f"Merhaba {user_name}, {training_name} eğitimine katılımınızı teyit etmek için lütfen bu linke tıklayın: {confirmation_url}"
        
        # SMS karakter limiti kontrolü (160 karakter)
        if len(message) > 160:
            # Kısa versiyon
            message = f"Merhaba {user_name}, {training_name} eğitimi için: {confirmation_url}"
        
        return message
