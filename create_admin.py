#!/usr/bin/env python3
"""Admin kullanıcısı oluşturma scripti"""

from app import create_app, db
from app.models.user import User

def create_admin_user():
    app = create_app('development')
    
    with app.app_context():
        # Mevcut admin var mı kontrol et
        existing_admin = User.query.filter_by(email='<EMAIL>').first()
        if existing_admin:
            print("Admin kullanıcısı zaten mevcut.")
            return
        
        # Yeni admin oluştur
        admin = User(
            ad='Admin',
            soyad='Kullanıcı',
            email='<EMAIL>',
            telefon_numarasi='05551234567',
            rol='admin'
        )
        admin.set_password('123456')
        
        db.session.add(admin)
        db.session.commit()
        
        print(f"Admin kullanıcısı oluşturuldu:")
        print(f"Email: {admin.email}")
        print(f"Şifre: 123456")
        print(f"Rol: {admin.rol}")

if __name__ == '__main__':
    create_admin_user()
