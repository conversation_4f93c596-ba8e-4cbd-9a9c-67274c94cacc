from datetime import datetime
from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from app import db
from app.models.user import User
from app.models.training import Training, Enrollment
from app.models.quiz import Quiz
from app.models.communication import Survey
from app.services.notification_service import TrainingNotificationService

training_bp = Blueprint('training', __name__)

@training_bp.route('/')
@login_required
def list_trainings():
    """Eğitim listesi"""
    page = request.args.get('page', 1, type=int)
    per_page = 10

    # Filtreleme parametreleri
    durum = request.args.get('durum', '')
    arama = request.args.get('arama', '')

    if current_user.can_manage_trainings():
        # Admin/Manager tüm eğitimleri görebilir
        query = Training.query.filter_by(aktif_mi=True)

        # Durum filtreleme
        if durum == 'planlanan':
            from datetime import datetime
            query = query.filter(Training.baslangic_tarihi > datetime.utcnow())
        elif durum == 'devam_eden':
            from datetime import datetime
            query = query.filter(
                Training.baslangic_tarihi <= datetime.utcnow(),
                Training.bitis_tarihi >= datetime.utcnow()
            )
        elif durum == 'tamamlanan':
            from datetime import datetime
            query = query.filter(Training.bitis_tarihi < datetime.utcnow())

        # Arama filtreleme
        if arama:
            query = query.filter(
                db.or_(
                    Training.ad.ilike(f'%{arama}%'),
                    Training.aciklama.ilike(f'%{arama}%'),
                    Training.egitmen_adi.ilike(f'%{arama}%')
                )
            )

        trainings = query.order_by(Training.olusturulma_tarihi.desc()).paginate(
            page=page, per_page=per_page, error_out=False
        )
    else:
        # Katılımcı sadece kayıtlı olduğu eğitimleri görebilir
        training_ids = [e.training_id for e in current_user.enrollments.filter_by(aktif_mi=True).all()]
        query = Training.query.filter(Training.id.in_(training_ids))

        # Durum filtreleme (katılımcı için de)
        if durum == 'planlanan':
            from datetime import datetime
            query = query.filter(Training.baslangic_tarihi > datetime.utcnow())
        elif durum == 'devam_eden':
            from datetime import datetime
            query = query.filter(
                Training.baslangic_tarihi <= datetime.utcnow(),
                Training.bitis_tarihi >= datetime.utcnow()
            )
        elif durum == 'tamamlanan':
            from datetime import datetime
            query = query.filter(Training.bitis_tarihi < datetime.utcnow())

        # Arama filtreleme
        if arama:
            query = query.filter(
                db.or_(
                    Training.ad.ilike(f'%{arama}%'),
                    Training.aciklama.ilike(f'%{arama}%')
                )
            )

        trainings = query.order_by(Training.baslangic_tarihi.desc()).paginate(
            page=page, per_page=per_page, error_out=False
        )

    return render_template('training_list.html',
                         trainings=trainings,
                         current_durum=durum,
                         current_arama=arama)

@training_bp.route('/create', methods=['GET', 'POST'])
@login_required
def create_training():
    """Yeni eğitim oluşturma"""
    if not current_user.can_manage_trainings():
        flash('Bu işlem için yetkiniz yok.', 'error')
        return redirect(url_for('training.list_trainings'))

    # Eğitmenler ve şirketler listesi
    from app.models.user import User
    from app.models.company import Company

    instructors = User.query.filter_by(rol='instructor', aktif_mi=True).order_by(User.ad, User.soyad).all()
    companies = Company.get_active_companies()

    if request.method == 'POST':
        ad = request.form.get('ad', '').strip()
        aciklama = request.form.get('aciklama', '').strip()
        baslangic_tarihi_str = request.form.get('baslangic_tarihi', '')
        bitis_tarihi_str = request.form.get('bitis_tarihi', '')
        company_id = request.form.get('company_id', '') or None

        # Eğitmen bilgileri
        egitmen_tipi = request.form.get('egitmen_tipi', 'dis')  # 'sistem' veya 'dis'
        egitmen_user_id = request.form.get('egitmen_user_id', '') or None
        egitmen_adi = request.form.get('egitmen_adi', '').strip()
        egitmen_telefon = request.form.get('egitmen_telefon', '').strip()
        egitmen_email = request.form.get('egitmen_email', '').strip()
        
        # Eğitmen validasyonu
        if egitmen_tipi == 'sistem':
            if not egitmen_user_id:
                flash('Sistem eğitmeni seçilmelidir.', 'error')
                return render_template('training_form.html', instructors=instructors, companies=companies)
            # Sistem eğitmeninden bilgileri al
            instructor_user = User.query.get(egitmen_user_id)
            if not instructor_user or instructor_user.rol != 'instructor':
                flash('Geçersiz eğitmen seçimi.', 'error')
                return render_template('training_form.html', instructors=instructors, companies=companies)
            egitmen_adi = instructor_user.tam_ad
            egitmen_telefon = instructor_user.telefon_numarasi
            egitmen_email = instructor_user.email
        else:
            # Dış eğitmen için bilgiler zorunlu
            if not all([egitmen_adi, egitmen_telefon]):
                flash('Eğitmen adı ve telefon numarası zorunludur.', 'error')
                return render_template('training_form.html', instructors=instructors, companies=companies)
            egitmen_user_id = None

        # Validasyon
        if not all([ad, baslangic_tarihi_str, bitis_tarihi_str]):
            flash('Eğitim adı, başlangıç ve bitiş tarihleri zorunludur.', 'error')
            return render_template('training_form.html', instructors=instructors, companies=companies)
        
        try:
            baslangic_tarihi = datetime.strptime(baslangic_tarihi_str, '%Y-%m-%dT%H:%M')
            bitis_tarihi = datetime.strptime(bitis_tarihi_str, '%Y-%m-%dT%H:%M')
        except ValueError:
            flash('Geçersiz tarih formatı.', 'error')
            return render_template('training_form.html')
        
        if bitis_tarihi <= baslangic_tarihi:
            flash('Bitiş tarihi başlangıç tarihinden sonra olmalıdır.', 'error')
            return render_template('training_form.html')
        
        # Yeni eğitim oluştur
        training = Training(
            ad=ad,
            aciklama=aciklama,
            baslangic_tarihi=baslangic_tarihi,
            bitis_tarihi=bitis_tarihi,
            olusturan_id=current_user.id,
            company_id=int(company_id) if company_id else None,
            egitmen_adi=egitmen_adi,
            egitmen_telefon=egitmen_telefon,
            egitmen_email=egitmen_email,
            egitmen_user_id=int(egitmen_user_id) if egitmen_user_id else None
        )
        
        try:
            db.session.add(training)
            db.session.commit()
            flash(f'"{training.ad}" eğitimi başarıyla oluşturuldu.', 'success')
            return redirect(url_for('training.detail', training_id=training.id))
        except Exception as e:
            db.session.rollback()
            flash('Eğitim oluşturulurken bir hata oluştu.', 'error')
    
    return render_template('training_form.html', instructors=instructors, companies=companies)

@training_bp.route('/<int:training_id>')
@login_required
def detail(training_id):
    """Eğitim detayları"""
    training = Training.query.get_or_404(training_id)
    
    # Yetki kontrolü
    if not current_user.can_manage_trainings() and not training.is_participant(current_user):
        flash('Bu eğitimi görüntüleme yetkiniz yok.', 'error')
        return redirect(url_for('training.list_trainings'))
    
    # Katılımcıları getir
    enrollments = training.enrollments.filter_by(aktif_mi=True).all()
    
    # Quizleri getir
    quizzes = training.get_active_quizzes()
    
    # Anketleri getir
    surveys = training.surveys.filter_by(aktif_mi=True).all()
    
    return render_template('training_detail.html', 
                         training=training, 
                         enrollments=enrollments,
                         quizzes=quizzes,
                         surveys=surveys)

@training_bp.route('/<int:training_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_training(training_id):
    """Eğitim düzenleme"""
    training = Training.query.get_or_404(training_id)
    
    if not current_user.can_manage_trainings():
        flash('Bu işlem için yetkiniz yok.', 'error')
        return redirect(url_for('training.detail', training_id=training_id))
    
    if request.method == 'POST':
        ad = request.form.get('ad', '').strip()
        aciklama = request.form.get('aciklama', '').strip()
        baslangic_tarihi_str = request.form.get('baslangic_tarihi', '')
        bitis_tarihi_str = request.form.get('bitis_tarihi', '')
        
        # Validasyon
        if not all([ad, baslangic_tarihi_str, bitis_tarihi_str]):
            flash('Eğitim adı, başlangıç ve bitiş tarihleri zorunludur.', 'error')
            return render_template('training_form.html', training=training)
        
        try:
            baslangic_tarihi = datetime.strptime(baslangic_tarihi_str, '%Y-%m-%dT%H:%M')
            bitis_tarihi = datetime.strptime(bitis_tarihi_str, '%Y-%m-%dT%H:%M')
        except ValueError:
            flash('Geçersiz tarih formatı.', 'error')
            return render_template('training_form.html', training=training)
        
        if bitis_tarihi <= baslangic_tarihi:
            flash('Bitiş tarihi başlangıç tarihinden sonra olmalıdır.', 'error')
            return render_template('training_form.html', training=training)
        
        # Eğitimi güncelle
        training.ad = ad
        training.aciklama = aciklama
        training.baslangic_tarihi = baslangic_tarihi
        training.bitis_tarihi = bitis_tarihi
        
        try:
            db.session.commit()
            flash(f'"{training.ad}" eğitimi başarıyla güncellendi.', 'success')
            return redirect(url_for('training.detail', training_id=training.id))
        except Exception as e:
            db.session.rollback()
            flash('Eğitim güncellenirken bir hata oluştu.', 'error')
    
    return render_template('training_form.html', training=training)

@training_bp.route('/<int:training_id>/participants')
@login_required
def manage_participants(training_id):
    """Katılımcı yönetimi"""
    training = Training.query.get_or_404(training_id)
    
    if not current_user.can_manage_trainings():
        flash('Bu işlem için yetkiniz yok.', 'error')
        return redirect(url_for('training.detail', training_id=training_id))
    
    # Mevcut katılımcılar
    current_participants = training.get_participants()
    current_participant_ids = [p.id for p in current_participants]
    
    # Potansiyel katılımcılar (henüz bu eğitime kayıtlı olmayanlar)
    available_users = User.query.filter(
        ~User.id.in_(current_participant_ids),
        User.aktif_mi == True
    ).order_by(User.ad, User.soyad).all()
    
    return render_template('manage_participants.html', 
                         training=training,
                         current_participants=current_participants,
                         available_users=available_users)

@training_bp.route('/<int:training_id>/add-participant', methods=['POST'])
@login_required
def add_participant(training_id):
    """Katılımcı ekleme"""
    training = Training.query.get_or_404(training_id)
    
    if not current_user.can_manage_trainings():
        return jsonify({'success': False, 'message': 'Yetkiniz yok'}), 403
    
    user_id = request.form.get('user_id', type=int)
    if not user_id:
        return jsonify({'success': False, 'message': 'Kullanıcı seçilmedi'}), 400
    
    user = User.query.get_or_404(user_id)
    
    # Zaten kayıtlı mı kontrol et
    existing_enrollment = Enrollment.query.filter_by(
        user_id=user_id, 
        training_id=training_id
    ).first()
    
    if existing_enrollment:
        if existing_enrollment.aktif_mi:
            return jsonify({'success': False, 'message': 'Kullanıcı zaten kayıtlı'}), 400
        else:
            # Pasif kaydı aktif yap
            existing_enrollment.aktif_mi = True
            existing_enrollment.katilim_teyidi_durumu = 'beklemede'
            existing_enrollment.kayit_tarihi = datetime.utcnow()
    else:
        # Yeni kayıt oluştur
        enrollment = Enrollment(
            user_id=user_id,
            training_id=training_id
        )
        db.session.add(enrollment)
    
    try:
        db.session.commit()
        return jsonify({
            'success': True, 
            'message': f'{user.tam_ad} eğitime başarıyla eklendi'
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': 'Kayıt eklenirken hata oluştu'}), 500

@training_bp.route('/<int:training_id>/remove-participant/<int:user_id>', methods=['POST'])
@login_required
def remove_participant(training_id, user_id):
    """Katılımcı çıkarma"""
    training = Training.query.get_or_404(training_id)
    
    if not current_user.can_manage_trainings():
        return jsonify({'success': False, 'message': 'Yetkiniz yok'}), 403
    
    enrollment = Enrollment.query.filter_by(
        user_id=user_id, 
        training_id=training_id,
        aktif_mi=True
    ).first()
    
    if not enrollment:
        return jsonify({'success': False, 'message': 'Kayıt bulunamadı'}), 404
    
    user = enrollment.user
    enrollment.aktif_mi = False
    
    try:
        db.session.commit()
        return jsonify({
            'success': True, 
            'message': f'{user.tam_ad} eğitimden çıkarıldı'
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': 'Kayıt çıkarılırken hata oluştu'}), 500

@training_bp.route('/<int:training_id>/send-participation-sms', methods=['POST'])
@login_required
def send_participation_sms(training_id):
    """Katılım teyidi SMS'i gönder"""
    training = Training.query.get_or_404(training_id)

    if not current_user.can_manage_trainings():
        return jsonify({'success': False, 'message': 'Yetkiniz yok'}), 403

    # Katılımcıları al
    participants = training.get_participants()

    if not participants:
        return jsonify({'success': False, 'message': 'Eğitimde katılımcı bulunamadı'}), 400

    # SMS gönder
    notification_service = TrainingNotificationService()
    results = notification_service.send_participation_confirmation_sms(training, participants)

    if results['success'] > 0:
        message = f"{results['success']} katılımcıya katılım teyidi SMS'i gönderildi."
        if results['failed'] > 0:
            message += f" {results['failed']} SMS gönderilemedi."

        return jsonify({
            'success': True,
            'message': message,
            'details': results
        })
    else:
        return jsonify({
            'success': False,
            'message': 'Hiçbir SMS gönderilemedi',
            'details': results
        }), 500

@training_bp.route('/<int:training_id>/send-quiz-sms/<int:quiz_id>', methods=['POST'])
@login_required
def send_quiz_sms(training_id, quiz_id):
    """Quiz linki SMS'i gönder"""
    training = Training.query.get_or_404(training_id)
    quiz = Quiz.query.get_or_404(quiz_id)

    if not current_user.can_manage_trainings():
        return jsonify({'success': False, 'message': 'Yetkiniz yok'}), 403

    if quiz.training_id != training_id:
        return jsonify({'success': False, 'message': 'Quiz bu eğitime ait değil'}), 400

    # Katılımcıları al
    participants = training.get_participants()

    if not participants:
        return jsonify({'success': False, 'message': 'Eğitimde katılımcı bulunamadı'}), 400

    # SMS gönder
    notification_service = TrainingNotificationService()
    results = notification_service.send_quiz_link_sms(training, quiz, participants)

    if results['success'] > 0:
        message = f"{results['success']} katılımcıya quiz linki SMS'i gönderildi."
        if results['failed'] > 0:
            message += f" {results['failed']} SMS gönderilemedi."

        return jsonify({
            'success': True,
            'message': message,
            'details': results
        })
    else:
        return jsonify({
            'success': False,
            'message': 'Hiçbir SMS gönderilemedi',
            'details': results
        }), 500

@training_bp.route('/<int:training_id>/send-evaluation-email', methods=['POST'])
@login_required
def send_evaluation_email(training_id):
    """Değerlendirme anketi email'i gönder"""
    training = Training.query.get_or_404(training_id)

    if not current_user.can_manage_trainings():
        return jsonify({'success': False, 'message': 'Yetkiniz yok'}), 403

    # Katılımcıların yöneticilerini bul
    participants = training.get_participants()
    manager_ids = set()

    for participant in participants:
        if participant.manager_id:
            manager_ids.add(participant.manager_id)

    if not manager_ids:
        return jsonify({'success': False, 'message': 'Katılımcıların yöneticisi bulunamadı'}), 400

    managers = User.query.filter(User.id.in_(manager_ids)).all()

    # Email gönder
    notification_service = TrainingNotificationService()
    results = notification_service.send_evaluation_survey_email(training, managers)

    if results['success'] > 0:
        message = f"{results['success']} yöneticiye değerlendirme anketi email'i gönderildi."
        if results['failed'] > 0:
            message += f" {results['failed']} email gönderilemedi."

        return jsonify({
            'success': True,
            'message': message,
            'details': results
        })
    else:
        return jsonify({
            'success': False,
            'message': 'Hiçbir email gönderilemedi',
            'details': results
        }), 500

@training_bp.route('/<int:training_id>/confirm/<int:user_id>')
def confirm_participation(training_id, user_id):
    """Katılım teyidi (SMS linkinden)"""
    training = Training.query.get_or_404(training_id)
    user = User.query.get_or_404(user_id)

    enrollment = Enrollment.query.filter_by(
        user_id=user_id,
        training_id=training_id,
        aktif_mi=True
    ).first()

    if not enrollment:
        flash('Kayıt bulunamadı.', 'error')
        return render_template('confirmation_result.html',
                             success=False,
                             message='Eğitim kaydınız bulunamadı.')

    if request.method == 'POST':
        confirmation = request.form.get('confirmation')

        if confirmation == 'confirm':
            enrollment.katilim_teyidi_durumu = 'teyit_edildi'
            enrollment.katilim_teyidi_tarihi = datetime.utcnow()
            message = f'"{training.ad}" eğitimine katılımınız teyit edildi.'
            success = True
        elif confirmation == 'decline':
            enrollment.katilim_teyidi_durumu = 'reddedildi'
            enrollment.katilim_teyidi_tarihi = datetime.utcnow()
            message = f'"{training.ad}" eğitimine katılımınız iptal edildi.'
            success = True
        else:
            message = 'Geçersiz seçim.'
            success = False

        if success:
            try:
                db.session.commit()
            except Exception as e:
                db.session.rollback()
                message = 'Teyit işlemi sırasında hata oluştu.'
                success = False

        return render_template('confirmation_result.html',
                             success=success,
                             message=message,
                             training=training,
                             user=user)

    return render_template('confirmation_result.html',
                         training=training,
                         user=user,
                         enrollment=enrollment)

@training_bp.route('/<int:training_id>/manager-survey/<int:manager_id>')
def manager_survey(training_id, manager_id):
    """Yönetici değerlendirme anketi"""
    training = Training.query.get_or_404(training_id)
    manager = User.query.get_or_404(manager_id)

    # Manager'ın bu eğitime katılan ekip üyelerini bul
    team_participants = []
    for enrollment in training.enrollments.filter_by(aktif_mi=True):
        if enrollment.user.manager_id == manager_id:
            team_participants.append(enrollment.user)

    if not team_participants:
        flash('Bu eğitime katılan ekip üyeniz bulunamadı.', 'error')
        return render_template('error.html',
                             message='Bu eğitime katılan ekip üyeniz bulunamadı.')

    return render_template('manager_survey.html',
                         training=training,
                         manager=manager,
                         team_participants=team_participants)

@training_bp.route('/<int:training_id>/instructor-confirmation', methods=['POST'])
@login_required
def send_instructor_confirmation_sms(training_id):
    """Eğitmene onay SMS'i gönder"""
    training = Training.query.get_or_404(training_id)

    if not current_user.can_manage_trainings():
        return jsonify({'success': False, 'message': 'Yetkiniz yok'}), 403

    if not training.egitmen_telefon:
        return jsonify({'success': False, 'message': 'Eğitmen telefon numarası bulunamadı'}), 400

    # Onay kodu oluştur
    onay_kodu = training.generate_onay_kodu()

    try:
        db.session.commit()  # Kodu kaydet

        # SMS gönder
        from app.services.notification_service import NotificationService
        notification_service = NotificationService()

        message = f"""
Sayın {training.egitmen_bilgisi['ad']},

"{training.ad}" eğitimini verdiğinizi onaylamak için aşağıdaki kodu kullanın:

Onay Kodu: {onay_kodu}

Onay linki: {request.url_root}training/{training_id}/instructor-confirm/{onay_kodu}

Eğitim Tarihi: {training.baslangic_tarihi.strftime('%d.%m.%Y %H:%M')}
        """.strip()

        success = notification_service.send_sms(
            to_phone=training.egitmen_telefon,
            message=message,
            related_training_id=training_id
        )

        if success:
            return jsonify({
                'success': True,
                'message': f'Eğitmene onay SMS\'i gönderildi: {training.egitmen_telefon}'
            })
        else:
            return jsonify({
                'success': False,
                'message': 'SMS gönderilemedi'
            }), 500

    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': 'SMS gönderilirken hata oluştu'
        }), 500

@training_bp.route('/<int:training_id>/instructor-confirm/<onay_kodu>')
def instructor_confirm(training_id, onay_kodu):
    """Eğitmen onay sayfası (SMS linkinden)"""
    training = Training.query.get_or_404(training_id)

    if training.egitmen_onay_kodu != onay_kodu:
        flash('Geçersiz onay kodu.', 'error')
        return render_template('instructor_confirmation.html',
                             training=training,
                             success=False,
                             message='Geçersiz onay kodu.')

    if request.method == 'POST':
        confirmation = request.form.get('confirmation')

        if confirmation == 'confirm':
            success, message = training.egitmen_onayini_ver(onay_kodu)
            if success:
                try:
                    db.session.commit()
                    return render_template('instructor_confirmation.html',
                                         training=training,
                                         success=True,
                                         message='Eğitim başarıyla onaylandı.')
                except Exception as e:
                    db.session.rollback()
                    return render_template('instructor_confirmation.html',
                                         training=training,
                                         success=False,
                                         message='Onay kaydedilirken hata oluştu.')
            else:
                return render_template('instructor_confirmation.html',
                                     training=training,
                                     success=False,
                                     message=message)
        else:
            return render_template('instructor_confirmation.html',
                                 training=training,
                                 success=False,
                                 message='Onay iptal edildi.')

    return render_template('instructor_confirmation.html',
                         training=training,
                         show_form=True)

@training_bp.route('/<int:training_id>/instructor-status', methods=['POST'])
@login_required
def toggle_instructor_confirmation(training_id):
    """Eğitmen onayını manuel olarak değiştir (admin)"""
    training = Training.query.get_or_404(training_id)

    if not current_user.is_admin:
        return jsonify({'success': False, 'message': 'Yetkiniz yok'}), 403

    try:
        if training.egitim_verildi_mi:
            training.egitmen_onayini_iptal_et()
            message = 'Eğitmen onayı iptal edildi'
        else:
            training.egitmen_onayini_ver()
            message = 'Eğitmen onayı verildi'

        db.session.commit()

        return jsonify({
            'success': True,
            'message': message,
            'new_status': training.egitim_verildi_mi
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': 'İşlem sırasında hata oluştu'
        }), 500
