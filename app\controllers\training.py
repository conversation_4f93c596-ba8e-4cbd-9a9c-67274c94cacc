from datetime import datetime
from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from app import db
from app.models.user import User
from app.models.training import Training, Enrollment
from app.models.quiz import Quiz
from app.models.communication import Survey

training_bp = Blueprint('training', __name__)

@training_bp.route('/')
@login_required
def list_trainings():
    """Eğitim listesi"""
    page = request.args.get('page', 1, type=int)
    per_page = 10
    
    if current_user.can_manage_trainings():
        # Admin/Manager tüm eğitimleri görebilir
        trainings = Training.query.order_by(Training.olusturulma_tarihi.desc()).paginate(
            page=page, per_page=per_page, error_out=False
        )
    else:
        # Katılımcı sadece kayıtlı olduğu eğitimleri görebilir
        training_ids = [e.training_id for e in current_user.enrollments.filter_by(aktif_mi=True).all()]
        trainings = Training.query.filter(Training.id.in_(training_ids)).order_by(
            Training.baslangic_tarihi.desc()
        ).paginate(page=page, per_page=per_page, error_out=False)
    
    return render_template('training_list.html', trainings=trainings)

@training_bp.route('/create', methods=['GET', 'POST'])
@login_required
def create_training():
    """Yeni eğitim oluşturma"""
    if not current_user.can_manage_trainings():
        flash('Bu işlem için yetkiniz yok.', 'error')
        return redirect(url_for('training.list_trainings'))
    
    if request.method == 'POST':
        ad = request.form.get('ad', '').strip()
        aciklama = request.form.get('aciklama', '').strip()
        baslangic_tarihi_str = request.form.get('baslangic_tarihi', '')
        bitis_tarihi_str = request.form.get('bitis_tarihi', '')
        
        # Validasyon
        if not all([ad, baslangic_tarihi_str, bitis_tarihi_str]):
            flash('Eğitim adı, başlangıç ve bitiş tarihleri zorunludur.', 'error')
            return render_template('training_form.html')
        
        try:
            baslangic_tarihi = datetime.strptime(baslangic_tarihi_str, '%Y-%m-%dT%H:%M')
            bitis_tarihi = datetime.strptime(bitis_tarihi_str, '%Y-%m-%dT%H:%M')
        except ValueError:
            flash('Geçersiz tarih formatı.', 'error')
            return render_template('training_form.html')
        
        if bitis_tarihi <= baslangic_tarihi:
            flash('Bitiş tarihi başlangıç tarihinden sonra olmalıdır.', 'error')
            return render_template('training_form.html')
        
        # Yeni eğitim oluştur
        training = Training(
            ad=ad,
            aciklama=aciklama,
            baslangic_tarihi=baslangic_tarihi,
            bitis_tarihi=bitis_tarihi,
            olusturan_id=current_user.id
        )
        
        try:
            db.session.add(training)
            db.session.commit()
            flash(f'"{training.ad}" eğitimi başarıyla oluşturuldu.', 'success')
            return redirect(url_for('training.detail', training_id=training.id))
        except Exception as e:
            db.session.rollback()
            flash('Eğitim oluşturulurken bir hata oluştu.', 'error')
    
    return render_template('training_form.html')

@training_bp.route('/<int:training_id>')
@login_required
def detail(training_id):
    """Eğitim detayları"""
    training = Training.query.get_or_404(training_id)
    
    # Yetki kontrolü
    if not current_user.can_manage_trainings() and not training.is_participant(current_user):
        flash('Bu eğitimi görüntüleme yetkiniz yok.', 'error')
        return redirect(url_for('training.list_trainings'))
    
    # Katılımcıları getir
    enrollments = training.enrollments.filter_by(aktif_mi=True).all()
    
    # Quizleri getir
    quizzes = training.get_active_quizzes()
    
    # Anketleri getir
    surveys = training.surveys.filter_by(aktif_mi=True).all()
    
    return render_template('training_detail.html', 
                         training=training, 
                         enrollments=enrollments,
                         quizzes=quizzes,
                         surveys=surveys)

@training_bp.route('/<int:training_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_training(training_id):
    """Eğitim düzenleme"""
    training = Training.query.get_or_404(training_id)
    
    if not current_user.can_manage_trainings():
        flash('Bu işlem için yetkiniz yok.', 'error')
        return redirect(url_for('training.detail', training_id=training_id))
    
    if request.method == 'POST':
        ad = request.form.get('ad', '').strip()
        aciklama = request.form.get('aciklama', '').strip()
        baslangic_tarihi_str = request.form.get('baslangic_tarihi', '')
        bitis_tarihi_str = request.form.get('bitis_tarihi', '')
        
        # Validasyon
        if not all([ad, baslangic_tarihi_str, bitis_tarihi_str]):
            flash('Eğitim adı, başlangıç ve bitiş tarihleri zorunludur.', 'error')
            return render_template('training_form.html', training=training)
        
        try:
            baslangic_tarihi = datetime.strptime(baslangic_tarihi_str, '%Y-%m-%dT%H:%M')
            bitis_tarihi = datetime.strptime(bitis_tarihi_str, '%Y-%m-%dT%H:%M')
        except ValueError:
            flash('Geçersiz tarih formatı.', 'error')
            return render_template('training_form.html', training=training)
        
        if bitis_tarihi <= baslangic_tarihi:
            flash('Bitiş tarihi başlangıç tarihinden sonra olmalıdır.', 'error')
            return render_template('training_form.html', training=training)
        
        # Eğitimi güncelle
        training.ad = ad
        training.aciklama = aciklama
        training.baslangic_tarihi = baslangic_tarihi
        training.bitis_tarihi = bitis_tarihi
        
        try:
            db.session.commit()
            flash(f'"{training.ad}" eğitimi başarıyla güncellendi.', 'success')
            return redirect(url_for('training.detail', training_id=training.id))
        except Exception as e:
            db.session.rollback()
            flash('Eğitim güncellenirken bir hata oluştu.', 'error')
    
    return render_template('training_form.html', training=training)

@training_bp.route('/<int:training_id>/participants')
@login_required
def manage_participants(training_id):
    """Katılımcı yönetimi"""
    training = Training.query.get_or_404(training_id)
    
    if not current_user.can_manage_trainings():
        flash('Bu işlem için yetkiniz yok.', 'error')
        return redirect(url_for('training.detail', training_id=training_id))
    
    # Mevcut katılımcılar
    current_participants = training.get_participants()
    current_participant_ids = [p.id for p in current_participants]
    
    # Potansiyel katılımcılar (henüz bu eğitime kayıtlı olmayanlar)
    available_users = User.query.filter(
        ~User.id.in_(current_participant_ids),
        User.aktif_mi == True
    ).order_by(User.ad, User.soyad).all()
    
    return render_template('manage_participants.html', 
                         training=training,
                         current_participants=current_participants,
                         available_users=available_users)

@training_bp.route('/<int:training_id>/add-participant', methods=['POST'])
@login_required
def add_participant(training_id):
    """Katılımcı ekleme"""
    training = Training.query.get_or_404(training_id)
    
    if not current_user.can_manage_trainings():
        return jsonify({'success': False, 'message': 'Yetkiniz yok'}), 403
    
    user_id = request.form.get('user_id', type=int)
    if not user_id:
        return jsonify({'success': False, 'message': 'Kullanıcı seçilmedi'}), 400
    
    user = User.query.get_or_404(user_id)
    
    # Zaten kayıtlı mı kontrol et
    existing_enrollment = Enrollment.query.filter_by(
        user_id=user_id, 
        training_id=training_id
    ).first()
    
    if existing_enrollment:
        if existing_enrollment.aktif_mi:
            return jsonify({'success': False, 'message': 'Kullanıcı zaten kayıtlı'}), 400
        else:
            # Pasif kaydı aktif yap
            existing_enrollment.aktif_mi = True
            existing_enrollment.katilim_teyidi_durumu = 'beklemede'
            existing_enrollment.kayit_tarihi = datetime.utcnow()
    else:
        # Yeni kayıt oluştur
        enrollment = Enrollment(
            user_id=user_id,
            training_id=training_id
        )
        db.session.add(enrollment)
    
    try:
        db.session.commit()
        return jsonify({
            'success': True, 
            'message': f'{user.tam_ad} eğitime başarıyla eklendi'
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': 'Kayıt eklenirken hata oluştu'}), 500

@training_bp.route('/<int:training_id>/remove-participant/<int:user_id>', methods=['POST'])
@login_required
def remove_participant(training_id, user_id):
    """Katılımcı çıkarma"""
    training = Training.query.get_or_404(training_id)
    
    if not current_user.can_manage_trainings():
        return jsonify({'success': False, 'message': 'Yetkiniz yok'}), 403
    
    enrollment = Enrollment.query.filter_by(
        user_id=user_id, 
        training_id=training_id,
        aktif_mi=True
    ).first()
    
    if not enrollment:
        return jsonify({'success': False, 'message': 'Kayıt bulunamadı'}), 404
    
    user = enrollment.user
    enrollment.aktif_mi = False
    
    try:
        db.session.commit()
        return jsonify({
            'success': True, 
            'message': f'{user.tam_ad} eğitimden çıkarıldı'
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': 'Kayıt çıkarılırken hata oluştu'}), 500
