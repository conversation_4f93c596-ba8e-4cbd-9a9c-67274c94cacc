{% extends "base.html" %}

{% block title %}Matery<PERSON> - {{ training.ad }}{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-upload"></i> Mat<PERSON><PERSON>
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{{ url_for('material.list_materials', training_id=training.id) }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left"></i> Geri <PERSON>ö<PERSON>
        </a>
    </div>
</div>

<!-- Eğitim Bilgisi -->
<div class="alert alert-info">
    <h6><i class="fas fa-info-circle"></i> Eğitim: {{ training.ad }}</h6>
    <small class="text-muted">
        {{ training.baslangic_tarihi.strftime('%d.%m.%Y') }} - {{ training.bitis_tarihi.strftime('%d.%m.%Y') }}
    </small>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card shadow">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-file-upload"></i> Dosya Yükleme
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" enctype="multipart/form-data" id="uploadForm">
                    <!-- Dosya Seçimi -->
                    <div class="mb-4">
                        <label for="file" class="form-label">Dosya Seçin *</label>
                        <div class="file-drop-area" id="fileDropArea">
                            <div class="file-drop-content">
                                <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                                <p class="mb-2">Dosyayı buraya sürükleyin veya tıklayın</p>
                                <input type="file" class="form-control" id="file" name="file" required style="display: none;">
                                <button type="button" class="btn btn-outline-primary" onclick="document.getElementById('file').click()">
                                    <i class="fas fa-folder-open"></i> Dosya Seç
                                </button>
                            </div>
                        </div>
                        <div id="fileInfo" class="mt-2" style="display: none;">
                            <div class="alert alert-success">
                                <i class="fas fa-file"></i> <span id="fileName"></span>
                                <br><small id="fileSize" class="text-muted"></small>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Açıklama -->
                    <div class="mb-3">
                        <label for="description" class="form-label">Açıklama</label>
                        <textarea class="form-control" id="description" name="description" rows="3" 
                                  placeholder="Bu materyal hakkında kısa bir açıklama yazın..."></textarea>
                        <div class="form-text">Opsiyonel: Materyalin içeriği hakkında bilgi verin</div>
                    </div>
                    
                    <!-- Progress Bar -->
                    <div id="uploadProgress" class="mb-3" style="display: none;">
                        <div class="progress">
                            <div class="progress-bar progress-bar-striped progress-bar-animated" 
                                 role="progressbar" style="width: 0%"></div>
                        </div>
                        <small class="text-muted">Dosya yükleniyor...</small>
                    </div>
                    
                    <!-- Submit Buttons -->
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('material.list_materials', training_id=training.id) }}" class="btn btn-secondary">
                            <i class="fas fa-times"></i> İptal
                        </a>
                        <button type="submit" class="btn btn-primary" id="submitBtn">
                            <i class="fas fa-upload"></i> Yükle
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <!-- Desteklenen Dosya Türleri -->
        <div class="card shadow">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle"></i> Desteklenen Dosya Türleri
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6 mb-3">
                        <i class="fas fa-file-powerpoint fa-2x text-warning"></i>
                        <br><small>Sunumlar</small>
                        <br><small class="text-muted">.ppt, .pptx</small>
                    </div>
                    <div class="col-6 mb-3">
                        <i class="fas fa-file-pdf fa-2x text-danger"></i>
                        <br><small>PDF</small>
                        <br><small class="text-muted">.pdf</small>
                    </div>
                    <div class="col-6 mb-3">
                        <i class="fas fa-file-word fa-2x text-primary"></i>
                        <br><small>Dökümanlar</small>
                        <br><small class="text-muted">.doc, .docx</small>
                    </div>
                    <div class="col-6 mb-3">
                        <i class="fas fa-file-excel fa-2x text-success"></i>
                        <br><small>Tablolar</small>
                        <br><small class="text-muted">.xls, .xlsx</small>
                    </div>
                    <div class="col-6 mb-3">
                        <i class="fas fa-file-video fa-2x text-info"></i>
                        <br><small>Videolar</small>
                        <br><small class="text-muted">.mp4, .avi</small>
                    </div>
                    <div class="col-6 mb-3">
                        <i class="fas fa-file-image fa-2x text-secondary"></i>
                        <br><small>Resimler</small>
                        <br><small class="text-muted">.jpg, .png</small>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Dosya Boyutu Limitleri -->
        <div class="card shadow mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-weight-hanging"></i> Boyut Limitleri
                </h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled mb-0">
                    <li><i class="fas fa-file-video text-info"></i> Videolar: 500MB</li>
                    <li><i class="fas fa-file-powerpoint text-warning"></i> Sunumlar: 50MB</li>
                    <li><i class="fas fa-file-pdf text-danger"></i> PDF: 50MB</li>
                    <li><i class="fas fa-file-word text-primary"></i> Dökümanlar: 25MB</li>
                    <li><i class="fas fa-file-excel text-success"></i> Tablolar: 25MB</li>
                    <li><i class="fas fa-file-image text-secondary"></i> Resimler: 10MB</li>
                </ul>
            </div>
        </div>
        
        <!-- İpuçları -->
        <div class="card shadow mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-lightbulb"></i> İpuçları
                </h6>
            </div>
            <div class="card-body">
                <ul class="small mb-0">
                    <li>Dosya adlarında Türkçe karakter kullanmaktan kaçının</li>
                    <li>Büyük video dosyaları için sıkıştırma kullanın</li>
                    <li>PDF dosyaları online görüntülenebilir</li>
                    <li>Açıklama ekleyerek materyalin amacını belirtin</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.file-drop-area {
    border: 2px dashed #dee2e6;
    border-radius: 8px;
    padding: 40px;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
}

.file-drop-area:hover {
    border-color: #007bff;
    background-color: #f8f9fa;
}

.file-drop-area.dragover {
    border-color: #007bff;
    background-color: #e3f2fd;
}

.file-drop-content {
    pointer-events: none;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    const fileInput = $('#file');
    const fileDropArea = $('#fileDropArea');
    const fileInfo = $('#fileInfo');
    const fileName = $('#fileName');
    const fileSize = $('#fileSize');
    const uploadForm = $('#uploadForm');
    const submitBtn = $('#submitBtn');
    const uploadProgress = $('#uploadProgress');

    // Drag & Drop
    fileDropArea.on('dragover', function(e) {
        e.preventDefault();
        $(this).addClass('dragover');
    });

    fileDropArea.on('dragleave', function(e) {
        e.preventDefault();
        $(this).removeClass('dragover');
    });

    fileDropArea.on('drop', function(e) {
        e.preventDefault();
        $(this).removeClass('dragover');

        const files = e.originalEvent.dataTransfer.files;
        if (files.length > 0) {
            fileInput[0].files = files;
            showFileInfo(files[0]);
        }
    });

    // Dosya seçimi
    fileInput.on('change', function() {
        if (this.files.length > 0) {
            showFileInfo(this.files[0]);
        }
    });

    // Dosya bilgilerini göster
    function showFileInfo(file) {
        fileName.text(file.name);
        fileSize.text(formatFileSize(file.size));
        fileInfo.show();

        // Dosya türü kontrolü
        if (!isAllowedFile(file.name)) {
            fileInfo.removeClass('alert-success').addClass('alert-danger');
            fileName.prepend('<i class="fas fa-exclamation-triangle"></i> ');
            fileSize.text('Bu dosya türü desteklenmiyor!');
            submitBtn.prop('disabled', true);
        } else {
            fileInfo.removeClass('alert-danger').addClass('alert-success');
            submitBtn.prop('disabled', false);
        }
    }

    // Dosya boyutunu formatla
    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // İzin verilen dosya türleri
    function isAllowedFile(filename) {
        const allowedExtensions = [
            '.ppt', '.pptx', '.odp',
            '.pdf',
            '.doc', '.docx', '.odt', '.txt', '.rtf',
            '.xls', '.xlsx', '.ods', '.csv',
            '.mp4', '.avi', '.mov', '.wmv', '.webm', '.mkv', '.flv',
            '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.svg', '.webp'
        ];

        const ext = filename.toLowerCase().substring(filename.lastIndexOf('.'));
        return allowedExtensions.includes(ext);
    }

    // Form submit
    uploadForm.on('submit', function(e) {
        if (!fileInput[0].files.length) {
            e.preventDefault();
            alert('Lütfen bir dosya seçin.');
            return;
        }

        submitBtn.prop('disabled', true);
        uploadProgress.show();

        // Progress bar animasyonu
        let progress = 0;
        const interval = setInterval(function() {
            progress += Math.random() * 15;
            if (progress > 90) progress = 90;
            $('.progress-bar').css('width', progress + '%');
        }, 200);

        // Form submit sonrası interval'i temizle
        setTimeout(function() {
            clearInterval(interval);
        }, 5000);
    });
});
</script>
{% endblock %}
