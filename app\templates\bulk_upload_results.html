{% extends "base.html" %}

{% block title %}Toplu Yükleme Sonuçları{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-chart-bar"></i> Toplu Yükleme Sonuçları
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('auth.users') }}" class="btn btn-outline-primary">
                <i class="fas fa-users"></i> Kullanıcı Listesi
            </a>
            <a href="{{ url_for('auth.bulk_upload_users') }}" class="btn btn-outline-secondary">
                <i class="fas fa-upload"></i> <PERSON><PERSON>
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <!-- <PERSON><PERSON><PERSON> -->
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="card text-white bg-success">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4 class="mb-0">{{ results.success_count }}</h4>
                                <p class="mb-0">Başarılı</p>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-check-circle fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card text-white bg-danger">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4 class="mb-0">{{ results.error_count }}</h4>
                                <p class="mb-0">Hatalı</p>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-times-circle fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card text-white bg-info">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4 class="mb-0">{{ results.success_count + results.error_count }}</h4>
                                <p class="mb-0">Toplam</p>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-list fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Başarılı Kullanıcılar -->
        {% if results.created_users %}
        <div class="card shadow mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-check-circle text-success"></i> 
                    Başarıyla Oluşturulan Kullanıcılar ({{ results.success_count }})
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>#</th>
                                <th>Ad Soyad</th>
                                <th>E-posta</th>
                                <th>Rol</th>
                                <th>Durum</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for user in results.created_users %}
                            <tr>
                                <td>{{ loop.index }}</td>
                                <td>{{ user.ad }} {{ user.soyad }}</td>
                                <td>{{ user.email }}</td>
                                <td>
                                    <span class="badge 
                                        {% if user.rol == 'admin' %}bg-danger
                                        {% elif user.rol == 'manager' %}bg-warning
                                        {% else %}bg-info{% endif %}">
                                        {% if user.rol == 'admin' %}Admin
                                        {% elif user.rol == 'manager' %}Manager
                                        {% else %}Participant{% endif %}
                                    </span>
                                </td>
                                <td>
                                    <span class="badge bg-success">
                                        <i class="fas fa-check"></i> Oluşturuldu
                                    </span>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Hatalar -->
        {% if results.errors %}
        <div class="card shadow">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-exclamation-triangle text-warning"></i> 
                    Hatalar ({{ results.error_count }})
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-warning">
                    <i class="fas fa-info-circle"></i>
                    <strong>Dikkat:</strong> Aşağıdaki hatalar nedeniyle bazı kullanıcılar oluşturulamadı.
                </div>
                
                <div class="list-group">
                    {% for error in results.errors %}
                    <div class="list-group-item list-group-item-warning">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        {{ error }}
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
        {% endif %}
    </div>
    
    <div class="col-lg-4">
        <!-- İstatistikler -->
        <div class="card shadow">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-chart-pie"></i> İşlem Özeti
                </h6>
            </div>
            <div class="card-body">
                {% set total = results.success_count + results.error_count %}
                {% if total > 0 %}
                <div class="mb-3">
                    <div class="d-flex justify-content-between mb-1">
                        <span>Başarı Oranı</span>
                        <span>{{ "%.1f"|format((results.success_count / total) * 100) }}%</span>
                    </div>
                    <div class="progress">
                        <div class="progress-bar bg-success" 
                             style="width: {{ (results.success_count / total) * 100 }}%"></div>
                    </div>
                </div>
                {% endif %}
                
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <div class="h4 text-success mb-0">{{ results.success_count }}</div>
                            <small class="text-muted">Başarılı</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="h4 text-danger mb-0">{{ results.error_count }}</div>
                        <small class="text-muted">Hatalı</small>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Sonraki Adımlar -->
        <div class="card shadow mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-tasks"></i> Sonraki Adımlar
                </h6>
            </div>
            <div class="card-body">
                <div class="list-group list-group-flush">
                    {% if results.success_count > 0 %}
                    <div class="list-group-item border-0 px-0">
                        <i class="fas fa-users text-primary me-2"></i>
                        <a href="{{ url_for('auth.users') }}" class="text-decoration-none">
                            Oluşturulan kullanıcıları görüntüle
                        </a>
                    </div>
                    {% endif %}
                    
                    {% if results.error_count > 0 %}
                    <div class="list-group-item border-0 px-0">
                        <i class="fas fa-edit text-warning me-2"></i>
                        Hatalı kayıtları düzelterek tekrar deneyin
                    </div>
                    {% endif %}
                    
                    <div class="list-group-item border-0 px-0">
                        <i class="fas fa-upload text-info me-2"></i>
                        <a href="{{ url_for('auth.bulk_upload_users') }}" class="text-decoration-none">
                            Yeni dosya yükle
                        </a>
                    </div>
                    
                    <div class="list-group-item border-0 px-0">
                        <i class="fas fa-download text-success me-2"></i>
                        <a href="{{ url_for('auth.download_sample_excel') }}" class="text-decoration-none">
                            Örnek dosyayı indir
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
