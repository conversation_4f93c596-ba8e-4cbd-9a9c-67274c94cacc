{% extends "base.html" %}

{% block title %}Kullanıcı Yönetimi - Eğitim Yönetim Sistemi{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-users"></i> Kullanıcı Yönetimi
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{{ url_for('auth.register') }}" class="btn btn-primary">
            <i class="fas fa-user-plus"></i> <PERSON><PERSON>llan<PERSON>
        </a>
    </div>
</div>

<!-- Filtreler -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-3">
                        <label for="rol" class="form-label">Rol</label>
                        <select class="form-select" id="rol" name="rol">
                            <option value="">Tüm Roller</option>
                            <option value="admin" {{ 'selected' if request.args.get('rol') == 'admin' }}>Admin</option>
                            <option value="manager" {{ 'selected' if request.args.get('rol') == 'manager' }}>Manager</option>
                            <option value="participant" {{ 'selected' if request.args.get('rol') == 'participant' }}>Participant</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="durum" class="form-label">Durum</label>
                        <select class="form-select" id="durum" name="durum">
                            <option value="">Tümü</option>
                            <option value="aktif" {{ 'selected' if request.args.get('durum') == 'aktif' }}>Aktif</option>
                            <option value="pasif" {{ 'selected' if request.args.get('durum') == 'pasif' }}>Pasif</option>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label for="arama" class="form-label">Arama</label>
                        <input type="text" class="form-control" id="arama" name="arama" 
                               placeholder="Ad, soyad veya e-posta..." 
                               value="{{ request.args.get('arama', '') }}">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <div>
                            <button type="submit" class="btn btn-outline-primary">
                                <i class="fas fa-search"></i> Filtrele
                            </button>
                            <a href="{{ url_for('auth.users') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-times"></i>
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Kullanıcı Listesi -->
<div class="card shadow">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-list"></i> Kullanıcılar 
            <span class="badge bg-secondary">{{ users.total }}</span>
        </h5>
    </div>
    <div class="card-body">
        {% if users.items %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>Ad Soyad</th>
                        <th>E-posta</th>
                        <th>Telefon</th>
                        <th>Rol</th>
                        <th>Durum</th>
                        <th>Kayıt Tarihi</th>
                        <th>Son Giriş</th>
                        <th>İşlemler</th>
                    </tr>
                </thead>
                <tbody>
                    {% for user in users.items %}
                    <tr id="user-{{ user.id }}" class="{{ 'table-secondary' if not user.aktif_mi }}">
                        <td>
                            <strong>{{ user.tam_ad }}</strong>
                            {% if user.id == current_user.id %}
                            <span class="badge bg-info ms-1">Siz</span>
                            {% endif %}
                        </td>
                        <td>{{ user.email }}</td>
                        <td>{{ user.telefon_numarasi }}</td>
                        <td>
                            <span class="badge 
                                {% if user.rol == 'admin' %}bg-danger
                                {% elif user.rol == 'manager' %}bg-warning
                                {% else %}bg-info{% endif %}">
                                {% if user.rol == 'admin' %}Admin
                                {% elif user.rol == 'manager' %}Manager
                                {% else %}Participant{% endif %}
                            </span>
                        </td>
                        <td>
                            <span class="badge {{ 'bg-success' if user.aktif_mi else 'bg-secondary' }}">
                                {{ 'Aktif' if user.aktif_mi else 'Pasif' }}
                            </span>
                        </td>
                        <td>
                            <small class="text-muted">
                                {{ user.olusturulma_tarihi.strftime('%d.%m.%Y') if user.olusturulma_tarihi }}
                            </small>
                        </td>
                        <td>
                            <small class="text-muted">
                                {% if user.son_giris_tarihi %}
                                {{ user.son_giris_tarihi.strftime('%d.%m.%Y %H:%M') }}
                                {% else %}
                                Hiç giriş yapmamış
                                {% endif %}
                            </small>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                {% if user.id != current_user.id %}
                                <button type="button" class="btn btn-outline-{{ 'secondary' if user.aktif_mi else 'success' }}" 
                                        onclick="toggleUserStatus({{ user.id }}, '{{ user.tam_ad }}', {{ user.aktif_mi|lower }})">
                                    <i class="fas fa-{{ 'ban' if user.aktif_mi else 'check' }}"></i>
                                    {{ 'Pasifleştir' if user.aktif_mi else 'Aktifleştir' }}
                                </button>
                                {% endif %}
                                
                                <button type="button" class="btn btn-outline-info" 
                                        onclick="showUserDetails({{ user.id }})">
                                    <i class="fas fa-eye"></i> Detay
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        {% if users.pages > 1 %}
        <nav aria-label="Kullanıcı listesi sayfalama" class="mt-4">
            <ul class="pagination justify-content-center">
                {% if users.has_prev %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('auth.users', page=users.prev_num, **request.args) }}">Önceki</a>
                </li>
                {% endif %}
                
                {% for page_num in users.iter_pages() %}
                    {% if page_num %}
                        {% if page_num != users.page %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('auth.users', page=page_num, **request.args) }}">{{ page_num }}</a>
                        </li>
                        {% else %}
                        <li class="page-item active">
                            <span class="page-link">{{ page_num }}</span>
                        </li>
                        {% endif %}
                    {% else %}
                    <li class="page-item disabled">
                        <span class="page-link">…</span>
                    </li>
                    {% endif %}
                {% endfor %}
                
                {% if users.has_next %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('auth.users', page=users.next_num, **request.args) }}">Sonraki</a>
                </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}
        
        {% else %}
        <div class="text-center text-muted py-4">
            <i class="fas fa-users fa-3x mb-3"></i>
            <p>Kullanıcı bulunamadı.</p>
            <a href="{{ url_for('auth.register') }}" class="btn btn-primary">
                <i class="fas fa-user-plus"></i> İlk Kullanıcıyı Oluştur
            </a>
        </div>
        {% endif %}
    </div>
</div>

<!-- Kullanıcı Detay Modalı -->
<div class="modal fade" id="userDetailModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-user"></i> Kullanıcı Detayları
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="userDetailContent">
                <!-- İçerik AJAX ile yüklenecek -->
                <div class="text-center">
                    <i class="fas fa-spinner fa-spin fa-2x"></i>
                    <p class="mt-2">Yükleniyor...</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function toggleUserStatus(userId, userName, isActive) {
    let action = isActive ? 'pasifleştirmek' : 'aktifleştirmek';
    
    if (confirm(`${userName} kullanıcısını ${action} istediğinizden emin misiniz?`)) {
        fetch(`/auth/users/${userId}/toggle-status`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => {
            if (response.ok) {
                location.reload();
            } else {
                alert('İşlem sırasında bir hata oluştu.');
            }
        })
        .catch(error => {
            alert('Bir hata oluştu: ' + error);
        });
    }
}

function showUserDetails(userId) {
    $('#userDetailModal').modal('show');
    
    // AJAX ile kullanıcı detaylarını yükle
    fetch(`/auth/users/${userId}/details`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                let user = data.user;
                let html = `
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Kişisel Bilgiler</h6>
                            <table class="table table-sm">
                                <tr><td><strong>Ad Soyad:</strong></td><td>${user.tam_ad}</td></tr>
                                <tr><td><strong>E-posta:</strong></td><td>${user.email}</td></tr>
                                <tr><td><strong>Telefon:</strong></td><td>${user.telefon_numarasi}</td></tr>
                                <tr><td><strong>Rol:</strong></td><td><span class="badge bg-info">${user.rol}</span></td></tr>
                                <tr><td><strong>Durum:</strong></td><td><span class="badge ${user.aktif_mi ? 'bg-success' : 'bg-secondary'}">${user.aktif_mi ? 'Aktif' : 'Pasif'}</span></td></tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h6>Sistem Bilgileri</h6>
                            <table class="table table-sm">
                                <tr><td><strong>Kayıt Tarihi:</strong></td><td>${user.olusturulma_tarihi || 'Bilinmiyor'}</td></tr>
                                <tr><td><strong>Son Giriş:</strong></td><td>${user.son_giris_tarihi || 'Hiç giriş yapmamış'}</td></tr>
                                <tr><td><strong>Eğitim Sayısı:</strong></td><td>${data.stats.egitim_sayisi}</td></tr>
                                <tr><td><strong>Quiz Sayısı:</strong></td><td>${data.stats.quiz_sayisi}</td></tr>
                                <tr><td><strong>Ortalama Skor:</strong></td><td>${data.stats.ortalama_skor}%</td></tr>
                            </table>
                        </div>
                    </div>
                `;
                
                if (data.recent_activities && data.recent_activities.length > 0) {
                    html += `
                        <hr>
                        <h6>Son Aktiviteler</h6>
                        <ul class="list-group list-group-flush">
                    `;
                    
                    data.recent_activities.forEach(activity => {
                        html += `
                            <li class="list-group-item d-flex justify-content-between align-items-start">
                                <div>
                                    <strong>${activity.title}</strong><br>
                                    <small class="text-muted">${activity.description}</small>
                                </div>
                                <small class="text-muted">${activity.date}</small>
                            </li>
                        `;
                    });
                    
                    html += '</ul>';
                }
                
                $('#userDetailContent').html(html);
            } else {
                $('#userDetailContent').html('<div class="alert alert-danger">Kullanıcı detayları yüklenemedi.</div>');
            }
        })
        .catch(error => {
            $('#userDetailContent').html('<div class="alert alert-danger">Bir hata oluştu: ' + error + '</div>');
        });
}

// Sayfa yüklendiğinde filtreleri uygula
$(document).ready(function() {
    // URL parametrelerine göre filtreleri ayarla
    let urlParams = new URLSearchParams(window.location.search);
    
    if (urlParams.get('rol')) {
        $('#rol').val(urlParams.get('rol'));
    }
    
    if (urlParams.get('durum')) {
        $('#durum').val(urlParams.get('durum'));
    }
    
    if (urlParams.get('arama')) {
        $('#arama').val(urlParams.get('arama'));
    }
});
</script>
{% endblock %}
