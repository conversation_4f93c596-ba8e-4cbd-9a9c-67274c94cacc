{% extends "base.html" %}

{% block title %}{{ category_info.name }} - Sistem Ayarları{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="{{ category_info.icon }}"></i> {{ category_info.name }}
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('settings.index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> Geri
            </a>
            <button type="button" class="btn btn-success" onclick="saveSettings()">
                <i class="fas fa-save"></i> Kaydet
            </button>
            <button type="button" class="btn btn-outline-warning" onclick="resetCategory()">
                <i class="fas fa-undo"></i> Sıfırla
            </button>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <!-- Ayarlar Formu -->
        <div class="card shadow">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="{{ category_info.icon }}"></i> {{ category_info.name }}
                </h5>
                <small class="text-muted">{{ category_info.description }}</small>
            </div>
            <div class="card-body">
                <form id="settingsForm">
                    {% for setting in settings %}
                    <div class="mb-4">
                        <label for="setting_{{ setting.key }}" class="form-label">
                            <strong>{{ setting.description or setting.key }}</strong>
                            {% if setting.data_type == 'boolean' %}
                            <span class="badge bg-info ms-1">Boolean</span>
                            {% elif setting.data_type == 'integer' %}
                            <span class="badge bg-warning ms-1">Sayı</span>
                            {% elif setting.data_type == 'json' %}
                            <span class="badge bg-danger ms-1">JSON</span>
                            {% else %}
                            <span class="badge bg-secondary ms-1">Metin</span>
                            {% endif %}
                        </label>
                        
                        {% if setting.data_type == 'boolean' %}
                        <!-- Boolean Switch -->
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" 
                                   id="setting_{{ setting.key }}" 
                                   name="setting_{{ setting.key }}"
                                   {% if setting.parsed_value %}checked{% endif %}>
                            <label class="form-check-label" for="setting_{{ setting.key }}">
                                {{ 'Aktif' if setting.parsed_value else 'Pasif' }}
                            </label>
                        </div>
                        
                        {% elif setting.data_type == 'integer' %}
                        <!-- Number Input -->
                        <input type="number" class="form-control" 
                               id="setting_{{ setting.key }}" 
                               name="setting_{{ setting.key }}"
                               value="{{ setting.parsed_value or 0 }}">
                        
                        {% elif setting.data_type == 'json' %}
                        <!-- JSON Textarea -->
                        <textarea class="form-control font-monospace" 
                                  id="setting_{{ setting.key }}" 
                                  name="setting_{{ setting.key }}"
                                  rows="4">{{ setting.value or '{}' }}</textarea>
                        <div class="form-text">JSON formatında giriniz</div>
                        
                        {% else %}
                        <!-- Text Input -->
                        {% if 'password' in setting.key.lower() or 'secret' in setting.key.lower() %}
                        <div class="input-group">
                            <input type="password" class="form-control" 
                                   id="setting_{{ setting.key }}" 
                                   name="setting_{{ setting.key }}"
                                   value="{{ setting.value or '' }}">
                            <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('setting_{{ setting.key }}')">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                        {% else %}
                        <input type="text" class="form-control" 
                               id="setting_{{ setting.key }}" 
                               name="setting_{{ setting.key }}"
                               value="{{ setting.value or '' }}">
                        {% endif %}
                        {% endif %}
                        
                        <div class="form-text">
                            <small class="text-muted">Anahtar: <code>{{ setting.key }}</code></small>
                            {% if setting.updated_at %}
                            <small class="text-muted ms-2">
                                Son güncelleme: {{ setting.updated_at.strftime('%d.%m.%Y %H:%M') }}
                                {% if setting.updated_by_user %}
                                ({{ setting.updated_by_user.tam_ad }})
                                {% endif %}
                            </small>
                            {% endif %}
                        </div>
                    </div>
                    {% endfor %}
                    
                    {% if not settings %}
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-cog fa-3x mb-3"></i>
                        <p>Bu kategoride henüz ayar bulunmuyor.</p>
                    </div>
                    {% endif %}
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <!-- Kategori Bilgileri -->
        <div class="card shadow mb-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle"></i> Kategori Bilgileri
                </h6>
            </div>
            <div class="card-body">
                <table class="table table-sm">
                    <tr>
                        <td><strong>Kategori:</strong></td>
                        <td>{{ category }}</td>
                    </tr>
                    <tr>
                        <td><strong>Ayar Sayısı:</strong></td>
                        <td>{{ settings|length }}</td>
                    </tr>
                    <tr>
                        <td><strong>Açıklama:</strong></td>
                        <td>{{ category_info.description }}</td>
                    </tr>
                </table>
            </div>
        </div>
        
        <!-- Hızlı İşlemler -->
        {% if category == 'sms' %}
        <div class="card shadow mb-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-bolt"></i> Hızlı İşlemler
                </h6>
            </div>
            <div class="card-body">
                <button class="btn btn-outline-info btn-sm w-100 mb-2" onclick="testSms()">
                    <i class="fas fa-paper-plane"></i> SMS Testi
                </button>
                <button class="btn btn-outline-secondary btn-sm w-100" onclick="showSmsTemplates()">
                    <i class="fas fa-file-alt"></i> SMS Şablonları
                </button>
            </div>
        </div>
        {% elif category == 'email' %}
        <div class="card shadow mb-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-bolt"></i> Hızlı İşlemler
                </h6>
            </div>
            <div class="card-body">
                <button class="btn btn-outline-success btn-sm w-100 mb-2" onclick="testEmail()">
                    <i class="fas fa-paper-plane"></i> Email Testi
                </button>
                <button class="btn btn-outline-secondary btn-sm w-100" onclick="showEmailTemplates()">
                    <i class="fas fa-file-alt"></i> Email Şablonları
                </button>
            </div>
        </div>
        {% endif %}
        
        <!-- Yardım -->
        <div class="card shadow">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-question-circle"></i> Yardım
                </h6>
            </div>
            <div class="card-body">
                {% if category == 'sms' %}
                <p class="small">
                    <strong>SMS API Ayarları:</strong><br>
                    • API URL: SMS servis sağlayıcınızın endpoint adresi<br>
                    • API Key: Servis sağlayıcıdan aldığınız anahtar<br>
                    • Sender Name: SMS'lerde görünecek gönderen adı
                </p>
                {% elif category == 'email' %}
                <p class="small">
                    <strong>Email Sunucu Ayarları:</strong><br>
                    • SMTP sunucu adresi ve port bilgileri<br>
                    • TLS/SSL güvenlik ayarları<br>
                    • Kimlik doğrulama bilgileri
                </p>
                {% elif category == 'security' %}
                <p class="small">
                    <strong>Güvenlik Ayarları:</strong><br>
                    • Şifre politikaları<br>
                    • Oturum yönetimi<br>
                    • Giriş denemeleri ve kilitleme
                </p>
                {% else %}
                <p class="small">
                    Bu kategorideki ayarları dikkatli bir şekilde yapılandırın. 
                    Değişiklikler sistem genelinde etkili olacaktır.
                </p>
                {% endif %}
                
                <hr>
                <p class="small text-muted">
                    <i class="fas fa-lightbulb"></i>
                    Ayarları değiştirdikten sonra "Kaydet" butonuna tıklamayı unutmayın.
                </p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.form-check-input:checked {
    background-color: #28a745;
    border-color: #28a745;
}

.font-monospace {
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
}

.badge {
    font-size: 0.7rem;
}

.card {
    border: none;
    border-radius: 10px;
}

.form-control:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}
</style>
{% endblock %}

{% block extra_js %}
<script>
function saveSettings() {
    const formData = new FormData($('#settingsForm')[0]);
    
    fetch(`/settings/category/{{ category }}/save`, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('✅ ' + data.message);
            if (data.updated_count > 0) {
                location.reload();
            }
        } else {
            alert('❌ ' + data.message);
        }
    })
    .catch(error => {
        alert('❌ Hata: ' + error);
    });
}

function resetCategory() {
    if (confirm('Bu kategorideki tüm ayarları varsayılan değerlere sıfırlamak istediğinizden emin misiniz?\n\nBu işlem geri alınamaz!')) {
        fetch(`/settings/reset-category/{{ category }}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('✅ ' + data.message);
                location.reload();
            } else {
                alert('❌ ' + data.message);
            }
        })
        .catch(error => {
            alert('❌ Hata: ' + error);
        });
    }
}

function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const button = field.nextElementSibling;
    const icon = button.querySelector('i');
    
    if (field.type === 'password') {
        field.type = 'text';
        icon.className = 'fas fa-eye-slash';
    } else {
        field.type = 'password';
        icon.className = 'fas fa-eye';
    }
}

function testSms() {
    const phone = prompt('Test SMS gönderilecek telefon numarası:');
    if (phone) {
        fetch('/settings/test-sms', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({phone: phone})
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('✅ ' + data.message);
            } else {
                alert('❌ ' + data.message);
            }
        })
        .catch(error => {
            alert('❌ Hata: ' + error);
        });
    }
}

function testEmail() {
    const email = prompt('Test email gönderilecek adres:');
    if (email) {
        fetch('/settings/test-email', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({email: email})
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('✅ ' + data.message);
            } else {
                alert('❌ ' + data.message);
            }
        })
        .catch(error => {
            alert('❌ Hata: ' + error);
        });
    }
}

function showSmsTemplates() {
    alert('SMS şablonları özelliği yakında eklenecek...');
}

function showEmailTemplates() {
    alert('Email şablonları özelliği yakında eklenecek...');
}

// JSON alanları için syntax highlighting
$(document).ready(function() {
    $('textarea[name*="json"]').each(function() {
        try {
            const value = $(this).val();
            if (value) {
                const parsed = JSON.parse(value);
                $(this).val(JSON.stringify(parsed, null, 2));
            }
        } catch (e) {
            // JSON geçersizse olduğu gibi bırak
        }
    });
});

// Boolean switch'ler için label güncelleme
$('.form-check-input').change(function() {
    const label = $(this).next('.form-check-label');
    if ($(this).is(':checked')) {
        label.text('Aktif');
    } else {
        label.text('Pasif');
    }
});
</script>
{% endblock %}
