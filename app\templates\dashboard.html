{% extends "base.html" %}

{% block title %}Dashboard - Eğitim Yönetim Sistemi{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">Dashboard</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-secondary">
                <i class="fas fa-download"></i> <PERSON><PERSON>
            </button>
        </div>
    </div>
</div>

<!-- <PERSON><PERSON> geldin mesajı -->
<div class="alert alert-info">
    <h4><i class="fas fa-user"></i> <PERSON><PERSON> geldin<PERSON>, {{ current_user.tam_ad }}!</h4>
    <p class="mb-0">Rol: <strong>{{ current_user.rol.title() }}</strong></p>
</div>

<!-- İstatistik kartları -->
<div class="row mb-4">
    {% if current_user.can_manage_trainings() %}
    <!-- Yönetici/Admin için istatistikler -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            Toplam Eğitim
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ training_count or 0 }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-book fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            Aktif Katılımcı
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ participant_count or 0 }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-users fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            Toplam Quiz
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ quiz_count or 0 }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-question-circle fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            Bekleyen Teyit
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ pending_confirmations or 0 }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-clock fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% else %}
    <!-- Katılımcı için istatistikler -->
    <div class="col-xl-4 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            Kayıtlı Eğitimler
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ my_trainings_count or 0 }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-book fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-4 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            Tamamlanan Quiz
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ completed_quizzes or 0 }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-4 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            Ortalama Skor
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ average_score or 0 }}%
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-chart-line fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<!-- Grafiksel Raporlar -->
{% if current_user.can_manage_trainings() %}
<div class="row mb-4">
    <!-- Eğitim Trendleri -->
    <div class="col-lg-8">
        <div class="card shadow mb-4">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold ">
                    <i class="fas fa-chart-line"></i> Eğitim Trendleri (Son 6 Ay)
                </h6>
            </div>
            <div class="card-body">
                <canvas id="trainingTrendsChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>

    <!-- Katılım Durumu -->
    <div class="col-lg-4">
        <div class="card shadow mb-4">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold ">
                    <i class="fas fa-chart-pie"></i> Katılım Durumu
                </h6>
            </div>
            <div class="card-body">
                <canvas id="participationChart" width="400" height="400"></canvas>
            </div>
        </div>
    </div>
</div>

<div class="row mb-4">
    <!-- Quiz Başarı Oranları -->
    <div class="col-lg-6">
        <div class="card shadow mb-4">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold ">
                    <i class="fas fa-chart-bar"></i> Quiz Başarı Oranları
                </h6>
            </div>
            <div class="card-body">
                <canvas id="quizSuccessChart" width="400" height="300"></canvas>
            </div>
        </div>
    </div>

    <!-- Şirket Bazlı Katılım -->
    <div class="col-lg-6">
        <div class="card shadow mb-4">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold">
                    <i class="fas fa-building"></i> Şirket Bazlı Katılım
                </h6>
            </div>
            <div class="card-body">
                <canvas id="companyParticipationChart" width="400" height="300"></canvas>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Son aktiviteler -->
<div class="row">
    <div class="col-lg-8">
        <div class="card shadow mb-4">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold">
                    <i class="fas fa-clock"></i> Son Aktiviteler
                </h6>
            </div>
            <div class="card-body">
                {% if recent_activities %}
                <div class="list-group list-group-flush">
                    {% for activity in recent_activities %}
                    <div class="list-group-item">
                        <div class="d-flex w-100 justify-content-between">
                            <h6 class="mb-1">{{ activity.title }}</h6>
                            <small>{{ activity.date }}</small>
                        </div>
                        <p class="mb-1">{{ activity.description }}</p>
                        <small class="text-muted">{{ activity.type }}</small>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <p class="text-muted">Henüz aktivite bulunmuyor.</p>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card shadow mb-4">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold">
                    <i class="fas fa-calendar"></i> Yaklaşan Eğitimler
                </h6>
            </div>
            <div class="card-body">
                {% if upcoming_trainings %}
                {% for training in upcoming_trainings %}
                <div class="mb-3 p-2 border-left border-primary">
                    <h6 class="mb-1">{{ training.ad }}</h6>
                    <small class="text-muted">
                        <i class="fas fa-calendar-alt"></i> 
                        {{ training.baslangic_tarihi.strftime('%d.%m.%Y %H:%M') }}
                    </small>
                    <br>
                    <small class="text-muted">
                        <i class="fas fa-users"></i> 
                        {{ training.katilimci_sayisi }} katılımcı
                    </small>
                </div>
                {% endfor %}
                {% else %}
                <p class="text-muted">Yaklaşan eğitim bulunmuyor.</p>
                {% endif %}
            </div>
        </div>
        
        {% if current_user.can_manage_trainings() %}
        <div class="card shadow mb-4">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold">
                    <i class="fas fa-tools"></i> Hızlı İşlemler
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('training.create_training') }}" class="btn btn-primary btn-sm">
                        <i class="fas fa-plus"></i> Yeni Eğitim
                    </a>
                    {% if current_user.is_admin %}
                    <a href="{{ url_for('auth.register') }}" class="btn btn-success btn-sm">
                        <i class="fas fa-user-plus"></i> Yeni Kullanıcı
                    </a>
                    {% endif %}
                    <button type="button" class="btn btn-info btn-sm" onclick="sendSMSConfirmations()">
                        <i class="fas fa-sms"></i> SMS Gönder
                    </button>
                    <button type="button" class="btn btn-warning btn-sm" onclick="sendSurveyEmails()">
                        <i class="fas fa-envelope"></i> Anket Gönder
                    </button>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
{% if current_user.can_manage_trainings() %}
<script>
// Chart.js konfigürasyonu
Chart.defaults.font.family = 'Nunito Sans, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto';
Chart.defaults.color = '#858796';

// Sayfa yüklendiğinde grafikleri oluştur
document.addEventListener('DOMContentLoaded', function() {
    createTrainingTrendsChart();
    createParticipationChart();
    createQuizSuccessChart();
    createCompanyParticipationChart();
});

// Eğitim Trendleri Grafiği
function createTrainingTrendsChart() {
    const ctx = document.getElementById('trainingTrendsChart').getContext('2d');

    // Son 6 ayın verilerini simüle et
    const months = ['Ocak', 'Şubat', 'Mart', 'Nisan', 'Mayıs', 'Haziran'];
    const trainingData = [12, 19, 15, 25, 22, 30];
    const participantData = [65, 89, 72, 105, 98, 125];

    new Chart(ctx, {
        type: 'line',
        data: {
            labels: months,
            datasets: [{
                label: 'Eğitim Sayısı',
                data: trainingData,
                borderColor: '#4e73df',
                backgroundColor: 'rgba(78, 115, 223, 0.1)',
                borderWidth: 2,
                fill: true,
                tension: 0.3,
                yAxisID: 'y'
            }, {
                label: 'Katılımcı Sayısı',
                data: participantData,
                borderColor: '#1cc88a',
                backgroundColor: 'rgba(28, 200, 138, 0.1)',
                borderWidth: 2,
                fill: true,
                tension: 0.3,
                yAxisID: 'y1'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                }
            },
            scales: {
                y: {
                    type: 'linear',
                    display: true,
                    position: 'left',
                    title: {
                        display: true,
                        text: 'Eğitim Sayısı'
                    }
                },
                y1: {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    title: {
                        display: true,
                        text: 'Katılımcı Sayısı'
                    },
                    grid: {
                        drawOnChartArea: false,
                    },
                }
            }
        }
    });
}

// Katılım Durumu Pie Chart
function createParticipationChart() {
    const ctx = document.getElementById('participationChart').getContext('2d');

    // Gerçek verilerden katılım durumu
    const participationData = {
        {% if participation_stats %}
        {% for stat in participation_stats %}
        '{{ stat.katilim_teyidi_durumu }}': {{ stat.count }},
        {% endfor %}
        {% endif %}
    };

    const confirmed = participationData['teyit_edildi'] || 0;
    const pending = participationData['beklemede'] || 0;
    const rejected = participationData['reddedildi'] || 0;

    new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['Teyit Edildi', 'Beklemede', 'Reddedildi'],
            datasets: [{
                data: [confirmed, pending, rejected],
                backgroundColor: [
                    '#1cc88a',
                    '#f6c23e',
                    '#e74a3b'
                ],
                borderWidth: 2,
                borderColor: '#ffffff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 20,
                        usePointStyle: true
                    }
                }
            }
        }
    });
}

// Quiz Başarı Oranları Bar Chart
function createQuizSuccessChart() {
    const ctx = document.getElementById('quizSuccessChart').getContext('2d');

    // Gerçek verilerden quiz başarı oranları
    const quizLabels = [];
    const quizScores = [];

    {% if quiz_success %}
    {% for quiz in quiz_success %}
    quizLabels.push('{{ quiz.ad }}');
    quizScores.push({{ quiz.avg_score|round(1) }});
    {% endfor %}
    {% else %}
    // Varsayılan veriler
    quizLabels.push('İSG Temel', 'İSG İleri', 'Yangın Güvenliği', 'İlk Yardım');
    quizScores.push(85, 72, 90, 78);
    {% endif %}

    new Chart(ctx, {
        type: 'bar',
        data: {
            labels: quizLabels,
            datasets: [{
                label: 'Başarı Oranı (%)',
                data: quizScores,
                backgroundColor: [
                    'rgba(78, 115, 223, 0.8)',
                    'rgba(28, 200, 138, 0.8)',
                    'rgba(246, 194, 62, 0.8)',
                    'rgba(231, 74, 59, 0.8)',
                    'rgba(54, 162, 235, 0.8)'
                ],
                borderColor: [
                    '#4e73df',
                    '#1cc88a',
                    '#f6c23e',
                    '#e74a3b',
                    '#36a2eb'
                ],
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100,
                    title: {
                        display: true,
                        text: 'Başarı Oranı (%)'
                    }
                }
            }
        }
    });
}

// Şirket Bazlı Katılım Horizontal Bar Chart
function createCompanyParticipationChart() {
    const ctx = document.getElementById('companyParticipationChart').getContext('2d');

    // Gerçek verilerden şirket katılımları
    const companyLabels = [];
    const companyCounts = [];

    {% if company_participation %}
    {% for company in company_participation %}
    companyLabels.push('{{ company.ad }}');
    companyCounts.push({{ company.count }});
    {% endfor %}
    {% else %}
    // Varsayılan veriler
    companyLabels.push('BAYRAKTAR', 'Teknoloji A.Ş.', 'Endüstri Ltd.', 'Güvenlik Corp.', 'Diğer');
    companyCounts.push(45, 32, 28, 15, 12);
    {% endif %}

    new Chart(ctx, {
        type: 'bar',
        data: {
            labels: companyLabels,
            datasets: [{
                label: 'Katılımcı Sayısı',
                data: companyCounts,
                backgroundColor: 'rgba(78, 115, 223, 0.8)',
                borderColor: '#4e73df',
                borderWidth: 1
            }]
        },
        options: {
            indexAxis: 'y',
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                x: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Katılımcı Sayısı'
                    }
                }
            }
        }
    });
}

function sendSMSConfirmations() {
    if (confirm('Katılım teyidi SMS\'lerini göndermek istediğinizden emin misiniz?')) {
        fetch('/api/send-sms-confirmations', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('SMS\'ler başarıyla gönderildi: ' + data.message);
            } else {
                alert('Hata: ' + data.message);
            }
        })
        .catch(error => {
            alert('Bir hata oluştu: ' + error);
        });
    }
}

function sendSurveyEmails() {
    if (confirm('Anket e-postalarını göndermek istediğinizden emin misiniz?')) {
        fetch('/api/send-survey-emails', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('E-postalar başarıyla gönderildi: ' + data.message);
            } else {
                alert('Hata: ' + data.message);
            }
        })
        .catch(error => {
            alert('Bir hata oluştu: ' + error);
        });
    }
}
</script>
{% endif %}
{% endblock %}
