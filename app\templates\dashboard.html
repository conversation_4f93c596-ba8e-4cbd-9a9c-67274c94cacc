{% extends "base.html" %}

{% block title %}Dashboard - Eğitim Yönetim Sistemi{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">Dashboard</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-secondary">
                <i class="fas fa-download"></i> <PERSON><PERSON>
            </button>
        </div>
    </div>
</div>

<!-- <PERSON><PERSON> geldin mesajı -->
<div class="alert alert-info">
    <h4><i class="fas fa-user"></i> <PERSON><PERSON> geldin<PERSON>, {{ current_user.tam_ad }}!</h4>
    <p class="mb-0">Rol: <strong>{{ current_user.rol.title() }}</strong></p>
</div>

<!-- İstatistik kartları -->
<div class="row mb-4">
    {% if current_user.can_manage_trainings() %}
    <!-- Yönetici/Admin için istatistikler -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            Toplam Eğitim
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ training_count or 0 }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-book fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            Aktif Katılımcı
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ participant_count or 0 }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-users fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            Toplam Quiz
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ quiz_count or 0 }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-question-circle fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            Bekleyen Teyit
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ pending_confirmations or 0 }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-clock fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% else %}
    <!-- Katılımcı için istatistikler -->
    <div class="col-xl-4 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            Kayıtlı Eğitimler
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ my_trainings_count or 0 }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-book fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-4 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            Tamamlanan Quiz
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ completed_quizzes or 0 }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-4 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            Ortalama Skor
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ average_score or 0 }}%
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-chart-line fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<!-- Son aktiviteler -->
<div class="row">
    <div class="col-lg-8">
        <div class="card shadow mb-4">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold">
                    <i class="fas fa-clock"></i> Son Aktiviteler
                </h6>
            </div>
            <div class="card-body">
                {% if recent_activities %}
                <div class="list-group list-group-flush">
                    {% for activity in recent_activities %}
                    <div class="list-group-item">
                        <div class="d-flex w-100 justify-content-between">
                            <h6 class="mb-1">{{ activity.title }}</h6>
                            <small>{{ activity.date }}</small>
                        </div>
                        <p class="mb-1">{{ activity.description }}</p>
                        <small class="text-muted">{{ activity.type }}</small>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <p class="text-muted">Henüz aktivite bulunmuyor.</p>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card shadow mb-4">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold">
                    <i class="fas fa-calendar"></i> Yaklaşan Eğitimler
                </h6>
            </div>
            <div class="card-body">
                {% if upcoming_trainings %}
                {% for training in upcoming_trainings %}
                <div class="mb-3 p-2 border-left border-primary">
                    <h6 class="mb-1">{{ training.ad }}</h6>
                    <small class="text-muted">
                        <i class="fas fa-calendar-alt"></i> 
                        {{ training.baslangic_tarihi.strftime('%d.%m.%Y %H:%M') }}
                    </small>
                    <br>
                    <small class="text-muted">
                        <i class="fas fa-users"></i> 
                        {{ training.katilimci_sayisi }} katılımcı
                    </small>
                </div>
                {% endfor %}
                {% else %}
                <p class="text-muted">Yaklaşan eğitim bulunmuyor.</p>
                {% endif %}
            </div>
        </div>
        
        {% if current_user.can_manage_trainings() %}
        <div class="card shadow mb-4">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold">
                    <i class="fas fa-tools"></i> Hızlı İşlemler
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('training.create_training') }}" class="btn btn-primary btn-sm">
                        <i class="fas fa-plus"></i> Yeni Eğitim
                    </a>
                    {% if current_user.is_admin %}
                    <a href="{{ url_for('auth.register') }}" class="btn btn-success btn-sm">
                        <i class="fas fa-user-plus"></i> Yeni Kullanıcı
                    </a>
                    {% endif %}
                    <button type="button" class="btn btn-info btn-sm" onclick="sendSMSConfirmations()">
                        <i class="fas fa-sms"></i> SMS Gönder
                    </button>
                    <button type="button" class="btn btn-warning btn-sm" onclick="sendSurveyEmails()">
                        <i class="fas fa-envelope"></i> Anket Gönder
                    </button>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
{% if current_user.can_manage_trainings() %}
<script>
function sendSMSConfirmations() {
    if (confirm('Katılım teyidi SMS\'lerini göndermek istediğinizden emin misiniz?')) {
        fetch('/api/send-sms-confirmations', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('SMS\'ler başarıyla gönderildi: ' + data.message);
            } else {
                alert('Hata: ' + data.message);
            }
        })
        .catch(error => {
            alert('Bir hata oluştu: ' + error);
        });
    }
}

function sendSurveyEmails() {
    if (confirm('Anket e-postalarını göndermek istediğinizden emin misiniz?')) {
        fetch('/api/send-survey-emails', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('E-postalar başarıyla gönderildi: ' + data.message);
            } else {
                alert('Hata: ' + data.message);
            }
        })
        .catch(error => {
            alert('Bir hata oluştu: ' + error);
        });
    }
}
</script>
{% endif %}
{% endblock %}
