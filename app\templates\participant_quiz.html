{% extends "base.html" %}

{% block title %}{{ quiz.ad }} - Quiz{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0">
                            <i class="fas fa-question-circle"></i> {{ quiz.ad }}
                        </h4>
                        <div id="timer" class="h5 mb-0">
                            <i class="fas fa-clock"></i> <span id="timeLeft">30:00</span>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Quiz Bilgileri -->
                    <div class="alert alert-info">
                        <div class="row">
                            <div class="col-md-6">
                                <strong>Eğitim:</strong> {{ training.ad }}<br>
                                <strong>So<PERSON>:</strong> {{ quiz.soru_sayisi }}
                            </div>
                            <div class="col-md-6">
                                <strong>Geçer Not:</strong> {{ quiz.gecer_notu }}%<br>
                                {% if quiz.aciklama %}
                                <strong>Açıklama:</strong> {{ quiz.aciklama }}
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <!-- Quiz Formu -->
                    <form id="quizForm">
                        {% for question in quiz.questions %}
                        <div class="quiz-question" data-question="{{ loop.index0 }}">
                            <h6 class="mb-3">
                                <span class="badge bg-secondary me-2">{{ loop.index }}</span>
                                {{ question.question }}
                            </h6>
                            
                            <div class="options">
                                {% for option in question.options %}
                                <div class="quiz-option">
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" 
                                               name="question_{{ loop.index0 }}" 
                                               id="q{{ loop.index0 }}_opt{{ loop.index0 }}" 
                                               value="{{ option }}" required>
                                        <label class="form-check-label" for="q{{ loop.index0 }}_opt{{ loop.index0 }}">
                                            {{ option }}
                                        </label>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                        {% endfor %}
                        
                        <!-- Submit Buttons -->
                        <div class="d-flex justify-content-between mt-4">
                            <a href="{{ url_for('training.detail', training_id=training.id) }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Geri Dön
                            </a>
                            <button type="submit" class="btn btn-success btn-lg" id="submitBtn">
                                <i class="fas fa-check"></i> Quiz'i Tamamla
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Progress Bar -->
<div class="fixed-bottom bg-white border-top p-2">
    <div class="container-fluid">
        <div class="row align-items-center">
            <div class="col-md-8">
                <div class="progress">
                    <div class="progress-bar" role="progressbar" style="width: 0%" id="progressBar">
                        <span id="progressText">0 / {{ quiz.soru_sayisi }} soru yanıtlandı</span>
                    </div>
                </div>
            </div>
            <div class="col-md-4 text-end">
                <button type="button" class="btn btn-outline-primary btn-sm" onclick="checkAnswers()">
                    <i class="fas fa-check-circle"></i> Yanıtları Kontrol Et
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let timeLimit = 30 * 60; // 30 dakika (saniye cinsinden)
let timeLeft = timeLimit;
let timerInterval;
let totalQuestions = {{ quiz.soru_sayisi }};

$(document).ready(function() {
    startTimer();
    updateProgress();
    
    // Yanıt değişikliklerini dinle
    $('input[type="radio"]').on('change', function() {
        updateProgress();
    });
    
    // Form submit
    $('#quizForm').on('submit', function(e) {
        e.preventDefault();
        submitQuiz();
    });
    
    // Sayfa kapatılmaya çalışıldığında uyar
    window.addEventListener('beforeunload', function(e) {
        e.preventDefault();
        e.returnValue = 'Quiz devam ediyor. Sayfayı kapatmak istediğinizden emin misiniz?';
    });
});

function startTimer() {
    timerInterval = setInterval(function() {
        timeLeft--;
        updateTimerDisplay();
        
        if (timeLeft <= 0) {
            clearInterval(timerInterval);
            alert('Süre doldu! Quiz otomatik olarak gönderiliyor.');
            submitQuiz();
        }
    }, 1000);
}

function updateTimerDisplay() {
    let minutes = Math.floor(timeLeft / 60);
    let seconds = timeLeft % 60;
    
    $('#timeLeft').text(
        (minutes < 10 ? '0' : '') + minutes + ':' + 
        (seconds < 10 ? '0' : '') + seconds
    );
    
    // Son 5 dakikada kırmızı yap
    if (timeLeft <= 300) {
        $('#timer').addClass('text-danger');
    }
}

function updateProgress() {
    let answeredQuestions = $('input[type="radio"]:checked').length;
    let percentage = (answeredQuestions / totalQuestions) * 100;
    
    $('#progressBar').css('width', percentage + '%');
    $('#progressText').text(answeredQuestions + ' / ' + totalQuestions + ' soru yanıtlandı');
    
    if (answeredQuestions === totalQuestions) {
        $('#progressBar').removeClass('bg-primary').addClass('bg-success');
        $('#submitBtn').removeClass('btn-success').addClass('btn-primary').prop('disabled', false);
    } else {
        $('#progressBar').removeClass('bg-success').addClass('bg-primary');
        $('#submitBtn').removeClass('btn-primary').addClass('btn-success');
    }
}

function checkAnswers() {
    let unansweredQuestions = [];
    
    for (let i = 0; i < totalQuestions; i++) {
        if (!$(`input[name="question_${i}"]:checked`).length) {
            unansweredQuestions.push(i + 1);
        }
    }
    
    if (unansweredQuestions.length > 0) {
        alert(`Şu sorular yanıtlanmamış: ${unansweredQuestions.join(', ')}`);
        
        // İlk yanıtlanmamış soruya git
        let firstUnanswered = unansweredQuestions[0] - 1;
        $(`[data-question="${firstUnanswered}"]`)[0].scrollIntoView({
            behavior: 'smooth',
            block: 'center'
        });
    } else {
        alert('Tüm sorular yanıtlandı! Quiz\'i gönderebilirsiniz.');
    }
}

function submitQuiz() {
    // Tüm sorular yanıtlandı mı kontrol et
    let answeredQuestions = $('input[type="radio"]:checked').length;
    
    if (answeredQuestions < totalQuestions) {
        if (!confirm(`${totalQuestions - answeredQuestions} soru yanıtlanmamış. Yine de göndermek istediğinizden emin misiniz?`)) {
            return;
        }
    }
    
    // Timer'ı durdur
    clearInterval(timerInterval);
    
    // Form verilerini topla
    let formData = new FormData($('#quizForm')[0]);
    
    // Submit butonunu deaktive et
    $('#submitBtn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Gönderiliyor...');
    
    // AJAX ile gönder
    fetch(`/quiz/{{ quiz.id }}/submit`, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Sayfa kapatma uyarısını kaldır
            window.removeEventListener('beforeunload', function() {});
            
            alert(`Quiz tamamlandı!\nSkorunuz: ${data.score.toFixed(1)}%\n${data.passing ? 'Tebrikler, geçtiniz!' : 'Maalesef geçemediniz.'}`);
            
            // Sonuç sayfasına yönlendir
            window.location.href = data.redirect_url;
        } else {
            alert('Hata: ' + data.message);
            $('#submitBtn').prop('disabled', false).html('<i class="fas fa-check"></i> Quiz\'i Tamamla');
        }
    })
    .catch(error => {
        alert('Bir hata oluştu: ' + error);
        $('#submitBtn').prop('disabled', false).html('<i class="fas fa-check"></i> Quiz\'i Tamamla');
    });
}

// Klavye kısayolları
$(document).on('keydown', function(e) {
    // Ctrl + Enter ile gönder
    if (e.ctrlKey && e.key === 'Enter') {
        e.preventDefault();
        submitQuiz();
    }
    
    // Sayı tuşları ile seçenek seçimi (1-9)
    if (e.key >= '1' && e.key <= '9') {
        let questionIndex = parseInt(e.key) - 1;
        let currentQuestion = $('.quiz-question').eq(questionIndex);
        if (currentQuestion.length) {
            let firstOption = currentQuestion.find('input[type="radio"]').first();
            if (firstOption.length) {
                firstOption.prop('checked', true).trigger('change');
            }
        }
    }
});
</script>
{% endblock %}
