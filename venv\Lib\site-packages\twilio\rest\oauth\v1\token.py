r"""
    This code was generated by
   ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
    |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
    |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

    Twilio - Oauth
    This is the public Twilio REST API.

    NOTE: This class is auto generated by OpenAPI Generator.
    https://openapi-generator.tech
    Do not edit the class manually.
"""


from datetime import datetime
from typing import Any, Dict, Optional, Union
from twilio.base import deserialize, values

from twilio.base.instance_resource import InstanceResource
from twilio.base.list_resource import ListResource
from twilio.base.version import Version


class TokenInstance(InstanceResource):

    """
    :ivar access_token: Token which carries the necessary information to access a Twilio resource directly.
    :ivar refresh_token: Token which carries the information necessary to get a new access token.
    :ivar id_token:
    :ivar refresh_token_expires_at: The date and time in GMT when the refresh token expires in [RFC 2822](https://www.ietf.org/rfc/rfc2822.txt) format.
    :ivar access_token_expires_at: The date and time in GMT when the refresh token expires in [RFC 2822](https://www.ietf.org/rfc/rfc2822.txt) format.
    """

    def __init__(self, version: Version, payload: Dict[str, Any]):
        super().__init__(version)

        self.access_token: Optional[str] = payload.get("access_token")
        self.refresh_token: Optional[str] = payload.get("refresh_token")
        self.id_token: Optional[str] = payload.get("id_token")
        self.refresh_token_expires_at: Optional[
            datetime
        ] = deserialize.iso8601_datetime(payload.get("refresh_token_expires_at"))
        self.access_token_expires_at: Optional[datetime] = deserialize.iso8601_datetime(
            payload.get("access_token_expires_at")
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """

        return "<Twilio.Oauth.V1.TokenInstance>"


class TokenList(ListResource):
    def __init__(self, version: Version):
        """
        Initialize the TokenList

        :param version: Version that contains the resource

        """
        super().__init__(version)

        self._uri = "/token"

    def create(
        self,
        grant_type: str,
        client_sid: str,
        client_secret: Union[str, object] = values.unset,
        code: Union[str, object] = values.unset,
        code_verifier: Union[str, object] = values.unset,
        device_code: Union[str, object] = values.unset,
        refresh_token: Union[str, object] = values.unset,
        device_id: Union[str, object] = values.unset,
    ) -> TokenInstance:
        """
        Create the TokenInstance

        :param grant_type: Grant type is a credential representing resource owner's authorization which can be used by client to obtain access token.
        :param client_sid: A 34 character string that uniquely identifies this OAuth App.
        :param client_secret: The credential for confidential OAuth App.
        :param code: JWT token related to the authorization code grant type.
        :param code_verifier: A code which is generation cryptographically.
        :param device_code: JWT token related to the device code grant type.
        :param refresh_token: JWT token related to the refresh token grant type.
        :param device_id: The Id of the device associated with the token (refresh token).

        :returns: The created TokenInstance
        """
        data = values.of(
            {
                "GrantType": grant_type,
                "ClientSid": client_sid,
                "ClientSecret": client_secret,
                "Code": code,
                "CodeVerifier": code_verifier,
                "DeviceCode": device_code,
                "RefreshToken": refresh_token,
                "DeviceId": device_id,
            }
        )

        payload = self._version.create(
            method="POST",
            uri=self._uri,
            data=data,
        )

        return TokenInstance(self._version, payload)

    async def create_async(
        self,
        grant_type: str,
        client_sid: str,
        client_secret: Union[str, object] = values.unset,
        code: Union[str, object] = values.unset,
        code_verifier: Union[str, object] = values.unset,
        device_code: Union[str, object] = values.unset,
        refresh_token: Union[str, object] = values.unset,
        device_id: Union[str, object] = values.unset,
    ) -> TokenInstance:
        """
        Asynchronously create the TokenInstance

        :param grant_type: Grant type is a credential representing resource owner's authorization which can be used by client to obtain access token.
        :param client_sid: A 34 character string that uniquely identifies this OAuth App.
        :param client_secret: The credential for confidential OAuth App.
        :param code: JWT token related to the authorization code grant type.
        :param code_verifier: A code which is generation cryptographically.
        :param device_code: JWT token related to the device code grant type.
        :param refresh_token: JWT token related to the refresh token grant type.
        :param device_id: The Id of the device associated with the token (refresh token).

        :returns: The created TokenInstance
        """
        data = values.of(
            {
                "GrantType": grant_type,
                "ClientSid": client_sid,
                "ClientSecret": client_secret,
                "Code": code,
                "CodeVerifier": code_verifier,
                "DeviceCode": device_code,
                "RefreshToken": refresh_token,
                "DeviceId": device_id,
            }
        )

        payload = await self._version.create_async(
            method="POST",
            uri=self._uri,
            data=data,
        )

        return TokenInstance(self._version, payload)

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Oauth.V1.TokenList>"
