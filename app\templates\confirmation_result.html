{% extends "base.html" %}

{% block title %}Katılım Teyidi - Eğitim Yönetim Sistemi{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card shadow">
                <div class="card-header {{ 'bg-success' if not already_confirmed else 'bg-info' }} text-white text-center">
                    <h4 class="mb-0">
                        <i class="fas {{ 'fa-check-circle' if not already_confirmed else 'fa-info-circle' }}"></i> 
                        Katılım Teyidi
                    </h4>
                </div>
                <div class="card-body text-center">
                    <div class="mb-4">
                        <i class="fas {{ 'fa-check-circle' if not already_confirmed else 'fa-info-circle' }} fa-5x {{ 'text-success' if not already_confirmed else 'text-info' }}"></i>
                    </div>
                    
                    <h5 class="mb-3">
                        {% if already_confirmed %}
                        Zaten Teyit Edilmiş
                        {% else %}
                        Teyit Başarılı!
                        {% endif %}
                    </h5>
                    
                    <p class="text-muted mb-4">{{ message }}</p>
                    
                    {% if training %}
                    <div class="card bg-light mb-4">
                        <div class="card-body">
                            <h6 class="card-title">{{ training.ad }}</h6>
                            <p class="card-text">
                                <i class="fas fa-calendar-alt"></i> 
                                {{ training.baslangic_tarihi.strftime('%d.%m.%Y %H:%M') }} - 
                                {{ training.bitis_tarihi.strftime('%d.%m.%Y %H:%M') }}
                            </p>
                            {% if training.aciklama %}
                            <p class="card-text">{{ training.aciklama }}</p>
                            {% endif %}
                        </div>
                    </div>
                    {% endif %}
                    
                    {% if user %}
                    <p class="mb-4">
                        <strong>Katılımcı:</strong> {{ user.tam_ad }}<br>
                        <strong>E-posta:</strong> {{ user.email }}
                    </p>
                    {% endif %}
                    
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        {% if not already_confirmed %}
                        Katılımınız başarıyla teyit edildi. Eğitim tarihi yaklaştığında size bilgilendirme yapılacaktır.
                        {% else %}
                        Katılımınız daha önce teyit edilmişti. Herhangi bir işlem yapmanıza gerek yoktur.
                        {% endif %}
                    </div>
                    
                    <div class="d-grid">
                        <a href="{{ url_for('index') }}" class="btn btn-primary">
                            <i class="fas fa-home"></i> Ana Sayfa
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
