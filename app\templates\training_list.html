{% extends "base.html" %}

{% block title %}Eğitimler - Eğitim Yönetim Sistemi{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-book"></i> 
        {% if current_user.can_manage_trainings() %}Tüm Eğitimler{% else %}Eğitimlerim{% endif %}
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        {% if current_user.can_manage_trainings() %}
        <a href="{{ url_for('training.create_training') }}" class="btn btn-primary">
            <i class="fas fa-plus"></i> Yeni Eğitim
        </a>
        {% endif %}
    </div>
</div>

<!-- Filtreler -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-3">
                        <label for="durum" class="form-label">Durum</label>
                        <select class="form-select" id="durum" name="durum">
                            <option value="">Tümü</option>
                            <option value="planlanan">Planlanan</option>
                            <option value="devam_eden">Devam Eden</option>
                            <option value="tamamlanan">Tamamlanan</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="arama" class="form-label">Arama</label>
                        <input type="text" class="form-control" id="arama" name="arama" placeholder="Eğitim adı...">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">&nbsp;</label>
                        <div>
                            <button type="submit" class="btn btn-outline-primary">
                                <i class="fas fa-search"></i> Filtrele
                            </button>
                            <a href="{{ url_for('training.list_trainings') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-times"></i> Temizle
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Eğitim Listesi -->
<div class="row">
    {% if trainings.items %}
    {% for training in trainings.items %}
    <div class="col-lg-6 mb-4">
        <div class="card shadow h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="mb-0">{{ training.ad }}</h6>
                <span class="badge 
                    {% if training.durum == 'planlanan' %}bg-info
                    {% elif training.durum == 'devam_eden' %}bg-success
                    {% else %}bg-secondary{% endif %}">
                    {% if training.durum == 'planlanan' %}Planlanan
                    {% elif training.durum == 'devam_eden' %}Devam Eden
                    {% else %}Tamamlanan{% endif %}
                </span>
            </div>
            <div class="card-body">
                {% if training.aciklama %}
                <p class="card-text">{{ training.aciklama[:100] }}{% if training.aciklama|length > 100 %}...{% endif %}</p>
                {% endif %}
                
                <div class="row text-center mb-3">
                    <div class="col-3">
                        <small class="text-muted">Katılımcı</small>
                        <div class="fw-bold">{{ training.katilimci_sayisi }}</div>
                    </div>
                    <div class="col-3">
                        <small class="text-muted">Teyit Eden</small>
                        <div class="fw-bold">{{ training.teyit_eden_sayisi }}</div>
                    </div>
                    <div class="col-3">
                        <small class="text-muted">Materyal</small>
                        <div class="fw-bold">{{ training.materyal_sayisi }}</div>
                    </div>
                    <div class="col-3">
                        <small class="text-muted">Teyit Oranı</small>
                        <div class="fw-bold">{{ "%.1f"|format(training.teyit_orani) }}%</div>
                    </div>
                </div>
                
                <div class="mb-2">
                    <small class="text-muted">
                        <i class="fas fa-calendar-alt"></i> 
                        {{ training.baslangic_tarihi.strftime('%d.%m.%Y %H:%M') }} - 
                        {{ training.bitis_tarihi.strftime('%d.%m.%Y %H:%M') }}
                    </small>
                </div>
                
                <!-- Eğitmen Bilgisi -->
                <div class="mb-2">
                    <small class="text-muted">
                        <i class="fas fa-chalkboard-teacher"></i>
                        Eğitmen: {{ training.egitmen_bilgisi.ad }}
                        {% if training.egitmen_bilgisi.tip == 'sistem_kullanicisi' %}
                        <span class="badge bg-primary ms-1">Sistem</span>
                        {% else %}
                        <span class="badge bg-secondary ms-1">Dış</span>
                        {% endif %}
                    </small>
                </div>

                <!-- Eğitim Onay Durumu -->
                <div class="mb-2">
                    <small class="text-muted">
                        <i class="fas fa-{{ 'check-circle' if training.egitim_verildi_mi else 'clock' }}"></i>
                        {% if training.egitim_verildi_mi %}
                        <span class="text-success">Eğitim Onaylandı</span>
                        {% if training.egitmen_onay_tarihi %}
                        ({{ training.egitmen_onay_tarihi.strftime('%d.%m.%Y %H:%M') }})
                        {% endif %}
                        {% else %}
                        <span class="text-warning">Eğitmen Onayı Bekleniyor</span>
                        {% endif %}
                    </small>
                </div>

                {% if current_user.can_manage_trainings() %}
                <div class="mb-2">
                    <small class="text-muted">
                        <i class="fas fa-user"></i>
                        Oluşturan: {{ training.olusturan.tam_ad }}
                    </small>
                </div>
                {% endif %}
            </div>
            <div class="card-footer">
                <div class="d-flex justify-content-between">
                    <a href="{{ url_for('training.detail', training_id=training.id) }}" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-eye"></i> Detay
                    </a>
                    {% if current_user.can_manage_trainings() %}
                    <div>
                        <a href="{{ url_for('training.edit_training', training_id=training.id) }}" class="btn btn-outline-warning btn-sm">
                            <i class="fas fa-edit"></i> Düzenle
                        </a>
                        <a href="{{ url_for('training.manage_participants', training_id=training.id) }}" class="btn btn-outline-info btn-sm">
                            <i class="fas fa-users"></i> Katılımcılar
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    {% endfor %}
    {% else %}
    <div class="col-12">
        <div class="alert alert-info text-center">
            <i class="fas fa-info-circle"></i> 
            {% if current_user.can_manage_trainings() %}
            Henüz eğitim oluşturulmamış. <a href="{{ url_for('training.create_training') }}">İlk eğitimi oluşturun</a>.
            {% else %}
            Henüz hiçbir eğitime kayıtlı değilsiniz.
            {% endif %}
        </div>
    </div>
    {% endif %}
</div>

<!-- Pagination -->
{% if trainings.pages > 1 %}
<nav aria-label="Eğitim listesi sayfalama">
    <ul class="pagination justify-content-center">
        {% if trainings.has_prev %}
        <li class="page-item">
            <a class="page-link" href="{{ url_for('training.list_trainings', page=trainings.prev_num) }}">Önceki</a>
        </li>
        {% endif %}
        
        {% for page_num in trainings.iter_pages() %}
            {% if page_num %}
                {% if page_num != trainings.page %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('training.list_trainings', page=page_num) }}">{{ page_num }}</a>
                </li>
                {% else %}
                <li class="page-item active">
                    <span class="page-link">{{ page_num }}</span>
                </li>
                {% endif %}
            {% else %}
            <li class="page-item disabled">
                <span class="page-link">…</span>
            </li>
            {% endif %}
        {% endfor %}
        
        {% if trainings.has_next %}
        <li class="page-item">
            <a class="page-link" href="{{ url_for('training.list_trainings', page=trainings.next_num) }}">Sonraki</a>
        </li>
        {% endif %}
    </ul>
</nav>
{% endif %}
{% endblock %}
