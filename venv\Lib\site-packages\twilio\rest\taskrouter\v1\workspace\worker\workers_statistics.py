r"""
    This code was generated by
   ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
    |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
    |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

    Twilio - Taskrouter
    This is the public Twilio REST API.

    NOTE: This class is auto generated by OpenAPI Generator.
    https://openapi-generator.tech
    Do not edit the class manually.
"""


from datetime import datetime
from typing import Any, Dict, Optional, Union
from twilio.base import serialize, values
from twilio.base.instance_context import InstanceContext
from twilio.base.instance_resource import InstanceResource
from twilio.base.list_resource import ListResource
from twilio.base.version import Version


class WorkersStatisticsInstance(InstanceResource):

    """
    :ivar realtime: An object that contains the real-time statistics for the Worker.
    :ivar cumulative: An object that contains the cumulative statistics for the Worker.
    :ivar account_sid: The SID of the [Account](https://www.twilio.com/docs/iam/api/account) that created the Worker resource.
    :ivar workspace_sid: The SID of the Workspace that contains the Worker.
    :ivar url: The absolute URL of the Worker statistics resource.
    """

    def __init__(self, version: Version, payload: Dict[str, Any], workspace_sid: str):
        super().__init__(version)

        self.realtime: Optional[Dict[str, object]] = payload.get("realtime")
        self.cumulative: Optional[Dict[str, object]] = payload.get("cumulative")
        self.account_sid: Optional[str] = payload.get("account_sid")
        self.workspace_sid: Optional[str] = payload.get("workspace_sid")
        self.url: Optional[str] = payload.get("url")

        self._solution = {
            "workspace_sid": workspace_sid,
        }
        self._context: Optional[WorkersStatisticsContext] = None

    @property
    def _proxy(self) -> "WorkersStatisticsContext":
        """
        Generate an instance context for the instance, the context is capable of
        performing various actions. All instance actions are proxied to the context

        :returns: WorkersStatisticsContext for this WorkersStatisticsInstance
        """
        if self._context is None:
            self._context = WorkersStatisticsContext(
                self._version,
                workspace_sid=self._solution["workspace_sid"],
            )
        return self._context

    def fetch(
        self,
        minutes: Union[int, object] = values.unset,
        start_date: Union[datetime, object] = values.unset,
        end_date: Union[datetime, object] = values.unset,
        task_queue_sid: Union[str, object] = values.unset,
        task_queue_name: Union[str, object] = values.unset,
        friendly_name: Union[str, object] = values.unset,
        task_channel: Union[str, object] = values.unset,
    ) -> "WorkersStatisticsInstance":
        """
        Fetch the WorkersStatisticsInstance

        :param minutes: Only calculate statistics since this many minutes in the past. The default 15 minutes. This is helpful for displaying statistics for the last 15 minutes, 240 minutes (4 hours), and 480 minutes (8 hours) to see trends.
        :param start_date: Only calculate statistics from this date and time and later, specified in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format.
        :param end_date: Only calculate statistics from this date and time and earlier, specified in GMT as an [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) date-time.
        :param task_queue_sid: The SID of the TaskQueue for which to fetch Worker statistics.
        :param task_queue_name: The `friendly_name` of the TaskQueue for which to fetch Worker statistics.
        :param friendly_name: Only include Workers with `friendly_name` values that match this parameter.
        :param task_channel: Only calculate statistics on this TaskChannel. Can be the TaskChannel's SID or its `unique_name`, such as `voice`, `sms`, or `default`.

        :returns: The fetched WorkersStatisticsInstance
        """
        return self._proxy.fetch(
            minutes=minutes,
            start_date=start_date,
            end_date=end_date,
            task_queue_sid=task_queue_sid,
            task_queue_name=task_queue_name,
            friendly_name=friendly_name,
            task_channel=task_channel,
        )

    async def fetch_async(
        self,
        minutes: Union[int, object] = values.unset,
        start_date: Union[datetime, object] = values.unset,
        end_date: Union[datetime, object] = values.unset,
        task_queue_sid: Union[str, object] = values.unset,
        task_queue_name: Union[str, object] = values.unset,
        friendly_name: Union[str, object] = values.unset,
        task_channel: Union[str, object] = values.unset,
    ) -> "WorkersStatisticsInstance":
        """
        Asynchronous coroutine to fetch the WorkersStatisticsInstance

        :param minutes: Only calculate statistics since this many minutes in the past. The default 15 minutes. This is helpful for displaying statistics for the last 15 minutes, 240 minutes (4 hours), and 480 minutes (8 hours) to see trends.
        :param start_date: Only calculate statistics from this date and time and later, specified in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format.
        :param end_date: Only calculate statistics from this date and time and earlier, specified in GMT as an [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) date-time.
        :param task_queue_sid: The SID of the TaskQueue for which to fetch Worker statistics.
        :param task_queue_name: The `friendly_name` of the TaskQueue for which to fetch Worker statistics.
        :param friendly_name: Only include Workers with `friendly_name` values that match this parameter.
        :param task_channel: Only calculate statistics on this TaskChannel. Can be the TaskChannel's SID or its `unique_name`, such as `voice`, `sms`, or `default`.

        :returns: The fetched WorkersStatisticsInstance
        """
        return await self._proxy.fetch_async(
            minutes=minutes,
            start_date=start_date,
            end_date=end_date,
            task_queue_sid=task_queue_sid,
            task_queue_name=task_queue_name,
            friendly_name=friendly_name,
            task_channel=task_channel,
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Taskrouter.V1.WorkersStatisticsInstance {}>".format(context)


class WorkersStatisticsContext(InstanceContext):
    def __init__(self, version: Version, workspace_sid: str):
        """
        Initialize the WorkersStatisticsContext

        :param version: Version that contains the resource
        :param workspace_sid: The SID of the Workspace with the Worker to fetch.
        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "workspace_sid": workspace_sid,
        }
        self._uri = "/Workspaces/{workspace_sid}/Workers/Statistics".format(
            **self._solution
        )

    def fetch(
        self,
        minutes: Union[int, object] = values.unset,
        start_date: Union[datetime, object] = values.unset,
        end_date: Union[datetime, object] = values.unset,
        task_queue_sid: Union[str, object] = values.unset,
        task_queue_name: Union[str, object] = values.unset,
        friendly_name: Union[str, object] = values.unset,
        task_channel: Union[str, object] = values.unset,
    ) -> WorkersStatisticsInstance:
        """
        Fetch the WorkersStatisticsInstance

        :param minutes: Only calculate statistics since this many minutes in the past. The default 15 minutes. This is helpful for displaying statistics for the last 15 minutes, 240 minutes (4 hours), and 480 minutes (8 hours) to see trends.
        :param start_date: Only calculate statistics from this date and time and later, specified in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format.
        :param end_date: Only calculate statistics from this date and time and earlier, specified in GMT as an [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) date-time.
        :param task_queue_sid: The SID of the TaskQueue for which to fetch Worker statistics.
        :param task_queue_name: The `friendly_name` of the TaskQueue for which to fetch Worker statistics.
        :param friendly_name: Only include Workers with `friendly_name` values that match this parameter.
        :param task_channel: Only calculate statistics on this TaskChannel. Can be the TaskChannel's SID or its `unique_name`, such as `voice`, `sms`, or `default`.

        :returns: The fetched WorkersStatisticsInstance
        """

        data = values.of(
            {
                "Minutes": minutes,
                "StartDate": serialize.iso8601_datetime(start_date),
                "EndDate": serialize.iso8601_datetime(end_date),
                "TaskQueueSid": task_queue_sid,
                "TaskQueueName": task_queue_name,
                "FriendlyName": friendly_name,
                "TaskChannel": task_channel,
            }
        )

        payload = self._version.fetch(method="GET", uri=self._uri, params=data)

        return WorkersStatisticsInstance(
            self._version,
            payload,
            workspace_sid=self._solution["workspace_sid"],
        )

    async def fetch_async(
        self,
        minutes: Union[int, object] = values.unset,
        start_date: Union[datetime, object] = values.unset,
        end_date: Union[datetime, object] = values.unset,
        task_queue_sid: Union[str, object] = values.unset,
        task_queue_name: Union[str, object] = values.unset,
        friendly_name: Union[str, object] = values.unset,
        task_channel: Union[str, object] = values.unset,
    ) -> WorkersStatisticsInstance:
        """
        Asynchronous coroutine to fetch the WorkersStatisticsInstance

        :param minutes: Only calculate statistics since this many minutes in the past. The default 15 minutes. This is helpful for displaying statistics for the last 15 minutes, 240 minutes (4 hours), and 480 minutes (8 hours) to see trends.
        :param start_date: Only calculate statistics from this date and time and later, specified in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format.
        :param end_date: Only calculate statistics from this date and time and earlier, specified in GMT as an [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) date-time.
        :param task_queue_sid: The SID of the TaskQueue for which to fetch Worker statistics.
        :param task_queue_name: The `friendly_name` of the TaskQueue for which to fetch Worker statistics.
        :param friendly_name: Only include Workers with `friendly_name` values that match this parameter.
        :param task_channel: Only calculate statistics on this TaskChannel. Can be the TaskChannel's SID or its `unique_name`, such as `voice`, `sms`, or `default`.

        :returns: The fetched WorkersStatisticsInstance
        """

        data = values.of(
            {
                "Minutes": minutes,
                "StartDate": serialize.iso8601_datetime(start_date),
                "EndDate": serialize.iso8601_datetime(end_date),
                "TaskQueueSid": task_queue_sid,
                "TaskQueueName": task_queue_name,
                "FriendlyName": friendly_name,
                "TaskChannel": task_channel,
            }
        )

        payload = await self._version.fetch_async(
            method="GET", uri=self._uri, params=data
        )

        return WorkersStatisticsInstance(
            self._version,
            payload,
            workspace_sid=self._solution["workspace_sid"],
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Taskrouter.V1.WorkersStatisticsContext {}>".format(context)


class WorkersStatisticsList(ListResource):
    def __init__(self, version: Version, workspace_sid: str):
        """
        Initialize the WorkersStatisticsList

        :param version: Version that contains the resource
        :param workspace_sid: The SID of the Workspace with the Worker to fetch.

        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "workspace_sid": workspace_sid,
        }

    def get(self) -> WorkersStatisticsContext:
        """
        Constructs a WorkersStatisticsContext

        """
        return WorkersStatisticsContext(
            self._version, workspace_sid=self._solution["workspace_sid"]
        )

    def __call__(self) -> WorkersStatisticsContext:
        """
        Constructs a WorkersStatisticsContext

        """
        return WorkersStatisticsContext(
            self._version, workspace_sid=self._solution["workspace_sid"]
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Taskrouter.V1.WorkersStatisticsList>"
