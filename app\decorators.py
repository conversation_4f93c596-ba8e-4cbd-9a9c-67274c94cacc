from functools import wraps
from flask import abort, flash, redirect, url_for
from flask_login import current_user


def admin_required(f):
    """Admin yetkisi gerektiren fonksiyonlar için decorator"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated:
            flash('Bu sayfaya erişmek için giriş yapmalısınız.', 'error')
            return redirect(url_for('auth.login'))
        
        if not current_user.is_admin:
            flash('Bu sayfaya erişim yetkiniz yok.', 'error')
            abort(403)
        
        return f(*args, **kwargs)
    return decorated_function


def manager_required(f):
    """Manager veya Admin yetkisi gerektiren fonksiyonlar için decorator"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated:
            flash('Bu sayfaya erişmek için giriş yapmalısınız.', 'error')
            return redirect(url_for('auth.login'))
        
        if not current_user.can_manage_trainings():
            flash('Bu sayfaya erişim yetkiniz yok.', 'error')
            abort(403)
        
        return f(*args, **kwargs)
    return decorated_function


def instructor_required(f):
    """Eğitmen yetkisi gerektiren fonksiyonlar için decorator"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated:
            flash('Bu sayfaya erişmek için giriş yapmalısınız.', 'error')
            return redirect(url_for('auth.login'))
        
        if current_user.rol not in ['admin', 'manager', 'instructor']:
            flash('Bu sayfaya erişim yetkiniz yok.', 'error')
            abort(403)
        
        return f(*args, **kwargs)
    return decorated_function
