from datetime import datetime
from flask_sqlalchemy import SQLAlchemy
from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash
from app import db

class User(UserMixin, db.Model):
    """Kullanıcı modeli"""
    __tablename__ = 'users'
    
    id = db.Column(db.Integer, primary_key=True)
    ad = db.Column(db.String(100), nullable=False)
    soyad = db.Column(db.String(100), nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False, index=True)
    telefon_numarasi = db.Column(db.String(20), nullable=False)
    password_hash = db.Column(db.String(255), nullable=False)
    rol = db.Column(db.Enum('participant', 'manager', 'admin', 'instructor', name='user_roles'),
                   nullable=False, default='participant')
    aktif_mi = db.Column(db.<PERSON>, default=True, nullable=False)
    olusturulma_tarihi = db.Column(db.DateTime, default=datetime.utcnow)
    son_giris_tarihi = db.Column(db.DateTime)

    # Yönetici ilişkisi (katılımcılar için)
    manager_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)

    # Şirket ilişkisi
    company_id = db.Column(db.Integer, db.ForeignKey('companies.id'), nullable=True)

    # İlişkiler
    enrollments = db.relationship('Enrollment', backref='user', lazy='dynamic', cascade='all, delete-orphan')
    quiz_attempts = db.relationship('QuizAttempt', backref='user', lazy='dynamic', cascade='all, delete-orphan')
    survey_responses = db.relationship('SurveyResponse', backref='manager', lazy='dynamic', cascade='all, delete-orphan')
    communications = db.relationship('CommunicationLog', backref='recipient', lazy='dynamic', cascade='all, delete-orphan')

    # Yönetici-katılımcı ilişkileri
    manager = db.relationship('User', remote_side=[id], backref='team_members')  # Yöneticisi
    # team_members: Bu kullanıcının yönettiği ekip üyeleri (backref ile otomatik oluşur)
    
    def __init__(self, **kwargs):
        super(User, self).__init__(**kwargs)
    
    def set_password(self, password):
        """Şifreyi hash'leyerek kaydet"""
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        """Şifreyi kontrol et"""
        return check_password_hash(self.password_hash, password)
    
    @property
    def tam_ad(self):
        """Tam adı döndür"""
        return f"{self.ad} {self.soyad}"
    
    @property
    def is_admin(self):
        """Admin mi kontrol et"""
        return self.rol == 'admin'
    
    @property
    def is_manager(self):
        """Manager mi kontrol et"""
        return self.rol == 'manager'
    
    @property
    def is_participant(self):
        """Participant mi kontrol et"""
        return self.rol == 'participant'

    @property
    def is_instructor(self):
        """Instructor mi kontrol et"""
        return self.rol == 'instructor'
    
    def can_manage_trainings(self):
        """Eğitim yönetebilir mi"""
        return self.rol in ['admin', 'manager']
    
    def can_create_quizzes(self):
        """Quiz oluşturabilir mi"""
        return self.rol in ['admin', 'manager']
    
    def get_active_enrollments(self):
        """Aktif eğitim kayıtlarını getir"""
        return self.enrollments.filter_by(aktif_mi=True).all()
    
    def get_completed_quizzes(self):
        """Tamamlanan quizleri getir"""
        return self.quiz_attempts.filter_by(tamamlandi_mi=True).all()

    def get_team_members(self):
        """Yönettiği ekip üyelerini getir (sadece aktif olanlar)"""
        return self.team_members.filter_by(aktif_mi=True).all()

    def get_active_team_members(self):
        """Aktif ekip üyelerini getir"""
        return [member for member in self.team_members if member.aktif_mi]

    def has_manager(self):
        """Yöneticisi var mı?"""
        return self.manager_id is not None

    def get_manager_name(self):
        """Yöneticisinin adını getir"""
        return self.manager.tam_ad if self.manager else "Yönetici Atanmamış"

    def can_manage_user(self, user):
        """Bu kullanıcıyı yönetebilir mi?"""
        if self.is_admin:
            return True
        if self.is_manager and user.manager_id == self.id:
            return True
        return False

    def can_manage_trainings(self):
        """Eğitim yönetebilir mi?"""
        return self.is_admin or self.is_manager
    
    def __repr__(self):
        return f'<User {self.email}>'
    
    def to_dict(self):
        """Kullanıcı bilgilerini dictionary olarak döndür"""
        return {
            'id': self.id,
            'ad': self.ad,
            'soyad': self.soyad,
            'email': self.email,
            'telefon_numarasi': self.telefon_numarasi,
            'rol': self.rol,
            'aktif_mi': self.aktif_mi,
            'tam_ad': self.tam_ad,
            'manager_id': self.manager_id,
            'manager_name': self.get_manager_name(),
            'company_id': self.company_id,
            'company': {
                'id': self.company.id,
                'kod': self.company.kod,
                'ad': self.company.ad,
                'tam_ad': self.company.tam_ad
            } if self.company else None,
            'team_size': len(self.get_active_team_members()) if self.is_manager else 0,
            'olusturulma_tarihi': self.olusturulma_tarihi.strftime('%d.%m.%Y %H:%M') if self.olusturulma_tarihi else None,
            'son_giris_tarihi': self.son_giris_tarihi.strftime('%d.%m.%Y %H:%M') if self.son_giris_tarihi else None
        }
