#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""Şirket yönetimi controller'ı"""

from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from app import db
from app.models.company import Company
from app.models.user import User

company_bp = Blueprint('company', __name__, url_prefix='/company')

@company_bp.route('/')
@login_required
def companies():
    """Şirket listesi"""
    if not current_user.is_admin:
        flash('Bu sayfaya erişim yetkiniz yok.', 'error')
        return redirect(url_for('dashboard'))
    
    # Sayfalama parametreleri
    page = request.args.get('page', 1, type=int)
    per_page = 10
    
    # Filtreleme
    search = request.args.get('search', '', type=str)
    status = request.args.get('status', '', type=str)
    
    # Query oluştur
    query = Company.query
    
    if search:
        query = query.filter(
            db.or_(
                Company.ad.contains(search),
                Company.kod.contains(search),
                Company.aciklama.contains(search)
            )
        )
    
    if status == 'active':
        query = query.filter_by(aktif_mi=True)
    elif status == 'inactive':
        query = query.filter_by(aktif_mi=False)
    
    # Sayfalama
    companies = query.order_by(Company.kod).paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    return render_template('companies.html', companies=companies, search=search, status=status)

@company_bp.route('/create', methods=['GET', 'POST'])
@login_required
def create_company():
    """Yeni şirket oluştur"""
    if not current_user.is_admin:
        flash('Bu sayfaya erişim yetkiniz yok.', 'error')
        return redirect(url_for('company.companies'))
    
    if request.method == 'POST':
        # Form verilerini al
        ad = request.form.get('ad', '').strip()
        kod = request.form.get('kod', '').strip().upper()
        aciklama = request.form.get('aciklama', '').strip()
        adres = request.form.get('adres', '').strip()
        telefon = request.form.get('telefon', '').strip()
        email = request.form.get('email', '').strip().lower()
        website = request.form.get('website', '').strip()
        vergi_no = request.form.get('vergi_no', '').strip()
        vergi_dairesi = request.form.get('vergi_dairesi', '').strip()
        aktif_mi = 'aktif_mi' in request.form
        
        # Validasyon
        if not all([ad, kod]):
            flash('Şirket adı ve kodu zorunludur.', 'error')
            return render_template('company_form.html')
        
        if len(kod) > 10:
            flash('Şirket kodu en fazla 10 karakter olabilir.', 'error')
            return render_template('company_form.html')
        
        # Kod kontrolü (benzersiz olmalı)
        existing_company = Company.query.filter_by(kod=kod).first()
        if existing_company:
            flash('Bu şirket kodu zaten kullanılıyor.', 'error')
            return render_template('company_form.html')
        
        # Email kontrolü (varsa benzersiz olmalı)
        if email:
            existing_email = Company.query.filter_by(email=email).first()
            if existing_email:
                flash('Bu email adresi zaten kullanılıyor.', 'error')
                return render_template('company_form.html')
        
        try:
            # Yeni şirket oluştur
            company = Company(
                ad=ad,
                kod=kod,
                aciklama=aciklama,
                adres=adres,
                telefon=telefon,
                email=email,
                website=website,
                vergi_no=vergi_no,
                vergi_dairesi=vergi_dairesi,
                aktif_mi=aktif_mi,
                olusturan_id=current_user.id
            )
            
            db.session.add(company)
            db.session.commit()
            
            flash(f'{company.tam_ad} şirketi başarıyla oluşturuldu.', 'success')
            return redirect(url_for('company.companies'))
            
        except Exception as e:
            db.session.rollback()
            flash('Şirket oluşturulurken bir hata oluştu.', 'error')
    
    return render_template('company_form.html')

@company_bp.route('/<int:company_id>')
@login_required
def company_detail(company_id):
    """Şirket detayı"""
    if not current_user.is_admin:
        flash('Bu sayfaya erişim yetkiniz yok.', 'error')
        return redirect(url_for('company.companies'))
    
    company = Company.query.get_or_404(company_id)
    
    # Şirket kullanıcıları
    users = company.users.order_by(User.ad, User.soyad).all()
    
    # Şirket eğitimleri
    trainings = company.trainings.order_by(db.desc(db.text('olusturulma_tarihi'))).limit(10).all()
    
    return render_template('company_detail.html', company=company, users=users, trainings=trainings)

@company_bp.route('/<int:company_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_company(company_id):
    """Şirket düzenle"""
    if not current_user.is_admin:
        flash('Bu sayfaya erişim yetkiniz yok.', 'error')
        return redirect(url_for('company.companies'))
    
    company = Company.query.get_or_404(company_id)
    
    if request.method == 'POST':
        # Form verilerini al
        ad = request.form.get('ad', '').strip()
        kod = request.form.get('kod', '').strip().upper()
        aciklama = request.form.get('aciklama', '').strip()
        adres = request.form.get('adres', '').strip()
        telefon = request.form.get('telefon', '').strip()
        email = request.form.get('email', '').strip().lower()
        website = request.form.get('website', '').strip()
        vergi_no = request.form.get('vergi_no', '').strip()
        vergi_dairesi = request.form.get('vergi_dairesi', '').strip()
        aktif_mi = 'aktif_mi' in request.form
        
        # Validasyon
        if not all([ad, kod]):
            flash('Şirket adı ve kodu zorunludur.', 'error')
            return render_template('company_form.html', company=company)
        
        if len(kod) > 10:
            flash('Şirket kodu en fazla 10 karakter olabilir.', 'error')
            return render_template('company_form.html', company=company)
        
        # Kod kontrolü (başka şirkette kullanılıyor mu?)
        existing_company = Company.query.filter(Company.kod == kod, Company.id != company_id).first()
        if existing_company:
            flash('Bu şirket kodu başka bir şirket tarafından kullanılıyor.', 'error')
            return render_template('company_form.html', company=company)
        
        # Email kontrolü (varsa başka şirkette kullanılıyor mu?)
        if email:
            existing_email = Company.query.filter(Company.email == email, Company.id != company_id).first()
            if existing_email:
                flash('Bu email adresi başka bir şirket tarafından kullanılıyor.', 'error')
                return render_template('company_form.html', company=company)
        
        try:
            # Şirket bilgilerini güncelle
            company.ad = ad
            company.kod = kod
            company.aciklama = aciklama
            company.adres = adres
            company.telefon = telefon
            company.email = email
            company.website = website
            company.vergi_no = vergi_no
            company.vergi_dairesi = vergi_dairesi
            company.aktif_mi = aktif_mi
            
            db.session.commit()
            
            flash(f'{company.tam_ad} şirketi başarıyla güncellendi.', 'success')
            return redirect(url_for('company.companies'))
            
        except Exception as e:
            db.session.rollback()
            flash('Şirket güncellenirken bir hata oluştu.', 'error')
    
    return render_template('company_form.html', company=company)

@company_bp.route('/<int:company_id>/delete', methods=['POST'])
@login_required
def delete_company(company_id):
    """Şirket sil"""
    if not current_user.is_admin:
        return jsonify({'success': False, 'message': 'Yetkiniz yok'}), 403
    
    company = Company.query.get_or_404(company_id)
    
    # Şirketin kullanıcısı var mı kontrol et
    if not company.can_be_deleted():
        return jsonify({
            'success': False, 
            'message': 'Bu şirkette kullanıcılar bulunduğu için silinemez. Önce kullanıcıları başka şirkete taşıyın.'
        }), 400
    
    try:
        company_name = company.tam_ad
        db.session.delete(company)
        db.session.commit()
        
        return jsonify({
            'success': True, 
            'message': f'{company_name} şirketi başarıyla silindi.'
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False, 
            'message': 'Şirket silinirken bir hata oluştu.'
        }), 500

@company_bp.route('/<int:company_id>/toggle-status', methods=['POST'])
@login_required
def toggle_company_status(company_id):
    """Şirket durumunu değiştir"""
    if not current_user.is_admin:
        return jsonify({'success': False, 'message': 'Yetkiniz yok'}), 403
    
    company = Company.query.get_or_404(company_id)
    
    try:
        company.aktif_mi = not company.aktif_mi
        db.session.commit()
        
        status = 'aktif' if company.aktif_mi else 'pasif'
        return jsonify({
            'success': True, 
            'message': f'{company.tam_ad} şirketi {status} duruma getirildi.',
            'new_status': company.aktif_mi
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False, 
            'message': 'Şirket durumu değiştirilirken bir hata oluştu.'
        }), 500

@company_bp.route('/api/companies')
@login_required
def api_companies():
    """API: Aktif şirketleri getir"""
    companies = Company.get_active_companies()
    return jsonify({
        'success': True,
        'companies': [{'id': c.id, 'kod': c.kod, 'ad': c.ad, 'tam_ad': c.tam_ad} for c in companies]
    })
