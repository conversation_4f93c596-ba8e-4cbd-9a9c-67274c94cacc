from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_login import LoginManager
from flask_mail import Mail
from config import config

# Flask eklentilerini başlat
db = SQLAlchemy()
login_manager = LoginManager()
mail = Mail()

def create_app(config_name='default'):
    """Flask uygulaması factory fonksiyonu"""
    app = Flask(__name__)
    
    # Konfigürasyonu yükle
    app.config.from_object(config[config_name])

    # UTF-8 encoding ayarları
    app.config['JSON_AS_ASCII'] = False
    app.config['JSONIFY_PRETTYPRINT_REGULAR'] = True

    # Eklentileri başlat
    db.init_app(app)
    login_manager.init_app(app)
    mail.init_app(app)
    
    # Login manager ayarları
    login_manager.login_view = 'auth.login'
    login_manager.login_message = 'Bu sayfaya erişmek için giriş ya<PERSON>al<PERSON>sınız.'
    login_manager.login_message_category = 'info'
    
    # User loader fonksiyonu
    from app.models.user import User
    
    @login_manager.user_loader
    def load_user(user_id):
        return User.query.get(int(user_id))
    
    # Blueprint'leri kaydet
    from app.controllers.auth import auth_bp
    from app.controllers.company import company_bp
    from app.controllers.training import training_bp
    from app.controllers.quiz import quiz_bp
    from app.controllers.api import api_bp
    from app.controllers.material import material_bp

    app.register_blueprint(auth_bp, url_prefix='/auth')
    app.register_blueprint(company_bp, url_prefix='/company')
    app.register_blueprint(training_bp, url_prefix='/training')
    app.register_blueprint(quiz_bp, url_prefix='/quiz')
    app.register_blueprint(api_bp, url_prefix='/api')
    app.register_blueprint(material_bp, url_prefix='/material')
    
    # Ana sayfa route'u
    from flask import render_template, redirect, url_for
    from flask_login import login_required, current_user

    @app.route('/')
    def index():
        if current_user.is_authenticated:
            return redirect(url_for('dashboard'))
        return redirect(url_for('auth.login'))

    @app.route('/dashboard')
    @login_required
    def dashboard():
        # Dashboard için gerekli verileri hazırla
        dashboard_data = {}

        if current_user.can_manage_trainings():
            # Admin/Manager için istatistikler
            from app.models.training import Training, Enrollment
            from app.models.quiz import Quiz, QuizAttempt
            from app.models.user import User
            from app.models.company import Company
            from datetime import datetime, timedelta
            from sqlalchemy import func, extract

            dashboard_data.update({
                'training_count': Training.query.filter_by(aktif_mi=True).count(),
                'participant_count': User.query.filter_by(rol='participant', aktif_mi=True).count(),
                'quiz_count': Quiz.query.filter_by(aktif_mi=True).count(),
                'pending_confirmations': Enrollment.query.filter_by(katilim_teyidi_durumu='beklemede').count()
            })

            # Grafik verileri
            # 1. Son 6 ayın eğitim trendleri
            six_months_ago = datetime.utcnow() - timedelta(days=180)
            monthly_trainings = db.session.query(
                extract('month', Training.olusturulma_tarihi).label('month'),
                func.count(Training.id).label('count')
            ).filter(
                Training.olusturulma_tarihi >= six_months_ago,
                Training.aktif_mi == True
            ).group_by(extract('month', Training.olusturulma_tarihi)).all()

            # 2. Katılım durumu istatistikleri
            participation_stats = db.session.query(
                Enrollment.katilim_teyidi_durumu,
                func.count(Enrollment.id).label('count')
            ).filter(Enrollment.aktif_mi == True).group_by(Enrollment.katilim_teyidi_durumu).all()

            # 3. Quiz başarı oranları (eğitim türüne göre)
            quiz_success = db.session.query(
                Training.ad,
                func.avg(QuizAttempt.skor).label('avg_score')
            ).join(Quiz, Training.id == Quiz.training_id)\
             .join(QuizAttempt, Quiz.id == QuizAttempt.quiz_id)\
             .filter(QuizAttempt.tamamlandi_mi == True)\
             .group_by(Training.ad).limit(5).all()

            # 4. Şirket bazlı katılım
            company_participation = db.session.query(
                Company.ad,
                func.count(Enrollment.id).label('count')
            ).join(User, Company.id == User.company_id)\
             .join(Enrollment, User.id == Enrollment.user_id)\
             .filter(Enrollment.aktif_mi == True)\
             .group_by(Company.ad).order_by(func.count(Enrollment.id).desc()).limit(5).all()

            dashboard_data.update({
                'monthly_trainings': monthly_trainings,
                'participation_stats': participation_stats,
                'quiz_success': quiz_success,
                'company_participation': company_participation
            })
        else:
            # Katılımcı için istatistikler
            dashboard_data.update({
                'my_trainings_count': current_user.enrollments.filter_by(aktif_mi=True).count(),
                'completed_quizzes': current_user.quiz_attempts.filter_by(tamamlandi_mi=True).count(),
                'average_score': 0  # Hesaplanacak
            })

            # Ortalama skoru hesapla
            attempts = current_user.quiz_attempts.filter_by(tamamlandi_mi=True).all()
            if attempts:
                dashboard_data['average_score'] = round(sum(a.skor for a in attempts) / len(attempts), 1)

        # Yaklaşan eğitimler
        from datetime import datetime, timedelta
        upcoming_trainings = Training.query.filter(
            Training.baslangic_tarihi > datetime.utcnow(),
            Training.baslangic_tarihi <= datetime.utcnow() + timedelta(days=30),
            Training.aktif_mi == True
        ).order_by(Training.baslangic_tarihi).limit(5).all()

        dashboard_data['upcoming_trainings'] = upcoming_trainings

        return render_template('dashboard.html', **dashboard_data)
    
    # Veritabanı tablolarını oluştur
    with app.app_context():
        db.create_all()
    
    return app
