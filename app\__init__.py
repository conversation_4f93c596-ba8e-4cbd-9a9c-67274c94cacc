from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_login import LoginManager
from flask_mail import Mail
from config import config

# Flask eklentilerini başlat
db = SQLAlchemy()
login_manager = LoginManager()
mail = Mail()

def create_app(config_name='default'):
    """Flask uygulaması factory fonksiyonu"""
    app = Flask(__name__)
    
    # Konfigürasyonu yükle
    app.config.from_object(config[config_name])
    
    # Eklentileri başlat
    db.init_app(app)
    login_manager.init_app(app)
    mail.init_app(app)
    
    # Login manager ayarları
    login_manager.login_view = 'auth.login'
    login_manager.login_message = 'Bu sayfaya erişmek için giriş yapmalısınız.'
    login_manager.login_message_category = 'info'
    
    # User loader fonksiyonu
    from app.models.user import User
    
    @login_manager.user_loader
    def load_user(user_id):
        return User.query.get(int(user_id))
    
    # Blueprint'leri kaydet
    from app.controllers.auth import auth_bp
    from app.controllers.training import training_bp
    from app.controllers.quiz import quiz_bp
    from app.controllers.api import api_bp
    
    app.register_blueprint(auth_bp, url_prefix='/auth')
    app.register_blueprint(training_bp, url_prefix='/training')
    app.register_blueprint(quiz_bp, url_prefix='/quiz')
    app.register_blueprint(api_bp, url_prefix='/api')
    
    # Ana sayfa route'u
    from flask import render_template, redirect, url_for
    from flask_login import login_required, current_user

    @app.route('/')
    def index():
        if current_user.is_authenticated:
            return redirect(url_for('dashboard'))
        return redirect(url_for('auth.login'))

    @app.route('/dashboard')
    @login_required
    def dashboard():
        # Dashboard için gerekli verileri hazırla
        dashboard_data = {}

        if current_user.can_manage_trainings():
            # Admin/Manager için istatistikler
            from app.models.training import Training, Enrollment
            from app.models.quiz import Quiz
            from app.models.user import User

            dashboard_data.update({
                'training_count': Training.query.filter_by(aktif_mi=True).count(),
                'participant_count': User.query.filter_by(rol='participant', aktif_mi=True).count(),
                'quiz_count': Quiz.query.filter_by(aktif_mi=True).count(),
                'pending_confirmations': Enrollment.query.filter_by(katilim_teyidi_durumu='beklemede').count()
            })
        else:
            # Katılımcı için istatistikler
            dashboard_data.update({
                'my_trainings_count': current_user.enrollments.filter_by(aktif_mi=True).count(),
                'completed_quizzes': current_user.quiz_attempts.filter_by(tamamlandi_mi=True).count(),
                'average_score': 0  # Hesaplanacak
            })

            # Ortalama skoru hesapla
            attempts = current_user.quiz_attempts.filter_by(tamamlandi_mi=True).all()
            if attempts:
                dashboard_data['average_score'] = round(sum(a.skor for a in attempts) / len(attempts), 1)

        # Yaklaşan eğitimler
        from datetime import datetime, timedelta
        upcoming_trainings = Training.query.filter(
            Training.baslangic_tarihi > datetime.utcnow(),
            Training.baslangic_tarihi <= datetime.utcnow() + timedelta(days=30),
            Training.aktif_mi == True
        ).order_by(Training.baslangic_tarihi).limit(5).all()

        dashboard_data['upcoming_trainings'] = upcoming_trainings

        return render_template('dashboard.html', **dashboard_data)
    
    # Veritabanı tablolarını oluştur
    with app.app_context():
        db.create_all()
    
    return app
